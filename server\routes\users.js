const express = require('express');
const router = express.Router();
const { authenticate, requireAdmin, requireManagerOrAdmin } = require('../middleware/auth');
const { body, query, validationResult } = require('express-validator');
const { models } = require('../models/database');

/**
 * @route   GET /api/users
 * @desc    Get all users (admin/manager only)
 * @access  Private (Admin/Manager)
 */
router.get('/', 
  authenticate, 
  requireManagerOrAdmin,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('search')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search term must not be empty'),
    query('role')
      .optional()
      .isIn(['customer', 'admin', 'manager'])
      .withMessage('Invalid role'),
    query('status')
      .optional()
      .isIn(['active', 'inactive'])
      .withMessage('Status must be active or inactive')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const offset = (page - 1) * limit;
      const search = req.query.search;
      const role = req.query.role;
      const status = req.query.status;

      // Build where clause
      const where = {};
      
      if (search) {
        where[models.Sequelize.Op.or] = [
          { firstName: { [models.Sequelize.Op.like]: `%${search}%` } },
          { lastName: { [models.Sequelize.Op.like]: `%${search}%` } },
          { email: { [models.Sequelize.Op.like]: `%${search}%` } }
        ];
      }
      
      if (role) {
        where.role = role;
      }
      
      if (status) {
        where.isActive = status === 'active';
      }

      const { count, rows: users } = await models.User.findAndCountAll({
        where,
        attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] },
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });

      const totalPages = Math.ceil(count / limit);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            currentPage: page,
            totalPages,
            totalUsers: count,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        },
        message: 'Users retrieved successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID (admin/manager only)
 * @access  Private (Admin/Manager)
 */
router.get('/:id', 
  authenticate, 
  requireManagerOrAdmin,
  async (req, res) => {
    try {
      const userId = req.params.id;

      const user = await models.User.findByPk(userId, {
        attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] },
        include: [
          {
            model: models.Address,
            as: 'addresses'
          },
          {
            model: models.Order,
            as: 'orders',
            limit: 5,
            order: [['createdAt', 'DESC']]
          }
        ]
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        data: { user },
        message: 'User retrieved successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user (admin only)
 * @access  Private (Admin)
 */
router.put('/:id', 
  authenticate, 
  requireAdmin,
  [
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('role')
      .optional()
      .isIn(['customer', 'admin', 'manager'])
      .withMessage('Invalid role'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('isActive must be a boolean'),
    body('isEmailVerified')
      .optional()
      .isBoolean()
      .withMessage('isEmailVerified must be a boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const userId = req.params.id;
      const updateData = req.body;

      // Prevent users from updating their own role or status
      if (req.user.id === parseInt(userId)) {
        delete updateData.role;
        delete updateData.isActive;
      }

      const user = await models.User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check for email uniqueness if email is being updated
      if (updateData.email && updateData.email !== user.email) {
        const existingUser = await models.User.findOne({ where: { email: updateData.email } });
        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: 'Email already exists'
          });
        }
      }

      await user.update(updateData);

      // Return updated user without sensitive data
      const updatedUser = await models.User.findByPk(userId, {
        attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
      });

      res.json({
        success: true,
        data: { user: updatedUser },
        message: 'User updated successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete/deactivate user (admin only)
 * @access  Private (Admin)
 */
router.delete('/:id', 
  authenticate, 
  requireAdmin,
  async (req, res) => {
    try {
      const userId = req.params.id;

      // Prevent admin from deleting themselves
      if (req.user.id === parseInt(userId)) {
        return res.status(400).json({
          success: false,
          message: 'You cannot delete your own account'
        });
      }

      const user = await models.User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Soft delete by deactivating
      await user.update({ isActive: false });

      res.json({
        success: true,
        message: 'User deactivated successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   GET /api/users/stats/overview
 * @desc    Get user statistics (admin/manager only)
 * @access  Private (Admin/Manager)
 */
router.get('/stats/overview', 
  authenticate, 
  requireManagerOrAdmin,
  async (req, res) => {
    try {
      const totalUsers = await models.User.count();
      const activeUsers = await models.User.count({ where: { isActive: true } });
      const verifiedUsers = await models.User.count({ where: { isEmailVerified: true } });
      const adminUsers = await models.User.count({ where: { role: 'admin' } });
      const customerUsers = await models.User.count({ where: { role: 'customer' } });

      // Users registered in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentUsers = await models.User.count({
        where: {
          createdAt: {
            [models.Sequelize.Op.gte]: thirtyDaysAgo
          }
        }
      });

      res.json({
        success: true,
        data: {
          totalUsers,
          activeUsers,
          verifiedUsers,
          adminUsers,
          customerUsers,
          recentUsers,
          inactiveUsers: totalUsers - activeUsers,
          unverifiedUsers: totalUsers - verifiedUsers
        },
        message: 'User statistics retrieved successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

module.exports = router;
