import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { logout } from '../../store/slices/authSlice';
import { fetchCart } from '../../store/slices/cartSlice';
import { ShoppingCartIcon, ShoppingBagIcon, UserIcon, MagnifyingGlassIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);
  const { itemCount: cartItemsCount } = useAppSelector((state) => state.cart);

  // Fetch cart when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchCart());
    }
  }, [isAuthenticated, dispatch]);

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/shop?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  const navigationItems = [
    { name: 'Home', href: '/' },
    { name: 'Shop', href: '/shop' },
    { name: 'Best Sellers', href: '/shop?bestSeller=true' },
    { name: 'Shop Finder', href: '/shop-finder' },
    { name: 'About', href: '/about' },
    { name: 'Social Media', href: '/social-media' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Contact', href: '/contact' },
  ];

  const cannabinoidCategories = [
    { name: 'THC-A', href: '/shop?cannabinoid=THC-A' },
    { name: 'CBD', href: '/shop?cannabinoid=CBD' },
    { name: 'Delta-8', href: '/shop?cannabinoid=Delta-8' },
    { name: 'Delta-9', href: '/shop?cannabinoid=Delta-9' },
  ];

  const productCategories = [
    { name: 'Flowers', href: '/shop?category=flowers', description: 'Premium cannabis flowers' },
    { name: 'Edibles', href: '/shop?category=edibles', description: 'Delicious cannabis edibles' },
    { name: 'Pre-Rolls', href: '/shop?category=pre-rolls', description: 'Ready-to-smoke pre-rolls' },
    { name: 'Concentrates', href: '/shop?category=concentrates', description: 'High-potency concentrates' },
    { name: 'Vapes', href: '/shop?category=vapes', description: 'Convenient vape products' },
    { name: 'Chocolates', href: '/shop?category=chocolates', description: 'Premium cannabis chocolates' },
  ];

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50 mobile-stable">
      {/* Top Banner */}
      <div className="bg-primary-600 text-white text-center py-2 text-sm no-horizontal-scroll">
        <div className="container mx-auto px-4">
          <span className="animate-pulse">
            FLASH SALE LIVE NOW! ENJOY FREE SHIPPING ON ORDERS OVER $100! GET 10% OFF ON ORDERS ABOVE $200.
          </span>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 no-horizontal-scroll">
        <div className="flex items-center justify-between h-20 sm:h-24">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <img
              src="/Nirvana_logo.png"
              alt="Nirvana Organics Logo"
              className="h-16 sm:h-20 w-auto"
              onError={(e) => {
                // Fallback to text if logo fails to load
                e.currentTarget.style.display = 'none';
              }}
            />
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-4 pr-12 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-stable smooth-transition no-zoom"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600"
                >
                  <MagnifyingGlassIcon className="h-5 w-5" />
                </button>
              </div>
            </form>
          </div>

          {/* Right Side Icons */}
          <div className="flex items-center space-x-4">
            {/* User Menu */}
            <div className="relative group">
              <button className="flex items-center space-x-1 text-gray-700 hover:text-primary-600">
                <UserIcon className="h-6 w-6" />
                <span className="hidden sm:block">
                  {isAuthenticated ? user?.firstName : 'Account'}
                </span>
              </button>
              
              {/* Dropdown Menu */}
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  {isAuthenticated ? (
                    <>
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        My Profile
                      </Link>
                      <Link
                        to="/orders"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        My Orders
                      </Link>
                      {user?.role === 'admin' && (
                        <Link
                          to="/admin"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          Admin Dashboard
                        </Link>
                      )}
                      <button
                        onClick={handleLogout}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Logout
                      </button>
                    </>
                  ) : (
                    <>
                      <Link
                        to="/login"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Login
                      </Link>
                      <Link
                        to="/register"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Register
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Cart */}
            <Link
              to="/cart"
              className="relative flex items-center text-gray-700 hover:text-primary-600"
            >
              <ShoppingCartIcon className="h-6 w-6" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
              <span className="hidden sm:block ml-1">${0.00}</span>
            </Link>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden text-gray-700 hover:text-primary-600 touch-target"
              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            >
              {isMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Navigation - Desktop */}
        <nav className="hidden md:flex items-center justify-between py-4 border-t border-gray-200">
          <div className="flex items-center space-x-8">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
              >
                {item.name}
              </Link>
            ))}
          </div>

          <div className="flex items-center space-x-6">
            <span className="text-sm font-medium text-gray-600">Cannabinoids:</span>
            {cannabinoidCategories.map((category) => (
              <Link
                key={category.name}
                to={category.href}
                className="text-sm text-gray-600 hover:text-primary-600 transition-colors duration-200"
              >
                {category.name}
              </Link>
            ))}
          </div>
        </nav>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200 shadow-lg no-overscroll">
          <div className="px-4 py-4 space-y-6 max-h-screen overflow-y-auto smooth-scroll">
            {/* Mobile Search - More Prominent */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <form onSubmit={handleSearch}>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search cannabis products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-4 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-base no-zoom"
                  />
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600"
                  >
                    <MagnifyingGlassIcon className="h-5 w-5" />
                  </button>
                </div>
              </form>
            </div>

            {/* Mobile Navigation */}
            <div className="space-y-1">
              <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Navigation</h3>
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="flex items-center py-3 px-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>

            {/* Mobile Product Categories */}
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Shop by Category</h3>
              <div className="grid grid-cols-2 gap-2">
                {productCategories.map((category) => (
                  <Link
                    key={category.name}
                    to={category.href}
                    className="block p-3 bg-gray-50 rounded-lg hover:bg-primary-50 hover:text-primary-600 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <div className="font-medium text-sm">{category.name}</div>
                    <div className="text-xs text-gray-500 mt-1">{category.description}</div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Mobile Cannabinoids */}
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Shop by Cannabinoid</h3>
              <div className="grid grid-cols-2 gap-2">
                {cannabinoidCategories.map((category) => (
                  <Link
                    key={category.name}
                    to={category.href}
                    className="block py-3 px-4 bg-gray-50 rounded-lg text-center font-medium text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {category.name}
                  </Link>
                ))}
              </div>
            </div>

            {/* Mobile Account Menu */}
            {isAuthenticated ? (
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">My Account</h3>
                <div className="space-y-1">
                  <Link
                    to="/profile"
                    className="flex items-center py-3 px-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <UserIcon className="h-5 w-5 mr-3" />
                    Profile Settings
                  </Link>
                  <Link
                    to="/orders"
                    className="flex items-center py-3 px-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <ShoppingBagIcon className="h-5 w-5 mr-3" />
                    My Orders
                  </Link>
                  <Link
                    to="/cart"
                    className="flex items-center py-3 px-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <ShoppingCartIcon className="h-5 w-5 mr-3" />
                    Shopping Cart
                    {cartItemsCount > 0 && (
                      <span className="ml-auto bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {cartItemsCount}
                      </span>
                    )}
                  </Link>
                </div>
              </div>
            ) : (
              <div className="border-t border-gray-200 pt-4">
                <div className="space-y-2">
                  <Link
                    to="/login"
                    className="block w-full py-3 px-4 bg-primary-600 text-white text-center font-medium rounded-lg hover:bg-primary-700 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    to="/register"
                    className="block w-full py-3 px-4 border border-primary-600 text-primary-600 text-center font-medium rounded-lg hover:bg-primary-50 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Create Account
                  </Link>
                </div>
              </div>
            )}

            {/* Mobile Contact Info */}
            <div className="border-t border-gray-200 pt-4 pb-2">
              <div className="text-center text-sm text-gray-600">
                <p className="mb-2">Need help? Contact us:</p>
                <a href="tel:******-123-4567" className="text-primary-600 hover:text-primary-700 font-medium">
                  (*************
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
