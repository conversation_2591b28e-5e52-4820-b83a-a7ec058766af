{"name": "nirvana-organics-backend", "version": "1.0.0", "description": "Nirvana Organics E-commerce Backend API", "main": "server/index.js", "scripts": {"start": "node server/index.js", "start:prod": "NODE_ENV=production node server/index.js", "dev": "nodemon server/index.js", "test": "jest", "setup:database": "node scripts/setup-database.js", "create:tables": "node scripts/create-database-tables.js", "seed:database": "node scripts/seed-database.js", "test:database": "node scripts/test-database-connection.js", "migrate": "node scripts/run-migrations.js", "status": "node scripts/system-status.js"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "helmet": "^8.0.0", "dotenv": "^16.4.7", "mysql2": "^3.12.0", "sequelize": "^6.37.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.2", "express-slow-down": "^2.1.0", "express-validator": "^7.2.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.18", "socket.io": "^4.8.1", "stripe": "^17.5.0", "square": "^39.0.0", "web-push": "^3.6.7", "handlebars": "^4.7.8", "xss": "^1.0.15", "yup": "^1.6.0", "axios": "^1.7.9", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "xml2js": "^0.6.2", "exceljs": "^4.4.0", "json2csv": "^6.0.0-alpha.2"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^29.7.0", "supertest": "^7.1.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ecommerce", "api", "backend", "nodejs", "express", "mysql", "sequelize"], "author": "Nirvana Organics", "license": "MIT"}