const { Category, Product } = require('../models');
const { validationResult } = require('express-validator');

// Get all categories
const getCategories = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      parent,
      level,
      status = 'active',
      tree = false
    } = req.query;

    if (tree === 'true') {
      // Return simple tree structure without complex associations
      const categories = await Category.findAll({
        where: { isActive: true },
        order: [['sortOrder', 'ASC'], ['name', 'ASC']],
        attributes: ['id', 'name', 'slug', 'description', 'image', 'parentId', 'sortOrder', 'isActive']
      });

      // Filter root categories (no parent)
      const rootCategories = categories.filter(cat => !cat.parentId);

      return res.json({
        success: true,
        data: { categories: rootCategories }
      });
    }

    // Build filter
    const where = {};
    if (status === 'active') where.isActive = true;
    if (parent) where.parentId = parent === 'null' ? null : parent;
    if (level !== undefined) where.level = parseInt(level);

    // Calculate pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get categories (simplified without associations for now)
    const categories = await Category.findAll({
      where,
      order: [['sortOrder', 'ASC'], ['name', 'ASC']],
      offset,
      limit: parseInt(limit),
      attributes: ['id', 'name', 'slug', 'description', 'image', 'parentId', 'sortOrder', 'isActive']
    });

    // Calculate pagination info
    const total = categories.length;
    const pages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        categories,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages
        }
      }
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: error.message
    });
  }
};

// Get single category by slug
const getCategoryBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const category = await Category.findOne({
      where: { slug, status: 'active' },
      include: [
        {
          model: Category,
          as: 'parent',
          attributes: ['id', 'name', 'slug']
        }
      ]
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Get category path (breadcrumb)
    const path = await category.getPath();

    // Get child categories
    const children = await Category.findAll({
      where: { parentId: category.id, status: 'active' },
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });

    // Get products in this category
    const products = await Product.findAll({
      where: {
        categoryId: category.id,
        isActive: true
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ],
      attributes: ['id', 'name', 'slug', 'price', 'images', 'averageRating', 'reviewCount', 'isFeatured'],
      order: [['isFeatured', 'DESC'], ['salesCount', 'DESC'], ['createdAt', 'DESC']],
      limit: 12
    });

    res.json({
      success: true,
      data: {
        category,
        path,
        children,
        products
      }
    });

  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category',
      error: error.message
    });
  }
};

// Create new category (Admin only)
const createCategory = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const categoryData = req.body;

    // Generate slug from name if not provided
    if (!categoryData.slug) {
      categoryData.slug = categoryData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Check if slug already exists
    const existingCategory = await Category.findOne({ slug: categoryData.slug });
    if (existingCategory) {
      categoryData.slug = `${categoryData.slug}-${Date.now()}`;
    }

    const category = new Category(categoryData);
    await category.save();

    // Populate parent information if exists
    if (category.parent) {
      await category.populate('parent', 'name slug');
    }

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: { category }
    });

  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create category',
      error: error.message
    });
  }
};

// Update category (Admin only)
const updateCategory = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    // If name is being updated, regenerate slug
    if (updateData.name && !updateData.slug) {
      updateData.slug = updateData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    const category = await Category.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('parent', 'name slug');

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: { category }
    });

  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update category',
      error: error.message
    });
  }
};

// Delete category (Admin only)
const deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category has children
    const childrenCount = await Category.countDocuments({ parent: id });
    if (childrenCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete category with subcategories. Please delete subcategories first.'
      });
    }

    // Check if category has products
    const productCount = await Product.countDocuments({ category: id });
    if (productCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete category with products. Please move or delete products first.'
      });
    }

    const category = await Category.findByIdAndDelete(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.json({
      success: true,
      message: 'Category deleted successfully'
    });

  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete category',
      error: error.message
    });
  }
};

// Get category statistics (Admin only)
const getCategoryStats = async (req, res) => {
  try {
    const stats = await Category.aggregate([
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'category',
          as: 'products'
        }
      },
      {
        $project: {
          name: 1,
          slug: 1,
          level: 1,
          status: 1,
          productCount: { $size: '$products' },
          activeProductCount: {
            $size: {
              $filter: {
                input: '$products',
                cond: { $eq: ['$$this.status', 'active'] }
              }
            }
          }
        }
      },
      {
        $sort: { level: 1, name: 1 }
      }
    ]);

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Get category stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category statistics',
      error: error.message
    });
  }
};

// Get main navigation categories
const getNavigationCategories = async (req, res) => {
  try {
    const categories = await Category.find({ 
      level: 0, 
      status: 'active' 
    })
    .sort({ sortOrder: 1, name: 1 })
    .select('name slug image');

    // Get subcategories for each main category
    const navigationData = await Promise.all(
      categories.map(async (category) => {
        const subcategories = await Category.find({
          parent: category._id,
          status: 'active'
        })
        .sort({ sortOrder: 1, name: 1 })
        .select('name slug')
        .limit(8);

        return {
          ...category.toObject(),
          subcategories
        };
      })
    );

    res.json({
      success: true,
      data: { categories: navigationData }
    });

  } catch (error) {
    console.error('Get navigation categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch navigation categories',
      error: error.message
    });
  }
};

module.exports = {
  getCategories,
  getCategoryBySlug,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats,
  getNavigationCategories
};
