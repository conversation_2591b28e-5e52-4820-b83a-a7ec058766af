import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { addToast } from '../store/slices/uiSlice';
import AddressForm from '../components/forms/AddressForm';
import LoadingSpinner from '../components/common/LoadingSpinner';
import PasswordChangeForm from '../components/profile/PasswordChangeForm';
import OrderHistory from '../components/profile/OrderHistory';
import {
  UserIcon,
  MapPinIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
  LockClosedIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';
import SEOHead from '../components/seo/SEOHead';
import Breadcrumb from '../components/common/Breadcrumb';

interface Address {
  id?: number;
  type: 'shipping' | 'billing';
  firstName: string;
  lastName: string;
  streetAddress: string;
  streetAddress2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

const Profile: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(false);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [activeTab, setActiveTab] = useState<'profile' | 'addresses' | 'password' | 'orders'>('profile');

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/addresses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAddresses(data.data.addresses || []);
      }
    } catch (error) {
      console.error('Failed to fetch addresses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAddress = async (addressData: any) => {
    try {
      setLoading(true);
      const url = editingAddress
        ? `/api/auth/addresses/${editingAddress.id}`
        : '/api/auth/addresses';

      const method = editingAddress ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(addressData)
      });

      if (response.ok) {
        dispatch(addToast({
          type: 'success',
          title: 'Success',
          message: `Address ${editingAddress ? 'updated' : 'added'} successfully`
        }));

        setShowAddressForm(false);
        setEditingAddress(null);
        fetchAddresses();
      } else {
        throw new Error('Failed to save address');
      }
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to save address'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAddress = async (addressId: number) => {
    if (!window.confirm('Are you sure you want to delete this address?')) {
      return;
    }

    try {
      const response = await fetch(`/api/auth/addresses/${addressId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        dispatch(addToast({
          type: 'success',
          title: 'Success',
          message: 'Address deleted successfully'
        }));
        fetchAddresses();
      } else {
        throw new Error('Failed to delete address');
      }
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete address'
      }));
    }
  };

  if (loading && addresses.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <SEOHead
        title="My Account - Manage Your Cannabis Profile"
        description="Manage your Nirvana Organics account profile, shipping addresses, and preferences. Update personal information and view order history."
        keywords={['account management', 'profile settings', 'cannabis account', 'user profile', 'account preferences']}
        canonicalUrl="/profile"
        noIndex={true}
      />
      <div className="max-w-4xl mx-auto">
        {/* Breadcrumb */}
        <Breadcrumb items={[{ label: 'My Account', current: true }]} className="mb-6" />

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Account</h1>
          <p className="text-gray-600">Manage your profile and addresses</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex flex-wrap space-x-8">
            <button
              onClick={() => setActiveTab('profile')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'profile'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <UserIcon className="h-5 w-5 inline mr-2" />
              Profile Information
            </button>
            <button
              onClick={() => setActiveTab('addresses')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'addresses'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <MapPinIcon className="h-5 w-5 inline mr-2" />
              Address Book
            </button>
            <button
              onClick={() => setActiveTab('password')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'password'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <LockClosedIcon className="h-5 w-5 inline mr-2" />
              Change Password
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'orders'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <ShoppingBagIcon className="h-5 w-5 inline mr-2" />
              Order History
            </button>
          </nav>
        </div>

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Profile Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <input
                  type="text"
                  value={user?.firstName || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  type="text"
                  value={user?.lastName || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  value={user?.email || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                />
              </div>
            </div>

            <div className="mt-6">
              <p className="text-sm text-gray-500">
                To update your profile information, please contact customer support.
              </p>
            </div>
          </div>
        )}

        {/* Addresses Tab */}
        {activeTab === 'addresses' && (
          <div className="space-y-6">
            {/* Add Address Button */}
            {!showAddressForm && (
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-900">Address Book</h2>
                <button
                  onClick={() => {
                    setShowAddressForm(true);
                    setEditingAddress(null);
                  }}
                  className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Add New Address
                </button>
              </div>
            )}

            {/* Address Form */}
            {showAddressForm && (
              <AddressForm
                initialData={editingAddress || {}}
                onSubmit={handleSaveAddress}
                title={editingAddress ? 'Edit Address' : 'Add New Address'}
                submitButtonText={editingAddress ? 'Update Address' : 'Save Address'}
                loading={loading}
              />
            )}

            {/* Address List */}
            {!showAddressForm && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {addresses.map((address) => (
                  <div key={address.id} className="bg-white rounded-lg shadow-sm border p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {address.firstName} {address.lastName}
                        </h3>
                        {address.isDefault && (
                          <span className="inline-block px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full mt-1">
                            Default
                          </span>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setEditingAddress(address);
                            setShowAddressForm(true);
                          }}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDeleteAddress(address.id!)}
                          className="text-red-400 hover:text-red-600"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>

                    <div className="text-gray-600 space-y-1">
                      <p>{address.streetAddress}</p>
                      {address.streetAddress2 && <p>{address.streetAddress2}</p>}
                      <p>{address.city}, {address.state} {address.postalCode}</p>
                      <p>{address.country}</p>
                      {address.phone && <p>{address.phone}</p>}
                    </div>
                  </div>
                ))}

                {addresses.length === 0 && (
                  <div className="md:col-span-2 text-center py-12">
                    <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No addresses saved</h3>
                    <p className="text-gray-600 mb-4">Add your first address to get started</p>
                    <button
                      onClick={() => setShowAddressForm(true)}
                      className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      Add Address
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Cancel Button for Form */}
            {showAddressForm && (
              <div className="flex justify-end">
                <button
                  onClick={() => {
                    setShowAddressForm(false);
                    setEditingAddress(null);
                  }}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        )}

        {/* Password Tab */}
        {activeTab === 'password' && (
          <PasswordChangeForm />
        )}

        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <OrderHistory />
        )}
      </div>
    </div>
  );
};

export default Profile;
