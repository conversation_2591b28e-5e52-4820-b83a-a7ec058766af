#!/bin/bash

# ============================================================================
# Production Deployment Script
# Nirvana Organics E-commerce Platform
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${CYAN}🔧 $1${NC}"
}

# Configuration
ENVIRONMENT="production"
PROJECT_NAME="nirvana-organics"
BACKUP_DIR="/var/www/backups/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/var/log/nirvana-deployment.log"

echo "============================================================================"
echo "🚀 PRODUCTION DEPLOYMENT - NIRVANA ORGANICS E-COMMERCE"
echo "============================================================================"

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
   log_warning "Running as root. Consider using a dedicated deployment user."
fi

# Step 1: Pre-deployment checks
log_step "Step 1: Pre-deployment checks"

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    log_error ".env.production file not found!"
    log_info "Please create .env.production with production configuration"
    exit 1
fi

# Check if required commands exist
command -v node >/dev/null 2>&1 || { log_error "Node.js is required but not installed."; exit 1; }
command -v npm >/dev/null 2>&1 || { log_error "npm is required but not installed."; exit 1; }
command -v pm2 >/dev/null 2>&1 || { log_error "PM2 is required but not installed."; exit 1; }

log_success "Pre-deployment checks passed"

# Step 2: Create backup
log_step "Step 2: Creating backup"

mkdir -p "$BACKUP_DIR"

# Backup current deployment if it exists
if [ -d "/var/www/nirvana-backend/current" ]; then
    cp -r /var/www/nirvana-backend/current "$BACKUP_DIR/app-backup"
    log_success "Application backup created"
fi

# Backup database
if command -v mysqldump >/dev/null 2>&1; then
    log_info "Creating database backup..."
    # Note: Database credentials should be loaded from .env.production
    source .env.production
    mysqldump -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_DIR/database-backup.sql"
    log_success "Database backup created"
else
    log_warning "mysqldump not found. Skipping database backup."
fi

# Step 3: Install dependencies
log_step "Step 3: Installing dependencies"

npm ci --production --silent
log_success "Dependencies installed"

# Step 4: Environment setup
log_step "Step 4: Setting up environment"

# Copy production environment
cp .env.production .env
log_success "Production environment configured"

# Validate environment variables
if [ -f "scripts/validate-env.js" ]; then
    node scripts/validate-env.js
    if [ $? -eq 0 ]; then
        log_success "Environment validation passed"
    else
        log_error "Environment validation failed"
        exit 1
    fi
fi

# Step 5: Database operations
log_step "Step 5: Database operations"

# Test database connection
if [ -f "scripts/test-database-connection.js" ]; then
    node scripts/test-database-connection.js
    if [ $? -eq 0 ]; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed"
        exit 1
    fi
fi

# Run migrations
log_info "Running database migrations..."
npm run migrate:prod
if [ $? -eq 0 ]; then
    log_success "Database migrations completed"
else
    log_error "Database migrations failed"
    exit 1
fi

# Step 6: Build application (if needed)
log_step "Step 6: Building application"

if [ -f "vite.config.ts" ]; then
    log_info "Building frontend..."
    npm run build
    log_success "Frontend build completed"
fi

# Step 7: Security setup
log_step "Step 7: Security setup"

# Run security setup script
if [ -f "scripts/setup-security.js" ]; then
    node scripts/setup-security.js
    log_success "Security setup completed"
fi

# Set proper file permissions
chmod -R 755 .
chmod -R 777 uploads/ 2>/dev/null || true
chmod -R 777 logs/ 2>/dev/null || true

# Step 8: Start/Restart services
log_step "Step 8: Starting services"

# Stop existing PM2 processes
pm2 stop all 2>/dev/null || true

# Start with PM2
pm2 start ecosystem.config.js --env production
if [ $? -eq 0 ]; then
    log_success "Application started with PM2"
else
    log_error "Failed to start application with PM2"
    exit 1
fi

# Save PM2 configuration
pm2 save

# Step 9: Health checks
log_step "Step 9: Health checks"

# Wait for application to start
sleep 10

# Check if application is running
if pm2 list | grep -q "online"; then
    log_success "Application is running"
else
    log_error "Application failed to start"
    pm2 logs --lines 20
    exit 1
fi

# Test API endpoint
if command -v curl >/dev/null 2>&1; then
    log_info "Testing API endpoint..."
    if curl -f -s http://localhost:5000/health >/dev/null; then
        log_success "API health check passed"
    else
        log_warning "API health check failed - check application logs"
    fi
fi

# Step 10: Cleanup
log_step "Step 10: Cleanup"

# Remove old backups (keep last 5)
find /var/www/backups -maxdepth 1 -type d -name "20*" | sort -r | tail -n +6 | xargs rm -rf 2>/dev/null || true

log_success "Cleanup completed"

# Final status
echo ""
echo "============================================================================"
log_success "🎉 PRODUCTION DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "============================================================================"

log_info "Deployment Summary:"
echo "• Environment: $ENVIRONMENT"
echo "• Backup Location: $BACKUP_DIR"
echo "• Application Status: $(pm2 list | grep nirvana | awk '{print $10}' | head -1)"
echo "• Log Location: $LOG_FILE"

echo ""
log_info "Next Steps:"
echo "1. Verify SSL certificates are properly configured"
echo "2. Test all application functionality"
echo "3. Monitor application logs: pm2 logs"
echo "4. Set up monitoring and alerting"

echo ""
log_warning "Important Reminders:"
echo "• Monitor application performance and logs"
echo "• Set up automated backups"
echo "• Configure log rotation"
echo "• Test disaster recovery procedures"

# Log deployment to file
echo "$(date): Production deployment completed successfully" >> "$LOG_FILE"
