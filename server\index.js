require('dotenv').config();

// Validate critical environment variables
const validateEnvironment = () => {
  const requiredVars = [
    'NODE_ENV',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'EMAIL_HOST',
    'EMAIL_USER',
    'EMAIL_PASS'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars.join(', '));
    console.error('Please check your .env file and ensure all required variables are set.');
    process.exit(1);
  }

  // Validate production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    const productionVars = ['FRONTEND_URL', 'API_BASE_URL'];
    const missingProdVars = productionVars.filter(varName => !process.env[varName]);

    if (missingProdVars.length > 0) {
      console.error('❌ Missing production environment variables:', missingProdVars.join(', '));
      process.exit(1);
    }

    // Check HTTPS enforcement
    if (process.env.FORCE_HTTPS !== 'true') {
      console.warn('⚠️ HTTPS is not enforced in production environment');
    }
  }

  console.log('✅ Environment validation passed');
};

// Run environment validation
validateEnvironment();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const { connectDB } = require('./config/database');
const {
  generalLimiter,
  authLimiter,
  smartAuthLimiter,
  apiLimiter,
  speedLimiter,
  sanitizeInput,
  securityHeaders,
  requestSizeLimiter
} = require('./middleware/security');
const performanceMonitor = require('./utils/performanceMonitor');
const { globalErrorHandler, handleNotFound } = require('./middleware/errorHandler');
const { responseMiddleware } = require('./utils/apiResponse');
const http = require('http');
const realTimeService = require('./services/realTimeService');

// Import routes
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const categoryRoutes = require('./routes/categories');
const cartRoutes = require('./routes/cart');
const orderRoutes = require('./routes/orders');
const webhookRoutes = require('./routes/webhooks');
const adminRoutes = require('./routes/admin');
const rbacRoutes = require('./routes/rbac');
const notificationRoutes = require('./routes/notifications');
const uploadRoutes = require('./routes/upload');
const guestRoutes = require('./routes/guest');
const contactRoutes = require('./routes/contact');
const shopFinderRoutes = require('./routes/shopFinder');
const adminShopFinderRoutes = require('./routes/adminShopFinder');
const orderVerificationRoutes = require('./routes/orderVerification');
const trackingRoutes = require('./routes/tracking');
const socialMediaRoutes = require('./routes/socialMedia');
const adminSocialMediaRoutes = require('./routes/adminSocialMedia');
const discountRoutes = require('./routes/discount');
const adminDiscountRoutes = require('./routes/adminDiscount');
const campaignRoutes = require('./routes/campaign');
const emailManagementRoutes = require('./routes/emailManagement');

// Initialize Express app
const app = express();

// Initialize Passport for social authentication
const passport = require('./config/passport');
const session = require('express-session');

// Session configuration for Passport
app.use(session({
  secret: process.env.SESSION_SECRET || process.env.JWT_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Initialize Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Trust proxy for HTTPS (important for Hostinger)
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', 1);
}

// Connect to database
connectDB();

// Force HTTPS in production (must be before other middleware)
if (process.env.NODE_ENV === 'production' && process.env.FORCE_HTTPS === 'true') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      return res.redirect(301, `https://${req.header('host')}${req.url}`);
    }
    next();
  });
}

// Security middleware
app.use(securityHeaders);
app.use(helmet({
  contentSecurityPolicy: false, // We handle CSP in securityHeaders
  crossOriginEmbedderPolicy: false
}));

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Request size limiting
app.use(requestSizeLimiter('10mb'));

// General rate limiting
app.use(generalLimiter);

// Speed limiting for repeated requests
app.use(speedLimiter);

// CORS configuration with support for multiple origins
const corsOptions = {
  origin: process.env.CORS_ORIGIN ?
    process.env.CORS_ORIGIN.split(',').map(origin => origin.trim()) :
    'http://localhost:5173',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Performance monitoring middleware
app.use(performanceMonitor.trackRequest());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization
app.use(sanitizeInput);

// Add response helpers
app.use(responseMiddleware);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// API health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// API routes with specific rate limiting
app.use('/api/auth', smartAuthLimiter, authRoutes);
app.use('/api/products', apiLimiter, productRoutes);
app.use('/api/categories', apiLimiter, categoryRoutes);
app.use('/api/cart', apiLimiter, cartRoutes);
app.use('/api/orders', apiLimiter, orderRoutes);
app.use('/api/admin', apiLimiter, adminRoutes);
app.use('/api/admin/rbac', apiLimiter, rbacRoutes); // RBAC routes under admin
app.use('/api/auth', smartAuthLimiter, rbacRoutes); // Public RBAC routes under auth
app.use('/api/notifications', apiLimiter, notificationRoutes);
app.use('/api/upload', apiLimiter, uploadRoutes);
app.use('/api/guest', apiLimiter, guestRoutes);
app.use('/api/contact', contactRoutes); // Contact routes have their own rate limiting
app.use('/api/shop-finder', apiLimiter, shopFinderRoutes);
app.use('/api/admin/shop-finder', apiLimiter, adminShopFinderRoutes);
app.use('/api/social-media', apiLimiter, socialMediaRoutes);
app.use('/api/admin/social-media', apiLimiter, adminSocialMediaRoutes);
app.use('/api', apiLimiter, discountRoutes);
app.use('/api/admin', apiLimiter, adminDiscountRoutes);
app.use('/api', apiLimiter, campaignRoutes);
app.use('/api/admin/email-management', apiLimiter, emailManagementRoutes);
app.use('/api/admin/order-verification', apiLimiter, orderVerificationRoutes);
app.use('/api/admin/tracking', apiLimiter, trackingRoutes);
app.use('/api/tracking', apiLimiter, trackingRoutes);
app.use('/api/webhooks', webhookRoutes); // No rate limiting for webhooks

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')));

// Serve public assets (logos, favicons, etc.)
app.use('/assets', express.static(path.join(__dirname, '../public')));

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static('dist'));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, '../dist', 'index.html'));
  });
}

// Global error handler
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  
  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(e => e.message);
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors
    });
  }
  
  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json({
      success: false,
      message: `${field} already exists`
    });
  }
  
  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    });
  }
  
  // Default error
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Handle unhandled routes
app.all('*', handleNotFound);

// Global error handling middleware
app.use(globalErrorHandler);

// Create HTTP server and initialize WebSocket
const PORT = process.env.PORT || 5000;
const server = http.createServer(app);

// Initialize real-time service with WebSocket
realTimeService.initialize(server);

// Start server
server.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
  console.log(`Server accessible at: http://localhost:${PORT}`);
  console.log(`WebSocket server initialized for real-time order monitoring`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.error('Unhandled Promise Rejection:', err);
  console.error('Promise:', promise);
  // Close server & exit process
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  console.error('Stack:', err.stack);
  // Close server & exit process
  process.exit(1);
});

module.exports = app;
