import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [react()],
    css: {
      postcss: './postcss.config.cjs',
    },
    define: {
      // Make environment variables available to the app
      __APP_ENV__: JSON.stringify(env.APP_ENV),
      __API_URL__: JSON.stringify(env.VITE_API_URL || 'https://shopnirvanaorganics.com/api'),
    },
    build: {
      // Browser compatibility target
      target: ['es2020', 'chrome120', 'firefox115', 'safari16', 'edge120'],
      // Output directory
      outDir: 'dist',
      // Optimize bundle splitting
      rollupOptions: {
        output: {
          manualChunks: {
            // Vendor chunks
            vendor: ['react', 'react-dom'],
            router: ['react-router-dom'],
            redux: ['@reduxjs/toolkit', 'react-redux'],
            ui: ['@headlessui/react', '@heroicons/react'],
            utils: ['axios', 'dayjs', 'lodash'],
          },
          // Asset file naming
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `assets/images/[name]-[hash][extname]`
            }
            if (/css/i.test(ext)) {
              return `assets/css/[name]-[hash][extname]`
            }
            return `assets/[name]-[hash][extname]`
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
        }
      },
      // Optimize chunk size
      chunkSizeWarningLimit: 1000,
      // Enable source maps for production debugging (can be disabled for smaller builds)
      sourcemap: mode === 'production' ? false : true,
      // CSS code splitting
      cssCodeSplit: true,
      // Minification
      minify: mode === 'production' ? 'esbuild' : false,
      // Asset inlining threshold
      assetsInlineLimit: 4096,
      // Optimize for production
      reportCompressedSize: mode === 'production',
      // Clean output directory before build
      emptyOutDir: true,
    },
    // Performance optimizations
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@reduxjs/toolkit',
        'react-redux',
        'axios',
        'dayjs'
      ]
    },
    // Development server optimizations
    server: {
      // Enable HMR
      hmr: true,
      // Optimize file watching
      watch: {
        usePolling: false,
        interval: 100
      },
      // Proxy API requests to backend during development
      proxy: mode === 'development' ? {
        '/api': {
          target: env.VITE_API_URL || 'http://localhost:5000',
          changeOrigin: true,
          secure: false
        }
      } : undefined
    },
    // Preview server configuration
    preview: {
      port: 4173,
      host: true,
      strictPort: true
    },
    // Resolve aliases
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@pages': resolve(__dirname, './src/pages'),
        '@utils': resolve(__dirname, './src/utils'),
        '@services': resolve(__dirname, './src/services'),
        '@store': resolve(__dirname, './src/store'),
        '@types': resolve(__dirname, './src/types'),
        '@assets': resolve(__dirname, './src/assets'),
      }
    }
  }
})
