// ============================================================================
// Nirvana Organics E-commerce - PM2 Ecosystem Configuration
// ============================================================================

module.exports = {
  apps: [
    {
      // Main Backend API
      name: 'nirvana-backend',
      script: 'server/index.js',
      cwd: '/var/www/nirvana-backend/current',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 5000
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 5000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      // Logging
      log_file: '/var/www/nirvana-backend/shared/logs/combined.log',
      out_file: '/var/www/nirvana-backend/shared/logs/out.log',
      error_file: '/var/www/nirvana-backend/shared/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process Management
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      autorestart: true,
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      
      // Advanced Options
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      wait_ready: true,
      
      // Health Monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,

      // Production Environment Variables
      env_file: '.env'
    },
    
    {
      // Admin Server (Separate Process)
      name: 'nirvana-admin',
      script: 'server/admin-server.js',
      cwd: '/var/www/nirvana-backend/current',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 5001
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 5001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5001
      },
      // Logging
      log_file: '/var/www/nirvana-backend/shared/logs/admin-combined.log',
      out_file: '/var/www/nirvana-backend/shared/logs/admin-out.log',
      error_file: '/var/www/nirvana-backend/shared/logs/admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process Management
      max_memory_restart: '512M',
      min_uptime: '10s',
      max_restarts: 5,
      autorestart: true,
      watch: false
    }
  ],

  // Deployment Configuration
  deploy: {
    production: {
      user: 'root', // UPDATE: Replace with actual SSH username
      host: ['shopnirvanaorganics.com'], // UPDATE: Replace with actual production server host
      ref: 'origin/main',
      repo: 'https://github.com/your-username/nirvana-organics-backend.git', // UPDATE: Replace with actual repository URL
      path: '/var/www/nirvana-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && npm run migrate:prod && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'mkdir -p /var/www/nirvana-backend/shared/{logs,uploads,config}',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'root', // UPDATE: Replace with actual SSH username
      host: ['staging.shopnirvanaorganics.com'], // UPDATE: Replace with actual staging server host
      ref: 'origin/staging',
      repo: 'https://github.com/your-username/nirvana-organics-backend.git', // UPDATE: Replace with actual repository URL
      path: '/var/www/nirvana-backend-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && npm run migrate:prod && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': 'mkdir -p /var/www/nirvana-backend-staging/shared/{logs,uploads,config}',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
