const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const rateLimit = require('express-rate-limit');

const RBACController = require('../controllers/rbacController');
const {
  authenticate,
  requireAdmin,
  requireManagerOrAdmin,
  requireManagerInvitation,
  requireRoleManagement,
  requireAuditAccess,
  auditAdminAction
} = require('../middleware/auth');

// Rate limiting for sensitive operations
const invitationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each IP to 10 invitations per hour
  message: {
    success: false,
    message: 'Too many invitation attempts, please try again later.'
  }
});

const roleUpdateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // limit each IP to 20 role updates per 15 minutes
  message: {
    success: false,
    message: 'Too many role update attempts, please try again later.'
  }
});

// Validation middleware
const validateManagerInvitation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number')
];

const validateAccountActivation = [
  body('token')
    .notEmpty()
    .withMessage('Verification token is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

const validateRoleUpdate = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('Valid user ID is required'),
  body('role')
    .isIn(['admin', 'manager', 'customer'])
    .withMessage('Role must be admin, manager, or customer')
];

const validateUserId = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('Valid user ID is required')
];

const validateRoleName = [
  param('roleName')
    .isIn(['admin', 'manager', 'customer'])
    .withMessage('Role name must be admin, manager, or customer')
];

const validateAuditQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('userId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
  query('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Severity must be low, medium, high, or critical'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
];

// RBAC Management Routes (Admin only)

/**
 * @route POST /api/admin/rbac/invite-manager
 * @desc Invite a new manager
 * @access Private (Admin only)
 */
router.post('/invite-manager',
  authenticate,
  requireManagerInvitation,
  invitationLimiter,
  validateManagerInvitation,
  auditAdminAction('INVITE_MANAGER', 'USER'),
  RBACController.inviteManager
);

/**
 * @route PUT /api/admin/rbac/users/:userId/role
 * @desc Update user role
 * @access Private (Admin only)
 */
router.put('/users/:userId/role',
  authenticate,
  requireRoleManagement,
  roleUpdateLimiter,
  validateRoleUpdate,
  auditAdminAction('UPDATE_USER_ROLE', 'USER'),
  RBACController.updateUserRole
);

/**
 * @route GET /api/admin/rbac/permissions/:userId
 * @desc Get user permissions
 * @access Private (Admin/Manager)
 */
router.get('/permissions/:userId',
  authenticate,
  requireManagerOrAdmin,
  validateUserId,
  RBACController.getUserPermissions
);

/**
 * @route GET /api/admin/rbac/roles
 * @desc Get all roles and their permissions
 * @access Private (Admin only)
 */
router.get('/roles',
  authenticate,
  requireAdmin,
  RBACController.getRoles
);

/**
 * @route GET /api/admin/rbac/users-by-role/:roleName
 * @desc Get users by role
 * @access Private (Admin/Manager)
 */
router.get('/users-by-role/:roleName',
  authenticate,
  requireManagerOrAdmin,
  validateRoleName,
  RBACController.getUsersByRole
);

/**
 * @route GET /api/admin/rbac/audit-logs
 * @desc Get audit logs
 * @access Private (Admin only)
 */
router.get('/audit-logs',
  authenticate,
  requireAuditAccess,
  validateAuditQuery,
  RBACController.getAuditLogs
);

/**
 * @route GET /api/admin/rbac/audit-stats
 * @desc Get audit statistics
 * @access Private (Admin only)
 */
router.get('/audit-stats',
  authenticate,
  requireAuditAccess,
  validateAuditQuery,
  RBACController.getAuditStatistics
);

// Public Routes (for account activation)

/**
 * @route POST /api/auth/activate-manager
 * @desc Activate manager account with token
 * @access Public (with token)
 */
router.post('/activate-manager',
  validateAccountActivation,
  RBACController.activateManagerAccount
);

// User Routes (for checking own permissions)

/**
 * @route GET /api/auth/my-permissions
 * @desc Get current user's permissions
 * @access Private
 */
router.get('/my-permissions',
  authenticate,
  RBACController.getMyPermissions
);

module.exports = router;
