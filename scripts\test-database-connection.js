#!/usr/bin/env node

/**
 * Database Connection Test Script for Nirvana Organics
 * Tests connection to Hostinger MySQL database
 */

const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`)
};

/**
 * Test database connection
 */
async function testDatabaseConnection() {
  log.info('Testing Hostinger MySQL Database Connection...');
  log.info('================================================');

  // Validate required environment variables
  const requiredVars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    log.error(`Missing required database environment variables: ${missingVars.join(', ')}`);
    log.error('Please check your .env file and ensure all database variables are set.');
    return false;
  }

  // Display connection info (without password)
  log.info('Database Configuration:');
  log.info(`Host: ${process.env.DB_HOST}`);
  log.info(`Port: ${process.env.DB_PORT || 3306}`);
  log.info(`Database: ${process.env.DB_NAME}`);
  log.info(`User: ${process.env.DB_USER}`);
  log.info(`Timezone: ${process.env.DB_TIMEZONE || '+00:00'}`);

  // Create Sequelize instance
  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      dialect: 'mysql', // mysql2 driver supports both MySQL and MariaDB
      timezone: process.env.DB_TIMEZONE || '+00:00',
      charset: process.env.DB_CHARSET || 'utf8mb4',
      collate: process.env.DB_COLLATE || 'utf8mb4_unicode_ci',
      logging: false, // Disable SQL logging for cleaner output
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      dialectOptions: {
        charset: 'utf8mb4',
        dateStrings: true,
        typeCast: true,
        timezone: 'local',
        // MariaDB specific options
        supportBigNumbers: true,
        bigNumberStrings: true
      }
    }
  );

  try {
    // Test basic connection
    log.info('Testing database connection...');
    await sequelize.authenticate();
    log.success('Database connection established successfully!');

    // Test database version
    log.info('Checking MySQL version...');
    const [results] = await sequelize.query('SELECT VERSION() as version');
    log.success(`MySQL Version: ${results[0].version}`);

    // Test database charset
    log.info('Checking database charset...');
    const [charsetResults] = await sequelize.query(`
      SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
      FROM information_schema.SCHEMATA 
      WHERE SCHEMA_NAME = '${process.env.DB_NAME}'
    `);
    
    if (charsetResults.length > 0) {
      log.success(`Database Charset: ${charsetResults[0].DEFAULT_CHARACTER_SET_NAME}`);
      log.success(`Database Collation: ${charsetResults[0].DEFAULT_COLLATION_NAME}`);
    }

    // Test table creation permissions
    log.info('Testing table creation permissions...');
    const testTableName = 'test_connection_' + Date.now();
    
    await sequelize.query(`
      CREATE TABLE ${testTableName} (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_field VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table creation test passed');

    // Test insert permissions
    log.info('Testing insert permissions...');
    await sequelize.query(`
      INSERT INTO ${testTableName} (test_field) VALUES ('test_value')
    `);
    log.success('Insert test passed');

    // Test select permissions
    log.info('Testing select permissions...');
    const [selectResults] = await sequelize.query(`SELECT * FROM ${testTableName}`);
    log.success(`Select test passed (${selectResults.length} rows)`);

    // Clean up test table
    await sequelize.query(`DROP TABLE ${testTableName}`);
    log.success('Test table cleaned up');

    // Test timezone handling (MariaDB compatible)
    log.info('Testing timezone handling...');
    const [timezoneResults] = await sequelize.query('SELECT NOW(), UTC_TIMESTAMP()');
    log.success(`Server Time: ${timezoneResults[0]['NOW()']}`);
    log.success(`UTC Time: ${timezoneResults[0]['UTC_TIMESTAMP()']}`);

    return true;

  } catch (error) {
    log.error('Database connection failed!');
    log.error(`Error: ${error.message}`);
    
    // Provide helpful error messages
    if (error.message.includes('ENOTFOUND')) {
      log.error('Host not found. Please check your DB_HOST setting.');
      log.error('Make sure you have the correct MySQL host from Hostinger.');
    } else if (error.message.includes('Access denied')) {
      log.error('Access denied. Please check your username and password.');
      log.error('Make sure you have the correct DB_USER and DB_PASSWORD.');
    } else if (error.message.includes('Unknown database')) {
      log.error('Database not found. Please check your DB_NAME setting.');
      log.error('Make sure the database exists in your Hostinger MySQL panel.');
    } else if (error.message.includes('ETIMEDOUT')) {
      log.error('Connection timeout. Please check your network connection.');
      log.error('Hostinger may have firewall restrictions.');
    }

    return false;

  } finally {
    // Close the connection
    await sequelize.close();
    log.info('Database connection closed');
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const success = await testDatabaseConnection();
    
    log.info('================================================');
    if (success) {
      log.success('Database connection test completed successfully!');
      log.info('Your Hostinger MySQL database is ready for use.');
      process.exit(0);
    } else {
      log.error('Database connection test failed!');
      log.error('Please check your database configuration and try again.');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Test failed with error: ${error.message}`);
    process.exit(1);
  }
}

// Handle script arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node scripts/test-database-connection.js [--help]');
  console.log('');
  console.log('This script tests the connection to your Hostinger MySQL database.');
  console.log('Make sure your .env file is configured with the correct database credentials.');
  console.log('');
  console.log('Required environment variables:');
  console.log('- DB_HOST: MySQL host from Hostinger');
  console.log('- DB_NAME: u106832845_nirvana');
  console.log('- DB_USER: u106832845_root');
  console.log('- DB_PASSWORD: Your MySQL password');
  process.exit(0);
}

// Run test if called directly
if (require.main === module) {
  main();
}

module.exports = { testDatabaseConnection };
