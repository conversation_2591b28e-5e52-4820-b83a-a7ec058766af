import React, { useState, useEffect } from 'react';
import WhatsAppChatInterface from './WhatsAppChatInterface';

interface WhatsAppWidgetProps {
  phoneNumber?: string;
  businessName?: string;
  welcomeMessage?: string;
}

const WhatsAppWidget: React.FC<WhatsAppWidgetProps> = ({
  phoneNumber = '+1234567890', // Replace with actual business number
  businessName = 'Nirvana Organics',
  welcomeMessage = 'Hello! How can we help you today?'
}) => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    // Show widget after a delay to avoid loading issues
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 1500); // Reduced delay for better UX

    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  return (
    <>
      {/* WhatsApp Chat Interface */}
      <WhatsAppChatInterface
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        businessName={businessName}
        businessPhone={phoneNumber}
      />

      {/* WhatsApp Widget Button - Fixed positioning for mobile stability */}
      <div
        className="whatsapp-widget-container"
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: isChatOpen ? 10002 : 10000,
          pointerEvents: 'auto',
          transform: isHovered ? 'scale(1.05)' : 'scale(1)',
          transition: 'transform 0.3s ease',
          willChange: 'transform',
          isolation: 'isolate'
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onTouchStart={() => setIsHovered(true)}
        onTouchEnd={() => setIsHovered(false)}
      >

        {/* WhatsApp Button */}
        <button
          onClick={() => setIsChatOpen(!isChatOpen)}
          className="group relative bg-green-500 hover:bg-green-600 active:bg-green-700 text-white w-14 h-14 sm:w-16 sm:h-16 rounded-full transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-green-300 focus:ring-opacity-50 flex items-center justify-center shadow-lg hover:shadow-xl"
          aria-label="Chat with us on WhatsApp"
          style={{
            transform: 'none',
            backfaceVisibility: 'hidden',
            touchAction: 'manipulation' // Improve touch responsiveness
          }}
        >
        {/* WhatsApp Icon */}
        <svg
          className="w-6 h-6 sm:w-7 sm:h-7"
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>

        {/* Mobile-Optimized Tooltip */}
        <div
          className="tooltip-container absolute px-3 py-2 bg-gray-900 text-white text-xs sm:text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap pointer-events-none"
          style={{
            bottom: '70px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 10003,
            minWidth: 'max-content',
            maxWidth: '160px',
            fontSize: window.innerWidth < 640 ? '12px' : '14px'
          }}
        >
          Chat with us
          <div
            className="absolute w-0 h-0 border-l-3 border-r-3 border-t-3 border-transparent border-t-gray-900"
            style={{
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              borderLeftWidth: '6px',
              borderRightWidth: '6px',
              borderTopWidth: '6px'
            }}
          ></div>
        </div>

          {/* Notification Badge */}
          <div
            className="absolute bg-red-500 animate-pulse border-2 border-white"
            style={{
              top: '2px',
              right: '2px',
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              zIndex: 2
            }}
          ></div>
        </button>
      </div>
    </>
  );
};

export default WhatsAppWidget;
