#!/usr/bin/env node

/**
 * Square Production Configuration Test
 * Tests the Square API connection with production credentials
 */

require('dotenv').config();
const { Client, Environment } = require('square');

console.log('🔧 Testing Square Production Configuration...\n');

// Display configuration (without sensitive data)
console.log('Configuration:');
console.log(`  Environment: ${process.env.SQUARE_ENVIRONMENT}`);
console.log(`  Application ID: ${process.env.SQUARE_APPLICATION_ID}`);
console.log(`  Location ID: ${process.env.SQUARE_LOCATION_ID}`);
console.log(`  Access Token: ${process.env.SQUARE_ACCESS_TOKEN ? '***' + process.env.SQUARE_ACCESS_TOKEN.slice(-4) : 'NOT SET'}`);
console.log(`  Webhook Key: ${process.env.SQUARE_WEBHOOK_SIGNATURE_KEY ? '***' + process.env.SQUARE_WEBHOOK_SIGNATURE_KEY.slice(-4) : 'NOT SET'}\n`);

// Validate required environment variables
const requiredVars = [
  'SQUARE_APPLICATION_ID',
  'SQUARE_ACCESS_TOKEN', 
  'SQUARE_LOCATION_ID',
  'SQUARE_ENVIRONMENT',
  'SQUARE_WEBHOOK_SIGNATURE_KEY'
];

let missingVars = [];
requiredVars.forEach(varName => {
  if (!process.env[varName]) {
    missingVars.push(varName);
  }
});

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  process.exit(1);
}

// Validate environment setting
if (process.env.SQUARE_ENVIRONMENT !== 'production') {
  console.error(`❌ SQUARE_ENVIRONMENT should be 'production', but is '${process.env.SQUARE_ENVIRONMENT}'`);
  process.exit(1);
}

// Initialize Square client
const squareClient = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN,
  environment: Environment.Production
});

const locationsApi = squareClient.locationsApi;

async function testSquareConnection() {
  try {
    console.log('🔍 Testing Square API connection...');
    
    // Test locations API
    const response = await locationsApi.listLocations();
    
    if (response.result && response.result.locations) {
      console.log('✅ Square API Connection Successful!\n');
      
      console.log('📍 Available Locations:');
      response.result.locations.forEach((location, index) => {
        console.log(`  ${index + 1}. ${location.name || 'Unnamed Location'}`);
        console.log(`     ID: ${location.id}`);
        console.log(`     Status: ${location.status}`);
        console.log(`     Type: ${location.type || 'N/A'}`);
        
        if (location.address) {
          const addr = location.address;
          const addressParts = [
            addr.addressLine1,
            addr.locality,
            addr.administrativeDistrictLevel1,
            addr.postalCode
          ].filter(Boolean);
          console.log(`     Address: ${addressParts.join(', ')}`);
        }
        
        if (location.capabilities) {
          console.log(`     Capabilities: ${location.capabilities.join(', ')}`);
        }
        
        console.log('');
      });
      
      // Verify the configured location ID exists
      const configuredLocation = response.result.locations.find(
        loc => loc.id === process.env.SQUARE_LOCATION_ID
      );
      
      if (configuredLocation) {
        console.log(`✅ Configured location ID (${process.env.SQUARE_LOCATION_ID}) found and active`);
        console.log(`   Location Name: ${configuredLocation.name}`);
        console.log(`   Status: ${configuredLocation.status}`);
      } else {
        console.error(`❌ Configured location ID (${process.env.SQUARE_LOCATION_ID}) not found in available locations`);
        console.error('   Please verify your SQUARE_LOCATION_ID setting');
        return false;
      }
      
    } else {
      console.error('❌ No locations found in Square account');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Square API Connection Failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.errors && Array.isArray(error.errors)) {
      console.error('   Details:');
      error.errors.forEach(err => {
        console.error(`     - ${err.category}: ${err.detail}`);
      });
    }
    
    // Common error scenarios
    if (error.message.includes('UNAUTHORIZED')) {
      console.error('\n💡 Troubleshooting:');
      console.error('   - Verify your SQUARE_ACCESS_TOKEN is correct');
      console.error('   - Ensure the token has the required permissions');
      console.error('   - Check that the token is for production environment');
    }
    
    return false;
  }
}

async function main() {
  const success = await testSquareConnection();
  
  if (success) {
    console.log('\n🎉 Square Production Configuration Test PASSED');
    console.log('   Your Square integration is ready for production!');
    process.exit(0);
  } else {
    console.log('\n❌ Square Production Configuration Test FAILED');
    console.log('   Please fix the issues above before deploying to production');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

main();
