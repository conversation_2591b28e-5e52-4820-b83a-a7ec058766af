import api from './api';

export interface SocialMediaPost {
  id: string;
  platform: 'facebook' | 'instagram' | 'youtube' | 'tiktok' | 'medium';
  title: string;
  content: string;
  imageUrl?: string;
  videoUrl?: string;
  url: string;
  publishedAt: string;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
    views?: number;
  };
  author?: string;
}

export interface SocialMediaStats {
  platform: string;
  followers: number;
  posts: number;
  engagement: number;
  growth: number;
  url: string;
  icon: string;
  color: string;
}

export interface SocialMediaFeed {
  platform: string;
  posts: SocialMediaPost[];
  lastUpdated: string;
  error?: string;
}

class SocialMediaService {
  /**
   * Get all social media posts
   */
  async getPosts(options?: {
    platform?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ success: boolean; data: SocialMediaPost[] }> {
    try {
      const response = await api.get('/api/social-media/posts', {
        params: options
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching social media posts:', error);
      throw error;
    }
  }

  /**
   * Get social media platform statistics
   */
  async getStats(): Promise<{ success: boolean; data: SocialMediaStats[] }> {
    try {
      const response = await api.get('/api/social-media/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching social media stats:', error);
      throw error;
    }
  }

  /**
   * Get posts from a specific platform
   */
  async getPlatformPosts(platform: string, options?: {
    limit?: number;
    offset?: number;
  }): Promise<{ success: boolean; data: SocialMediaPost[] }> {
    try {
      const response = await api.get(`/api/social-media/platforms/${platform}/posts`, {
        params: options
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${platform} posts:`, error);
      throw error;
    }
  }

  /**
   * Get platform-specific statistics
   */
  async getPlatformStats(platform: string): Promise<{ success: boolean; data: SocialMediaStats }> {
    try {
      const response = await api.get(`/api/social-media/platforms/${platform}/stats`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${platform} stats:`, error);
      throw error;
    }
  }

  /**
   * Refresh social media feeds (admin only)
   */
  async refreshFeeds(platforms?: string[]): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.post('/api/admin/social-media/refresh', {
        platforms
      });
      return response.data;
    } catch (error) {
      console.error('Error refreshing social media feeds:', error);
      throw error;
    }
  }

  /**
   * Get social media feed status (admin only)
   */
  async getFeedStatus(): Promise<{ success: boolean; data: SocialMediaFeed[] }> {
    try {
      const response = await api.get('/api/admin/social-media/feeds/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching feed status:', error);
      throw error;
    }
  }

  /**
   * Update social media platform settings (admin only)
   */
  async updatePlatformSettings(platform: string, settings: {
    enabled: boolean;
    apiKey?: string;
    accessToken?: string;
    refreshInterval?: number;
    maxPosts?: number;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.put(`/api/admin/social-media/platforms/${platform}/settings`, settings);
      return response.data;
    } catch (error) {
      console.error(`Error updating ${platform} settings:`, error);
      throw error;
    }
  }

  /**
   * Get social media platform settings (admin only)
   */
  async getPlatformSettings(platform: string): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get(`/api/admin/social-media/platforms/${platform}/settings`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${platform} settings:`, error);
      throw error;
    }
  }

  /**
   * Share content to social media platforms (admin only)
   */
  async shareContent(data: {
    platforms: string[];
    title: string;
    content: string;
    imageUrl?: string;
    url?: string;
    scheduledAt?: string;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.post('/api/admin/social-media/share', data);
      return response.data;
    } catch (error) {
      console.error('Error sharing content:', error);
      throw error;
    }
  }

  /**
   * Get social media analytics (admin only)
   */
  async getAnalytics(options?: {
    platform?: string;
    dateFrom?: string;
    dateTo?: string;
    metrics?: string[];
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/social-media/analytics', {
        params: options
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching social media analytics:', error);
      throw error;
    }
  }

  /**
   * Get engagement metrics for a specific post
   */
  async getPostEngagement(postId: string): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get(`/api/social-media/posts/${postId}/engagement`);
      return response.data;
    } catch (error) {
      console.error('Error fetching post engagement:', error);
      throw error;
    }
  }

  /**
   * Search social media posts
   */
  async searchPosts(query: {
    keyword?: string;
    platform?: string;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
  }): Promise<{ success: boolean; data: SocialMediaPost[] }> {
    try {
      const response = await api.get('/api/social-media/posts/search', {
        params: query
      });
      return response.data;
    } catch (error) {
      console.error('Error searching posts:', error);
      throw error;
    }
  }

  /**
   * Get trending hashtags and topics
   */
  async getTrendingTopics(platform?: string): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/social-media/trending', {
        params: { platform }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching trending topics:', error);
      throw error;
    }
  }
}

export const socialMediaService = new SocialMediaService();
export default socialMediaService;
