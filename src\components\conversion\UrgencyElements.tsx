import React, { useState, useEffect } from 'react';
import { 
  ClockIcon, 
  FireIcon, 
  ExclamationTriangleIcon,
  EyeIcon,
  ShoppingCartIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface UrgencyElementProps {
  type: 'countdown' | 'stock' | 'demand' | 'limited_time' | 'social_proof' | 'flash_sale';
  data?: any;
  className?: string;
}

interface CountdownTimerProps {
  endTime: Date;
  onExpire?: () => void;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ endTime, onExpire }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = endTime.getTime() - now;

      if (distance < 0) {
        clearInterval(timer);
        onExpire?.();
        return;
      }

      setTimeLeft({
        days: Math.floor(distance / (1000 * 60 * 60 * 24)),
        hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((distance % (1000 * 60)) / 1000)
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [endTime, onExpire]);

  return (
    <div className="flex items-center space-x-2">
      {timeLeft.days > 0 && (
        <div className="bg-red-600 text-white px-2 py-1 rounded text-sm font-bold">
          {timeLeft.days}d
        </div>
      )}
      <div className="bg-red-600 text-white px-2 py-1 rounded text-sm font-bold">
        {String(timeLeft.hours).padStart(2, '0')}h
      </div>
      <div className="bg-red-600 text-white px-2 py-1 rounded text-sm font-bold">
        {String(timeLeft.minutes).padStart(2, '0')}m
      </div>
      <div className="bg-red-600 text-white px-2 py-1 rounded text-sm font-bold">
        {String(timeLeft.seconds).padStart(2, '0')}s
      </div>
    </div>
  );
};

const UrgencyElements: React.FC<UrgencyElementProps> = ({ type, data, className = '' }) => {
  const [currentViewers, setCurrentViewers] = useState(Math.floor(Math.random() * 15) + 5);
  const [recentPurchases] = useState([
    { name: 'Sarah M.', location: 'California', product: 'CBD Flower', time: '2 minutes ago' },
    { name: 'Mike R.', location: 'Texas', product: 'THC-A Pre-Roll', time: '5 minutes ago' },
    { name: 'Jennifer L.', location: 'Florida', product: 'Delta-9 Chocolate', time: '8 minutes ago' },
    { name: 'David K.', location: 'Colorado', product: 'Diamond Sauce', time: '12 minutes ago' }
  ]);

  useEffect(() => {
    if (type === 'demand') {
      const interval = setInterval(() => {
        setCurrentViewers(prev => {
          const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
          return Math.max(3, Math.min(25, prev + change));
        });
      }, 10000); // Update every 10 seconds

      return () => clearInterval(interval);
    }
  }, [type]);

  const baseClasses = `inline-flex items-center space-x-2 ${className}`;

  switch (type) {
    case 'countdown':
      const saleEndTime = data?.endTime || new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
      return (
        <div className={`${baseClasses} bg-red-50 border border-red-200 rounded-lg p-4`}>
          <div className="flex items-center space-x-3">
            <ClockIcon className="h-6 w-6 text-red-600" />
            <div>
              <p className="text-sm font-medium text-red-900">
                {data?.title || 'Limited Time Offer Ends In:'}
              </p>
              <CountdownTimer endTime={saleEndTime} />
            </div>
          </div>
        </div>
      );

    case 'stock':
      const stockLevel = data?.stock || Math.floor(Math.random() * 10) + 1;
      const isLowStock = stockLevel <= 5;
      return (
        <div className={`${baseClasses} ${isLowStock ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'} border rounded-lg p-3`}>
          <ExclamationTriangleIcon className={`h-5 w-5 ${isLowStock ? 'text-red-600' : 'text-yellow-600'}`} />
          <span className={`text-sm font-medium ${isLowStock ? 'text-red-900' : 'text-yellow-900'}`}>
            {isLowStock 
              ? `Only ${stockLevel} left in stock!` 
              : `${stockLevel} items remaining`
            }
          </span>
        </div>
      );

    case 'demand':
      return (
        <div className={`${baseClasses} bg-blue-50 border border-blue-200 rounded-lg p-3`}>
          <EyeIcon className="h-5 w-5 text-blue-600" />
          <span className="text-sm font-medium text-blue-900">
            {currentViewers} people are viewing this item
          </span>
        </div>
      );

    case 'limited_time':
      return (
        <div className={`${baseClasses} bg-orange-50 border border-orange-200 rounded-lg p-3`}>
          <FireIcon className="h-5 w-5 text-orange-600" />
          <span className="text-sm font-medium text-orange-900">
            {data?.message || 'Limited time offer - Act fast!'}
          </span>
        </div>
      );

    case 'social_proof':
      const randomPurchase = recentPurchases[Math.floor(Math.random() * recentPurchases.length)];
      return (
        <div className={`${baseClasses} bg-green-50 border border-green-200 rounded-lg p-3`}>
          <ShoppingCartIcon className="h-5 w-5 text-green-600" />
          <div className="text-sm">
            <span className="font-medium text-green-900">
              {randomPurchase.name} from {randomPurchase.location}
            </span>
            <span className="text-green-700"> purchased {randomPurchase.product} </span>
            <span className="text-green-600">{randomPurchase.time}</span>
          </div>
        </div>
      );

    case 'flash_sale':
      return (
        <div className={`${baseClasses} bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg p-4`}>
          <div className="flex items-center space-x-3">
            <div className="animate-pulse">
              <FireIcon className="h-6 w-6" />
            </div>
            <div>
              <p className="font-bold text-lg">FLASH SALE!</p>
              <p className="text-sm opacity-90">
                {data?.discount || '25%'} OFF - Limited Time Only
              </p>
            </div>
          </div>
        </div>
      );

    default:
      return null;
  }
};

// Composite component for multiple urgency elements
export const UrgencyBar: React.FC<{ 
  elements: Array<{ type: UrgencyElementProps['type']; data?: any }>;
  className?: string;
}> = ({ elements, className = '' }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (elements.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % elements.length);
      }, 5000); // Rotate every 5 seconds

      return () => clearInterval(interval);
    }
  }, [elements.length]);

  if (elements.length === 0) return null;

  return (
    <div className={`transition-all duration-500 ${className}`}>
      <UrgencyElements 
        type={elements[currentIndex].type} 
        data={elements[currentIndex].data}
      />
    </div>
  );
};

// Product page urgency component
export const ProductUrgency: React.FC<{ 
  product: any;
  className?: string;
}> = ({ product, className = '' }) => {
  const urgencyElements = [];

  // Add stock urgency if low stock
  if (product.stock <= 10) {
    urgencyElements.push({
      type: 'stock' as const,
      data: { stock: product.stock }
    });
  }

  // Add demand indicator
  urgencyElements.push({
    type: 'demand' as const
  });

  // Add social proof
  urgencyElements.push({
    type: 'social_proof' as const
  });

  // Add flash sale if product is on sale
  if (product.salePrice && product.salePrice < product.price) {
    const discount = Math.round(((product.price - product.salePrice) / product.price) * 100);
    urgencyElements.push({
      type: 'flash_sale' as const,
      data: { discount: `${discount}%` }
    });
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <UrgencyBar elements={urgencyElements} />
    </div>
  );
};

// Cart urgency component
export const CartUrgency: React.FC<{ 
  cartTotal: number;
  freeShippingThreshold?: number;
  className?: string;
}> = ({ cartTotal, freeShippingThreshold = 100, className = '' }) => {
  const remaining = freeShippingThreshold - cartTotal;
  
  if (remaining <= 0) {
    return (
      <div className={`bg-green-50 border border-green-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center space-x-2">
          <StarIcon className="h-5 w-5 text-green-600" />
          <span className="text-sm font-medium text-green-900">
            🎉 You qualify for FREE shipping!
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-2">
        <ShoppingCartIcon className="h-5 w-5 text-blue-600" />
        <span className="text-sm font-medium text-blue-900">
          Add ${remaining.toFixed(2)} more for FREE shipping!
        </span>
      </div>
    </div>
  );
};

export default UrgencyElements;
