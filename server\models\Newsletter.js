const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Newsletter = sequelize.define('Newsletter', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  isSubscribed: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  subscribedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  unsubscribedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  preferences: {
    type: DataTypes.JSON,
    defaultValue: {
      promotions: true,
      newProducts: true,
      newsletters: true,
      orderUpdates: true
    }
  },
  source: {
    type: DataTypes.STRING,
    defaultValue: 'website'
  },
  unsubscribeToken: {
    type: DataTypes.STRING,
    allowNull: true
  }
}, {
  tableName: 'newsletters',
  timestamps: true,
  indexes: [
    {
      fields: ['email']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['isSubscribed']
    }
  ]
});

// Associations
Newsletter.associate = (models) => {
  Newsletter.belongsTo(models.User, {
    foreignKey: 'userId',
    as: 'user'
  });
};

module.exports = Newsletter;
