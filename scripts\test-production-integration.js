#!/usr/bin/env node

/**
 * Production Integration Testing Suite
 * Comprehensive testing of all production systems and integrations
 */

require('dotenv').config();
const axios = require('axios');
const { Client, Environment } = require('square');
const fs = require('fs');

console.log('🧪 Running Production Integration Tests...\n');

// Configuration
const config = {
  baseUrl: process.env.FRONTEND_URL || 'https://shopnirvanaorganics.com',
  apiUrl: process.env.API_BASE_URL || 'https://shopnirvanaorganics.com/api',
  timeout: 10000,
  retries: 3
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  tests: []
};

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m',
    test: '\x1b[35m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

async function runTest(testName, testFunction) {
  log(`🔍 Testing: ${testName}`, 'test');
  
  try {
    const startTime = Date.now();
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    if (result.success) {
      log(`✅ ${testName} - PASSED (${duration}ms)`, 'success');
      testResults.passed++;
    } else {
      log(`❌ ${testName} - FAILED: ${result.error}`, 'error');
      testResults.failed++;
    }
    
    testResults.tests.push({
      name: testName,
      success: result.success,
      duration,
      error: result.error || null,
      details: result.details || null
    });
    
  } catch (error) {
    log(`❌ ${testName} - ERROR: ${error.message}`, 'error');
    testResults.failed++;
    testResults.tests.push({
      name: testName,
      success: false,
      duration: 0,
      error: error.message
    });
  }
}

// Test 1: Frontend Accessibility
async function testFrontendAccessibility() {
  try {
    const response = await axios.get(config.baseUrl, { timeout: config.timeout });
    
    if (response.status === 200) {
      const hasTitle = response.data.includes('<title>');
      const hasMetaDescription = response.data.includes('meta name="description"');
      const hasViewport = response.data.includes('meta name="viewport"');
      
      return {
        success: hasTitle && hasMetaDescription && hasViewport,
        details: {
          statusCode: response.status,
          hasTitle,
          hasMetaDescription,
          hasViewport,
          contentLength: response.data.length
        }
      };
    }
    
    return { success: false, error: `Unexpected status code: ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test 2: API Health Check
async function testApiHealth() {
  try {
    const response = await axios.get(`${config.apiUrl}/health`, { timeout: config.timeout });
    
    if (response.status === 200 && response.data.status) {
      return {
        success: response.data.status === 'healthy',
        details: response.data
      };
    }
    
    return { success: false, error: 'Invalid health check response' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test 3: Database Connectivity
async function testDatabaseConnectivity() {
  try {
    const response = await axios.get(`${config.apiUrl}/health`, { timeout: config.timeout });
    
    if (response.data.checks && response.data.checks.database) {
      return {
        success: response.data.checks.database.status === 'healthy',
        details: response.data.checks.database
      };
    }
    
    return { success: false, error: 'Database check not found in health response' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test 4: Square Payment Integration
async function testSquareIntegration() {
  try {
    // Test Square API connectivity
    const squareClient = new Client({
      accessToken: process.env.SQUARE_ACCESS_TOKEN,
      environment: Environment.Production
    });
    
    const locationsApi = squareClient.locationsApi;
    const response = await locationsApi.listLocations();
    
    if (response.result && response.result.locations) {
      const configuredLocation = response.result.locations.find(
        loc => loc.id === process.env.SQUARE_LOCATION_ID
      );
      
      return {
        success: !!configuredLocation,
        details: {
          locationCount: response.result.locations.length,
          configuredLocationFound: !!configuredLocation,
          environment: process.env.SQUARE_ENVIRONMENT
        }
      };
    }
    
    return { success: false, error: 'No locations found' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test 5: API Endpoints
async function testApiEndpoints() {
  const endpoints = [
    { path: '/products', method: 'GET', expectedStatus: 200 },
    { path: '/categories', method: 'GET', expectedStatus: 200 },
    { path: '/auth/status', method: 'GET', expectedStatus: [200, 401] }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios({
        method: endpoint.method,
        url: `${config.apiUrl}${endpoint.path}`,
        timeout: config.timeout,
        validateStatus: () => true // Don't throw on any status
      });
      
      const expectedStatuses = Array.isArray(endpoint.expectedStatus) 
        ? endpoint.expectedStatus 
        : [endpoint.expectedStatus];
      
      const success = expectedStatuses.includes(response.status);
      
      results.push({
        path: endpoint.path,
        success,
        status: response.status,
        responseTime: response.headers['x-response-time'] || 'N/A'
      });
      
    } catch (error) {
      results.push({
        path: endpoint.path,
        success: false,
        error: error.message
      });
    }
  }
  
  const allSuccessful = results.every(r => r.success);
  
  return {
    success: allSuccessful,
    details: results
  };
}

// Test 6: SSL Certificate
async function testSSLCertificate() {
  try {
    const https = require('https');
    const url = new URL(config.baseUrl);
    
    return new Promise((resolve) => {
      const req = https.request({
        hostname: url.hostname,
        port: 443,
        path: '/',
        method: 'GET',
        rejectUnauthorized: true
      }, (res) => {
        const cert = res.connection.getPeerCertificate();
        const now = new Date();
        const validFrom = new Date(cert.valid_from);
        const validTo = new Date(cert.valid_to);
        
        const isValid = now >= validFrom && now <= validTo;
        const daysUntilExpiry = Math.floor((validTo - now) / (1000 * 60 * 60 * 24));
        
        resolve({
          success: isValid && daysUntilExpiry > 7,
          details: {
            subject: cert.subject.CN,
            issuer: cert.issuer.CN,
            validFrom: cert.valid_from,
            validTo: cert.valid_to,
            daysUntilExpiry,
            isValid
          }
        });
      });
      
      req.on('error', (error) => {
        resolve({ success: false, error: error.message });
      });
      
      req.end();
    });
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Test 7: Performance Metrics
async function testPerformanceMetrics() {
  const endpoints = [
    config.baseUrl,
    `${config.apiUrl}/health`,
    `${config.apiUrl}/products`
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const startTime = Date.now();
      const response = await axios.get(endpoint, { timeout: config.timeout });
      const responseTime = Date.now() - startTime;
      
      results.push({
        endpoint,
        responseTime,
        status: response.status,
        success: responseTime < 3000 // 3 second threshold
      });
      
    } catch (error) {
      results.push({
        endpoint,
        responseTime: config.timeout,
        success: false,
        error: error.message
      });
    }
  }
  
  const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  const allFast = results.every(r => r.success);
  
  return {
    success: allFast,
    details: {
      averageResponseTime,
      results
    }
  };
}

// Test 8: Admin Panel Access
async function testAdminPanelAccess() {
  try {
    const response = await axios.get(`${config.baseUrl}/admin`, { 
      timeout: config.timeout,
      validateStatus: () => true
    });
    
    // Admin panel should be accessible (200) or redirect to login
    const success = [200, 302, 401].includes(response.status);
    
    return {
      success,
      details: {
        statusCode: response.status,
        accessible: success
      }
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Main test runner
async function runAllTests() {
  log('🚀 Starting Production Integration Tests', 'info');
  log('==========================================\n');
  
  // Run all tests
  await runTest('Frontend Accessibility', testFrontendAccessibility);
  await runTest('API Health Check', testApiHealth);
  await runTest('Database Connectivity', testDatabaseConnectivity);
  await runTest('Square Payment Integration', testSquareIntegration);
  await runTest('API Endpoints', testApiEndpoints);
  await runTest('SSL Certificate', testSSLCertificate);
  await runTest('Performance Metrics', testPerformanceMetrics);
  await runTest('Admin Panel Access', testAdminPanelAccess);
  
  // Generate report
  generateTestReport();
}

// Generate test report
function generateTestReport() {
  log('\n📊 Test Results Summary', 'info');
  log('========================');
  
  const total = testResults.passed + testResults.failed + testResults.skipped;
  const successRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;
  
  log(`Total Tests: ${total}`);
  log(`Passed: ${testResults.passed}`, 'success');
  log(`Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'info');
  log(`Skipped: ${testResults.skipped}`, 'warning');
  log(`Success Rate: ${successRate}%`, successRate >= 90 ? 'success' : 'warning');
  
  // Detailed results
  if (testResults.failed > 0) {
    log('\n❌ Failed Tests:', 'error');
    testResults.tests
      .filter(test => !test.success)
      .forEach(test => {
        log(`   - ${test.name}: ${test.error}`);
      });
  }
  
  // Save report to file
  const report = {
    timestamp: new Date().toISOString(),
    environment: 'production',
    summary: {
      total,
      passed: testResults.passed,
      failed: testResults.failed,
      skipped: testResults.skipped,
      successRate: parseFloat(successRate)
    },
    tests: testResults.tests
  };
  
  const fs = require('fs');
  const reportPath = 'integration-test-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`\n📄 Detailed report saved: ${reportPath}`);
  
  // Exit with appropriate code
  if (testResults.failed > 0) {
    log('\n❌ Some tests failed. Please review and fix issues before deployment.', 'error');
    process.exit(1);
  } else {
    log('\n🎉 All tests passed! Production system is ready.', 'success');
    process.exit(0);
  }
}

// Run tests
runAllTests().catch(error => {
  log(`❌ Test runner failed: ${error.message}`, 'error');
  process.exit(1);
});
