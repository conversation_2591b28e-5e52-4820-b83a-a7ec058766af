#!/usr/bin/env node

/**
 * Minimal Google OAuth test server
 * This creates a simple Express server to test Google OAuth configuration
 */

require('dotenv').config();
const express = require('express');
const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const session = require('express-session');

const app = express();
const PORT = 5001; // Use different port to avoid conflicts

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'test-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to false for development
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// Passport serialization
passport.serializeUser((user, done) => {
  done(null, user);
});

passport.deserializeUser((user, done) => {
  done(null, user);
});

// Google OAuth Strategy
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: `http://localhost:${PORT}/auth/google/callback`
}, (accessToken, refreshToken, profile, done) => {
  console.log('✅ Google OAuth callback received');
  console.log('Profile:', {
    id: profile.id,
    displayName: profile.displayName,
    emails: profile.emails
  });
  return done(null, profile);
}));

// Routes
app.get('/', (req, res) => {
  res.send(`
    <h1>Google OAuth Test Server</h1>
    <p>Server is running on port ${PORT}</p>
    <p><a href="/auth/google/login">Test Google OAuth Login</a></p>
    <p>Environment:</p>
    <ul>
      <li>Google Client ID: ${process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not Set'}</li>
      <li>Google Client Secret: ${process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not Set'}</li>
    </ul>
  `);
});

// Google OAuth routes
app.get('/auth/google/login', passport.authenticate('google', {
  scope: ['profile', 'email']
}));

app.get('/auth/google/callback',
  passport.authenticate('google', { failureRedirect: '/error' }),
  (req, res) => {
    res.send(`
      <h1>Google OAuth Success!</h1>
      <p>Authentication successful</p>
      <p>User: ${req.user.displayName}</p>
      <p>Email: ${req.user.emails ? req.user.emails[0].value : 'No email'}</p>
      <p><a href="/">Back to home</a></p>
    `);
  }
);

app.get('/error', (req, res) => {
  res.send(`
    <h1>Google OAuth Error</h1>
    <p>Authentication failed</p>
    <p><a href="/">Try again</a></p>
  `);
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Minimal OAuth test server running on port ${PORT}`);
  console.log(`📍 Open http://localhost:${PORT} to test Google OAuth`);
  console.log(`🔑 Google Client ID: ${process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not Set'}`);
  console.log(`🔐 Google Client Secret: ${process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not Set'}`);
});

// Error handling
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});
