import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchProductById, updateProduct } from '../../store/slices/productSlice';
import { addToast } from '../../store/slices/uiSlice';
import { Product } from '../../types';
import ProductForm from '../../components/admin/ProductForm';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const ProductEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { currentProduct: product, loading, error } = useAppSelector((state) => state.products);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(fetchProductById(parseInt(id)));
    }
  }, [dispatch, id]);

  const handleUpdateProduct = async (productData: Partial<Product>) => {
    if (!product || !id) return;

    setUpdating(true);
    try {
      await dispatch(updateProduct({
        id: parseInt(id),
        productData
      })).unwrap();

      dispatch(addToast({
        type: 'success',
        title: 'Product Updated',
        message: 'Product has been updated successfully'
      }));

      navigate('/admin/products');
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Update Failed',
        message: error.message || 'Failed to update product'
      }));
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-red-600 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Product Not Found</h3>
            <p className="text-gray-600 mb-4">
              The product you're looking for doesn't exist or has been removed.
            </p>
            <button
              onClick={() => navigate('/admin/products')}
              className="btn-primary"
            >
              Back to Products
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate('/admin/products')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Product</h1>
              <p className="text-gray-600 mt-1">
                Update product information and variants
              </p>
            </div>
          </div>

          {/* Product Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-4">
              {product.images && product.images.length > 0 && (
                <img
                  src={product.images[0].url}
                  alt={product.images[0].alt}
                  className="h-16 w-16 object-cover rounded-lg border border-gray-200"
                  onError={(e) => {
                    e.currentTarget.src = '/images/placeholder-product.jpg';
                  }}
                />
              )}
              <div>
                <h2 className="text-lg font-semibold text-gray-900">{product.name}</h2>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span>SKU: {product.sku}</span>
                  <span>•</span>
                  <span>Price: ${product.price.toFixed(2)}</span>
                  <span>•</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    product.status === 'active' 
                      ? 'bg-green-100 text-green-800'
                      : product.status === 'draft'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Form */}
        <ProductForm
          product={product}
          isEditing={true}
          onSubmit={handleUpdateProduct}
          loading={updating}
        />
      </div>
    </div>
  );
};

export default ProductEdit;
