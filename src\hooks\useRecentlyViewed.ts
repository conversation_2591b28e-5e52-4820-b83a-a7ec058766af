import { useState, useEffect } from 'react';
import { Product } from '../types';

const STORAGE_KEY = 'nirvana_recently_viewed';
const MAX_ITEMS = 10;

interface RecentlyViewedHook {
  recentlyViewed: Product[];
  addToRecentlyViewed: (product: Product) => void;
  removeFromRecentlyViewed: (productId: string) => void;
  clearRecentlyViewed: () => void;
}

export const useRecentlyViewed = (): RecentlyViewedHook => {
  const [recentlyViewed, setRecentlyViewed] = useState<Product[]>([]);

  // Load recently viewed products from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          setRecentlyViewed(parsed);
        }
      }
    } catch (error) {
      console.warn('Failed to load recently viewed products:', error);
    }
  }, []);

  // Save to localStorage whenever recentlyViewed changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(recentlyViewed));
    } catch (error) {
      console.warn('Failed to save recently viewed products:', error);
    }
  }, [recentlyViewed]);

  const addToRecentlyViewed = (product: Product) => {
    setRecentlyViewed(prev => {
      // Remove the product if it already exists
      const filtered = prev.filter(item => item.id !== product.id);
      
      // Add the product to the beginning
      const updated = [product, ...filtered];
      
      // Limit to MAX_ITEMS
      return updated.slice(0, MAX_ITEMS);
    });
  };

  const removeFromRecentlyViewed = (productId: string) => {
    setRecentlyViewed(prev => prev.filter(item => item.id !== productId));
  };

  const clearRecentlyViewed = () => {
    setRecentlyViewed([]);
  };

  return {
    recentlyViewed,
    addToRecentlyViewed,
    removeFromRecentlyViewed,
    clearRecentlyViewed
  };
};
