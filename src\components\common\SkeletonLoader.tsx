import React from 'react';

interface SkeletonLoaderProps {
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular' | 'rounded';
  width?: string | number;
  height?: string | number;
  lines?: number;
  animation?: 'pulse' | 'wave' | 'none';
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  className = '',
  variant = 'rectangular',
  width = '100%',
  height = '1rem',
  lines = 1,
  animation = 'pulse'
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded';
      case 'circular':
        return 'rounded-full';
      case 'rounded':
        return 'rounded-lg';
      case 'rectangular':
      default:
        return 'rounded';
    }
  };

  const getAnimationClasses = () => {
    switch (animation) {
      case 'wave':
        return 'animate-wave';
      case 'pulse':
        return 'animate-pulse';
      case 'none':
      default:
        return '';
    }
  };

  const baseClasses = `bg-gray-200 ${getVariantClasses()} ${getAnimationClasses()} ${className}`;

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  if (lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={baseClasses}
            style={{
              ...style,
              width: index === lines - 1 ? '75%' : style.width, // Last line is shorter
            }}
          />
        ))}
      </div>
    );
  }

  return <div className={baseClasses} style={style} />;
};

// Product Card Skeleton
export const ProductCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Image Skeleton */}
      <div className="aspect-square bg-gray-200 animate-pulse" />
      
      {/* Content Skeleton */}
      <div className="p-4 space-y-3">
        {/* Title */}
        <SkeletonLoader variant="text" height="1.25rem" width="80%" />
        
        {/* Category */}
        <SkeletonLoader variant="text" height="0.875rem" width="60%" />
        
        {/* Price */}
        <SkeletonLoader variant="text" height="1.5rem" width="40%" />
        
        {/* Rating */}
        <div className="flex items-center space-x-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <SkeletonLoader key={i} variant="circular" width="1rem" height="1rem" />
          ))}
          <SkeletonLoader variant="text" height="0.875rem" width="3rem" className="ml-2" />
        </div>
        
        {/* Button */}
        <SkeletonLoader variant="rounded" height="2.5rem" width="100%" />
      </div>
    </div>
  );
};

// Product List Skeleton
export const ProductListSkeleton: React.FC<{ count?: number }> = ({ count = 8 }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <ProductCardSkeleton key={index} />
      ))}
    </div>
  );
};

// Product Detail Skeleton
export const ProductDetailSkeleton: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Gallery Skeleton */}
        <div className="space-y-4">
          <div className="aspect-square bg-gray-200 animate-pulse rounded-lg" />
          <div className="grid grid-cols-4 gap-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="aspect-square bg-gray-200 animate-pulse rounded" />
            ))}
          </div>
        </div>
        
        {/* Product Info Skeleton */}
        <div className="space-y-6">
          {/* Breadcrumb */}
          <SkeletonLoader variant="text" height="0.875rem" width="50%" />
          
          {/* Title */}
          <SkeletonLoader variant="text" height="2rem" width="90%" />
          
          {/* Rating */}
          <div className="flex items-center space-x-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <SkeletonLoader key={i} variant="circular" width="1.25rem" height="1.25rem" />
            ))}
            <SkeletonLoader variant="text" height="1rem" width="6rem" />
          </div>
          
          {/* Price */}
          <SkeletonLoader variant="text" height="2.5rem" width="30%" />
          
          {/* Description */}
          <div className="space-y-2">
            <SkeletonLoader variant="text" height="1rem" lines={4} />
          </div>
          
          {/* Variants */}
          <div className="space-y-3">
            <SkeletonLoader variant="text" height="1.25rem" width="20%" />
            <div className="flex space-x-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <SkeletonLoader key={i} variant="rounded" width="4rem" height="2.5rem" />
              ))}
            </div>
          </div>
          
          {/* Quantity */}
          <div className="space-y-3">
            <SkeletonLoader variant="text" height="1.25rem" width="20%" />
            <SkeletonLoader variant="rounded" width="8rem" height="2.5rem" />
          </div>
          
          {/* Add to Cart Button */}
          <SkeletonLoader variant="rounded" height="3rem" width="100%" />
          
          {/* Product Details */}
          <div className="space-y-4">
            <SkeletonLoader variant="text" height="1.5rem" width="40%" />
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex justify-between">
                  <SkeletonLoader variant="text" height="1rem" width="30%" />
                  <SkeletonLoader variant="text" height="1rem" width="20%" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Category Page Skeleton
export const CategoryPageSkeleton: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8 space-y-4">
        <SkeletonLoader variant="text" height="2.5rem" width="40%" />
        <SkeletonLoader variant="text" height="1.25rem" width="60%" lines={2} />
      </div>
      
      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div className="flex space-x-4">
          <SkeletonLoader variant="rounded" width="8rem" height="2.5rem" />
          <SkeletonLoader variant="rounded" width="8rem" height="2.5rem" />
          <SkeletonLoader variant="rounded" width="8rem" height="2.5rem" />
        </div>
        <SkeletonLoader variant="rounded" width="10rem" height="2.5rem" />
      </div>
      
      {/* Products Grid */}
      <ProductListSkeleton count={12} />
    </div>
  );
};

// Cart Item Skeleton
export const CartItemSkeleton: React.FC = () => {
  return (
    <div className="flex items-center space-x-4 p-4 border-b border-gray-200">
      {/* Image */}
      <SkeletonLoader variant="rounded" width="4rem" height="4rem" />
      
      {/* Content */}
      <div className="flex-1 space-y-2">
        <SkeletonLoader variant="text" height="1.25rem" width="70%" />
        <SkeletonLoader variant="text" height="1rem" width="50%" />
        <div className="flex items-center justify-between">
          <SkeletonLoader variant="rounded" width="6rem" height="2rem" />
          <SkeletonLoader variant="text" height="1.25rem" width="4rem" />
        </div>
      </div>
    </div>
  );
};

// Dashboard Stats Skeleton
export const DashboardStatsSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <SkeletonLoader variant="circular" width="3rem" height="3rem" />
            <div className="ml-4 flex-1">
              <SkeletonLoader variant="text" height="0.875rem" width="60%" />
              <SkeletonLoader variant="text" height="1.5rem" width="40%" className="mt-1" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SkeletonLoader;
