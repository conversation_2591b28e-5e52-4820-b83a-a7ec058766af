import React from 'react';
import { 
  ShieldCheckIcon, 
  LockClosedIcon, 
  CreditCardIcon,
  CheckBadgeIcon,
  StarIcon,
  TruckIcon
} from '@heroicons/react/24/outline';

interface SecurityBadge {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  verified: boolean;
  details: string[];
}

interface TrustMetric {
  label: string;
  value: string;
  icon: React.ComponentType<any>;
  color: string;
}

const SecurityBadges: React.FC<{ variant?: 'full' | 'compact' | 'footer' }> = ({ 
  variant = 'full' 
}) => {
  const securityBadges: SecurityBadge[] = [
    {
      id: 'ssl',
      name: 'SSL Secured',
      description: '256-bit SSL encryption protects your data',
      icon: LockClosedIcon,
      verified: true,
      details: [
        '256-bit SSL encryption',
        'Secure data transmission',
        'Protected checkout process',
        'Verified certificate authority'
      ]
    },
    {
      id: 'pci',
      name: 'PCI DSS Compliant',
      description: 'Payment Card Industry security standards',
      icon: CreditCardIcon,
      verified: true,
      details: [
        'PCI DSS Level 1 compliance',
        'Secure payment processing',
        'Card data protection',
        'Regular security audits'
      ]
    },
    {
      id: 'lab_tested',
      name: 'Lab Tested',
      description: 'Third-party laboratory verification',
      icon: CheckBadgeIcon,
      verified: true,
      details: [
        'Third-party lab testing',
        'COA for every batch',
        'Potency verification',
        'Purity and safety testing'
      ]
    },
    {
      id: 'legal_compliance',
      name: 'Legal Compliance',
      description: '2018 Farm Bill compliant products',
      icon: ShieldCheckIcon,
      verified: true,
      details: [
        '2018 Farm Bill compliant',
        'Less than 0.3% Delta-9 THC',
        'Federal law compliance',
        'State law awareness'
      ]
    }
  ];

  const trustMetrics: TrustMetric[] = [
    {
      label: 'Customer Rating',
      value: '4.8/5',
      icon: StarIcon,
      color: 'text-yellow-500'
    },
    {
      label: 'Orders Shipped',
      value: '50,000+',
      icon: TruckIcon,
      color: 'text-blue-500'
    },
    {
      label: 'Years in Business',
      value: '4+',
      icon: CheckBadgeIcon,
      color: 'text-green-500'
    }
  ];

  if (variant === 'footer') {
    return (
      <div className="flex items-center justify-center space-x-6 text-gray-400">
        {securityBadges.slice(0, 4).map((badge) => (
          <div key={badge.id} className="flex items-center space-x-2">
            <badge.icon className="h-4 w-4" />
            <span className="text-xs font-medium">{badge.name}</span>
          </div>
        ))}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {securityBadges.map((badge) => (
            <div key={badge.id} className="text-center">
              <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                <badge.icon className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium text-gray-900">{badge.name}</p>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Your Security & Trust Matter
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We implement industry-leading security measures and maintain the highest 
            standards of quality and compliance to ensure your complete peace of mind.
          </p>
        </div>

        {/* Trust Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {trustMetrics.map((metric, index) => (
            <div key={index} className="text-center">
              <div className={`w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4 ${metric.color}`}>
                <metric.icon className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">{metric.value}</div>
              <div className="text-gray-600">{metric.label}</div>
            </div>
          ))}
        </div>

        {/* Security Badges */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {securityBadges.map((badge) => (
            <div key={badge.id} className="bg-white border border-gray-200 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <badge.icon className="h-8 w-8" />
              </div>
              
              <div className="flex items-center justify-center mb-2">
                <h3 className="text-lg font-semibold text-gray-900">{badge.name}</h3>
                {badge.verified && (
                  <CheckBadgeIcon className="h-5 w-5 text-green-500 ml-2" />
                )}
              </div>
              
              <p className="text-gray-600 text-sm mb-4">{badge.description}</p>
              
              <div className="text-left">
                <ul className="space-y-1">
                  {badge.details.map((detail, index) => (
                    <li key={index} className="text-xs text-gray-500 flex items-center">
                      <div className="w-1 h-1 bg-green-500 rounded-full mr-2"></div>
                      {detail}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Security Information */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Data Protection</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <LockClosedIcon className="h-5 w-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">All personal data encrypted with AES-256</span>
                </li>
                <li className="flex items-start">
                  <LockClosedIcon className="h-5 w-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">Payment information never stored on our servers</span>
                </li>
                <li className="flex items-start">
                  <LockClosedIcon className="h-5 w-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">Regular security audits and penetration testing</span>
                </li>
                <li className="flex items-start">
                  <LockClosedIcon className="h-5 w-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">GDPR and CCPA compliant privacy practices</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Quality Assurance</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <CheckBadgeIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">ISO 17025 accredited laboratory testing</span>
                </li>
                <li className="flex items-start">
                  <CheckBadgeIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">Certificate of Analysis (COA) for every product</span>
                </li>
                <li className="flex items-start">
                  <CheckBadgeIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">Batch tracking and quality control systems</span>
                </li>
                <li className="flex items-start">
                  <CheckBadgeIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-sm">Good Manufacturing Practices (GMP) certified</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Certifications */}
        <div className="mt-12 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Our Certifications & Partners</h3>
          <div className="flex items-center justify-center space-x-8 opacity-60">
            {/* Placeholder for certification logos */}
            <div className="w-20 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs font-medium text-gray-500">SSL</span>
            </div>
            <div className="w-20 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs font-medium text-gray-500">PCI DSS</span>
            </div>
            <div className="w-20 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs font-medium text-gray-500">ISO</span>
            </div>
            <div className="w-20 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs font-medium text-gray-500">GMP</span>
            </div>
            <div className="w-20 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs font-medium text-gray-500">USPS</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityBadges;
