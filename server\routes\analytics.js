const express = require('express');
const router = express.Router();
const analyticsController = require('../controllers/analyticsController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const {
  validateDashboardQuery,
  validateSalesAnalyticsQuery,
  validateCustomerAnalyticsQuery,
  validateProductAnalyticsQuery,
  validateSystemMetricsQuery,
  validateExportQuery,
  requireAnalyticsAccess,
  requireExportAccess,
  analyticsRateLimit,
  validateDateRange,
  sanitizeAnalyticsQuery,
  logAnalyticsAccess,
  checkSystemLoad
} = require('../middleware/analyticsValidation');

// All routes require authentication and admin privileges
router.use(authenticate);
router.use(requireAdmin);
router.use(requireAnalyticsAccess);
router.use(analyticsRateLimit);
router.use(sanitizeAnalyticsQuery);
router.use(logAnalyticsAccess);

// @route   GET /api/admin/analytics/dashboard
// @desc    Get dashboard overview statistics
// @access  Private (Admin/Manager)
router.get('/dashboard',
  validateDashboardQuery,
  validateDateRange,
  analyticsController.getDashboardOverview
);

// @route   GET /api/admin/analytics/sales
// @desc    Get sales analytics with time series data
// @access  Private (Admin/Manager)
router.get('/sales',
  validateSalesAnalyticsQuery,
  validateDateRange,
  checkSystemLoad,
  analyticsController.getSalesAnalytics
);

// @route   GET /api/admin/analytics/customers
// @desc    Get customer analytics and segmentation
// @access  Private (Admin/Manager)
router.get('/customers',
  validateCustomerAnalyticsQuery,
  validateDateRange,
  checkSystemLoad,
  analyticsController.getCustomerAnalytics
);

// @route   GET /api/admin/analytics/products
// @desc    Get product performance analytics
// @access  Private (Admin/Manager)
router.get('/products',
  validateProductAnalyticsQuery,
  validateDateRange,
  analyticsController.getProductAnalytics
);

// @route   GET /api/admin/analytics/system
// @desc    Get system performance metrics
// @access  Private (Admin/Manager)
router.get('/system',
  validateSystemMetricsQuery,
  validateDateRange,
  analyticsController.getSystemMetrics
);

// @route   GET /api/admin/analytics/export
// @desc    Export analytics data in various formats
// @access  Private (Manager+)
router.get('/export',
  requireExportAccess,
  validateExportQuery,
  validateDateRange,
  analyticsController.exportData
);

// Enhanced analytics routes
// @route   GET /api/admin/analytics/enhanced-dashboard
// @desc    Get enhanced dashboard analytics with comprehensive data
// @access  Private (Admin)
router.get('/enhanced-dashboard',
  checkSystemLoad,
  validateDashboardQuery,
  validateDateRange,
  analyticsController.getEnhancedDashboardAnalytics
);

// @route   GET /api/admin/analytics/realtime
// @desc    Get real-time analytics
// @access  Private (Admin)
router.get('/realtime',
  checkSystemLoad,
  analyticsController.getRealTimeAnalytics
);

// @route   GET /api/admin/analytics/customer-ltv
// @desc    Get customer lifetime value analytics
// @access  Private (Admin)
router.get('/customer-ltv',
  checkSystemLoad,
  validateCustomerAnalyticsQuery,
  analyticsController.getCustomerLTVAnalytics
);

// Enhanced export routes
// @route   POST /api/admin/analytics/export/excel
// @desc    Export analytics data to Excel format
// @access  Private (Admin)
router.post('/export/excel',
  requireExportAccess,
  validateExportQuery,
  validateDateRange,
  analyticsController.exportToExcel
);

// @route   POST /api/admin/analytics/export/csv
// @desc    Export analytics data to CSV format
// @access  Private (Admin)
router.post('/export/csv',
  requireExportAccess,
  validateExportQuery,
  validateDateRange,
  analyticsController.exportToCSV
);

module.exports = router;
