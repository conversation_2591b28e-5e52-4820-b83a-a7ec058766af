#!/usr/bin/env node

/**
 * Production Monitoring Setup
 * Sets up comprehensive monitoring for Nirvana Organics production environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Setting up Production Monitoring...\n');

// Configuration
const config = {
  logDir: '/var/log/nirvana-organics',
  monitoringDir: '/var/www/nirvana-organics/monitoring',
  healthCheckInterval: 60000, // 1 minute
  alertThresholds: {
    cpuUsage: 80,
    memoryUsage: 85,
    diskUsage: 90,
    responseTime: 5000
  }
};

// Create monitoring directories
function createDirectories() {
  console.log('📁 Creating monitoring directories...');
  
  const dirs = [
    config.logDir,
    config.monitoringDir,
    path.join(config.monitoringDir, 'logs'),
    path.join(config.monitoringDir, 'scripts')
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`   ✅ Created: ${dir}`);
    }
  });
}

// Create health check endpoint
function createHealthCheckEndpoint() {
  console.log('🏥 Creating health check endpoint...');
  
  const healthCheckCode = `
const express = require('express');
const { sequelize } = require('../server/config/database');
const SquareService = require('../server/services/squareService');

const router = express.Router();

// Basic health check
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      checks: {}
    };

    // Database check
    try {
      await sequelize.authenticate();
      health.checks.database = { status: 'healthy', responseTime: Date.now() };
    } catch (error) {
      health.checks.database = { status: 'unhealthy', error: error.message };
      health.status = 'unhealthy';
    }

    // Square API check
    try {
      const locations = await SquareService.getLocations();
      health.checks.square = { 
        status: 'healthy', 
        locationCount: locations.length,
        environment: process.env.SQUARE_ENVIRONMENT 
      };
    } catch (error) {
      health.checks.square = { status: 'unhealthy', error: error.message };
      health.status = 'degraded';
    }

    // Memory check
    const memUsage = process.memoryUsage();
    health.checks.memory = {
      status: memUsage.heapUsed < (memUsage.heapTotal * 0.9) ? 'healthy' : 'warning',
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    };

    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;
    
    res.status(statusCode).json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// Detailed system metrics
router.get('/metrics', (req, res) => {
  const metrics = {
    timestamp: new Date().toISOString(),
    process: {
      pid: process.pid,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    },
    system: {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      loadAverage: require('os').loadavg(),
      freeMemory: require('os').freemem(),
      totalMemory: require('os').totalmem()
    }
  };
  
  res.json(metrics);
});

module.exports = router;
`;

  const healthCheckPath = path.join(config.monitoringDir, 'health-check.js');
  fs.writeFileSync(healthCheckPath, healthCheckCode);
  console.log('   ✅ Health check endpoint created');
}

// Create monitoring script
function createMonitoringScript() {
  console.log('📊 Creating monitoring script...');
  
  const monitoringScript = `#!/bin/bash

# Nirvana Organics Production Monitoring Script
# Runs every minute via cron

LOG_FILE="${config.logDir}/monitoring.log"
ALERT_FILE="${config.logDir}/alerts.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Function to log messages
log_message() {
    echo "[$TIMESTAMP] $1" >> "$LOG_FILE"
}

# Function to send alert
send_alert() {
    echo "[$TIMESTAMP] ALERT: $1" >> "$ALERT_FILE"
    # Add email/webhook notification here if needed
}

# Check application health
check_health() {
    RESPONSE=$(curl -s -w "%{http_code}" http://localhost:5000/health)
    HTTP_CODE=\${RESPONSE: -3}
    
    if [ "$HTTP_CODE" != "200" ]; then
        send_alert "Application health check failed (HTTP $HTTP_CODE)"
        return 1
    fi
    
    log_message "Health check passed"
    return 0
}

# Check PM2 processes
check_pm2() {
    PM2_STATUS=$(pm2 jlist | jq -r '.[].pm2_env.status' 2>/dev/null)
    
    if [ -z "$PM2_STATUS" ]; then
        send_alert "PM2 processes not found"
        return 1
    fi
    
    if echo "$PM2_STATUS" | grep -q "stopped"; then
        send_alert "Some PM2 processes are stopped"
        return 1
    fi
    
    log_message "PM2 processes running normally"
    return 0
}

# Check disk space
check_disk_space() {
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$DISK_USAGE" -gt ${config.alertThresholds.diskUsage} ]; then
        send_alert "Disk usage is $DISK_USAGE% (threshold: ${config.alertThresholds.diskUsage}%)"
        return 1
    fi
    
    log_message "Disk usage: $DISK_USAGE%"
    return 0
}

# Check memory usage
check_memory() {
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$MEMORY_USAGE" -gt ${config.alertThresholds.memoryUsage} ]; then
        send_alert "Memory usage is $MEMORY_USAGE% (threshold: ${config.alertThresholds.memoryUsage}%)"
        return 1
    fi
    
    log_message "Memory usage: $MEMORY_USAGE%"
    return 0
}

# Run all checks
log_message "Starting monitoring checks"

check_health
check_pm2
check_disk_space
check_memory

log_message "Monitoring checks completed"
`;

  const scriptPath = path.join(config.monitoringDir, 'scripts', 'monitor.sh');
  fs.writeFileSync(scriptPath, monitoringScript);
  fs.chmodSync(scriptPath, '755');
  console.log('   ✅ Monitoring script created');
}

// Create log rotation configuration
function createLogRotation() {
  console.log('🔄 Setting up log rotation...');
  
  const logrotateConfig = `
${config.logDir}/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}

/var/www/nirvana-organics/current/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
`;

  const logrotateConfigPath = '/etc/logrotate.d/nirvana-organics';
  try {
    fs.writeFileSync(logrotateConfigPath, logrotateConfig);
    console.log('   ✅ Log rotation configured');
  } catch (error) {
    console.log('   ⚠️  Log rotation setup requires sudo access');
  }
}

// Setup cron job for monitoring
function setupCronJob() {
  console.log('⏰ Setting up cron job...');
  
  const cronJob = `# Nirvana Organics Monitoring
* * * * * ${path.join(config.monitoringDir, 'scripts', 'monitor.sh')}
0 2 * * * ${path.join(config.monitoringDir, 'scripts', 'cleanup-logs.sh')}
`;

  const cronPath = path.join(config.monitoringDir, 'crontab');
  fs.writeFileSync(cronPath, cronJob);
  
  console.log('   ✅ Cron configuration created');
  console.log('   ℹ️  To install: crontab ' + cronPath);
}

// Create cleanup script
function createCleanupScript() {
  console.log('🧹 Creating cleanup script...');
  
  const cleanupScript = `#!/bin/bash

# Cleanup old logs and temporary files
LOG_DIR="${config.logDir}"
APP_LOG_DIR="/var/www/nirvana-organics/current/logs"

# Remove logs older than 30 days
find "$LOG_DIR" -name "*.log" -mtime +30 -delete
find "$APP_LOG_DIR" -name "*.log" -mtime +7 -delete

# Clean up PM2 logs
pm2 flush

# Clean up temporary files
find /tmp -name "nirvana-*" -mtime +1 -delete

echo "$(date): Log cleanup completed" >> "$LOG_DIR/cleanup.log"
`;

  const cleanupPath = path.join(config.monitoringDir, 'scripts', 'cleanup-logs.sh');
  fs.writeFileSync(cleanupPath, cleanupScript);
  fs.chmodSync(cleanupPath, '755');
  console.log('   ✅ Cleanup script created');
}

// Main setup function
function main() {
  try {
    createDirectories();
    createHealthCheckEndpoint();
    createMonitoringScript();
    createLogRotation();
    setupCronJob();
    createCleanupScript();
    
    console.log('\n🎉 Monitoring setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Install cron job: crontab ' + path.join(config.monitoringDir, 'crontab'));
    console.log('   2. Add health check route to your Express app');
    console.log('   3. Configure alerting (email/webhook)');
    console.log('   4. Set up external monitoring (UptimeRobot, etc.)');
    console.log('\n🔗 Monitoring endpoints:');
    console.log('   Health: https://shopnirvanaorganics.com/health');
    console.log('   Metrics: https://shopnirvanaorganics.com/metrics');
    
  } catch (error) {
    console.error('❌ Monitoring setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup
main();
