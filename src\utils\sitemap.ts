// Sitemap generator utility
export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export const generateSitemap = (urls: SitemapUrl[]): string => {
  const siteUrl = 'https://nirvanaorganics.com';
  
  const urlElements = urls.map(url => {
    const fullUrl = url.loc.startsWith('http') ? url.loc : `${siteUrl}${url.loc}`;
    
    return `  <url>
    <loc>${fullUrl}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
};

export const getStaticSitemapUrls = (): SitemapUrl[] => {
  const currentDate = new Date().toISOString().split('T')[0];
  
  return [
    {
      loc: '/',
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 1.0
    },
    {
      loc: '/shop',
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 0.9
    },
    {
      loc: '/about',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.8
    },
    {
      loc: '/contact',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.7
    },
    {
      loc: '/privacy',
      lastmod: currentDate,
      changefreq: 'yearly',
      priority: 0.5
    },
    {
      loc: '/terms',
      lastmod: currentDate,
      changefreq: 'yearly',
      priority: 0.5
    },
    {
      loc: '/shipping',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      loc: '/returns',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.6
    },
    // Category pages
    {
      loc: '/shop?cannabinoid=CBD',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: '/shop?cannabinoid=THC-A',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: '/shop?cannabinoid=Delta-8',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: '/shop?cannabinoid=Delta-9',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: '/shop?bestSeller=true',
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 0.9
    },
    {
      loc: '/shop?featured=true',
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 0.9
    }
  ];
};

// Function to generate product URLs for sitemap
export const getProductSitemapUrls = (products: any[]): SitemapUrl[] => {
  return products.map(product => ({
    loc: `/product/${product.slug}`,
    lastmod: product.updatedAt ? new Date(product.updatedAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    changefreq: 'weekly' as const,
    priority: product.featured ? 0.9 : 0.7
  }));
};

// Function to generate category URLs for sitemap
export const getCategorySitemapUrls = (categories: any[]): SitemapUrl[] => {
  return categories.map(category => ({
    loc: `/shop/${category.slug}`,
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'weekly' as const,
    priority: 0.8
  }));
};
