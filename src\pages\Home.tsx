import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchFeaturedProducts, fetchBestSellerProducts } from '../store/slices/productSlice';
import HeroSection from '../components/home/<USER>';
import SEOHead from '../components/seo/SEOHead';
import SecurityBadges from '../components/trust/SecurityBadges';
import CustomerReviews from '../components/trust/CustomerReviews';
import ProductRecommendations from '../components/conversion/ProductRecommendations';
import { NewsletterCTA } from '../components/conversion/EnhancedCTA';
import USProductCategories from '../components/products/USProductCategories';
import RecentlyViewed from '../components/products/RecentlyViewed';

const Home: React.FC = () => {
  const dispatch = useAppDispatch();
  const { featuredProducts, bestSellerProducts, loading } = useAppSelector((state) => state.products);

  useEffect(() => {
    // Add timeout and error handling to prevent loading issues
    const fetchData = async () => {
      try {
        await Promise.allSettled([
          dispatch(fetchFeaturedProducts(8)),
          dispatch(fetchBestSellerProducts(8))
        ]);
      } catch (error) {
        console.warn('Failed to fetch products, continuing anyway:', error);
      }
    };

    const timeout = setTimeout(fetchData, 100);
    return () => clearTimeout(timeout);
  }, [dispatch]);

  const categories = [
    {
      name: 'CBD',
      image: '/images/categories/cbd.svg',
      href: '/shop?cannabinoid=CBD',
      description: 'Premium CBD products for wellness and relaxation'
    },
    {
      name: 'Delta-8',
      image: '/images/categories/delta-8.svg',
      href: '/shop?cannabinoid=Delta-8',
      description: 'Smooth Delta-8 products for a mild experience'
    },
    {
      name: 'Delta-9',
      image: '/images/categories/delta-9.svg',
      href: '/shop?cannabinoid=Delta-9',
      description: 'Traditional Delta-9 THC products'
    },
    {
      name: 'THC-A',
      image: '/images/categories/thc-a.svg',
      href: '/shop?cannabinoid=THC-A',
      description: 'High-quality THC-A flowers and concentrates'
    }
  ];

  return (
    <div className="min-h-screen">
      <SEOHead
        title="Premium Hemp-Derived Cannabis Products | Nirvana Organics"
        description="Discover premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide with free shipping over $100."
        keywords={[
          'hemp products',
          'cannabis products',
          'CBD products',
          'THC-A products',
          'Delta-8 products',
          'Delta-9 products',
          'hemp flowers',
          'cannabis chocolates',
          'pre-rolls',
          'diamond sauce',
          'cannabis vapes',
          'legal cannabis',
          'lab tested cannabis',
          'organic hemp products',
          'premium cannabis',
          'hemp delivery',
          'cannabis store online'
        ]}
        canonicalUrl="/"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "Store",
          "name": "Nirvana Organics",
          "description": "Premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes.",
          "url": "https://nirvanaorganics.com",
          "image": "https://nirvanaorganics.com/images/hero-banner-1.jpg",
          "priceRange": "$10-$200",
          "paymentAccepted": ["Credit Card", "PayPal"],
          "currenciesAccepted": "USD",
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Hemp Products",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Product",
                  "name": "Hemp Flowers",
                  "category": "Cannabis Products"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Product",
                  "name": "Cannabis Chocolates",
                  "category": "Edibles"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Product",
                  "name": "Pre-Rolls",
                  "category": "Cannabis Products"
                }
              }
            ]
          }
        }}
      />

      {/* Hero Section */}
      <HeroSection />

      {/* Legacy Hero Content - keeping for reference */}
      <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white" style={{ display: 'none' }}>
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative container mx-auto px-4 py-24">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 animate-fade-in">
              Premium Cannabis Products Delivered to Your Door Step
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Discover Nirvana Organics' premium hemp-derived cannabis products designed to enhance your lifestyle. 
              Explore our flowers, chocolates, pre-rolls, diamond sauce, and vapes for relaxation, focus, and well-being.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold">500k+</span>
                <span>Satisfied Customers</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold">Fast</span>
                <span>Discreet & Nationwide Delivery</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold">100%</span>
                <span>Organic & Certified Products</span>
              </div>
            </div>
            <Link
              to="/shop"
              className="inline-block bg-white text-primary-600 font-bold py-4 px-8 rounded-lg text-lg hover:bg-gray-100 transition-colors duration-200 transform hover:scale-105"
            >
              SHOP NOW
            </Link>
          </div>
        </div>
      </section>

      {/* US Product Categories */}
      <USProductCategories />

      {/* Featured Products Section */}
      <ProductRecommendations
        type="you_might_like"
        limit={4}
        className="bg-gray-50"
      />

      {/* Trending Products */}
      <ProductRecommendations
        type="trending"
        limit={4}
      />

      {/* Recently Viewed Products */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <RecentlyViewed maxItems={5} />
        </div>
      </section>

      {/* Security & Trust */}
      <SecurityBadges variant="full" />

      {/* Customer Reviews */}
      <CustomerReviews variant="testimonials" />

      {/* Newsletter Signup */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <NewsletterCTA
              onSubscribe={(email) => {
                console.log('Newsletter subscription:', email);
                // Handle newsletter subscription
              }}
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
