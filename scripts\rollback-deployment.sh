#!/bin/bash

# ============================================================================
# Deployment Rollback Script
# Rolls back to previous deployment in case of issues
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${CYAN}🔧 $1${NC}"; }

# Configuration
PROJECT_NAME="nirvana-organics-backend"
PROJECT_PATH="/var/www/$PROJECT_NAME"
BACKUP_PATH="/var/backups/$PROJECT_NAME"
LOG_FILE="/var/log/$PROJECT_NAME-rollback.log"

# Create log file
touch "$LOG_FILE"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "============================================================================"
echo "🔄 DEPLOYMENT ROLLBACK - NIRVANA ORGANICS"
echo "============================================================================"
echo "Started: $(date)"
echo "============================================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: Check current deployment
log_step "Step 1: Checking current deployment"

CURRENT_RELEASE=$(readlink "$PROJECT_PATH/current" | xargs basename)
log_info "Current release: $CURRENT_RELEASE"

# Get list of available releases
RELEASES=($(ls -1t "$PROJECT_PATH/releases/" | head -5))
log_info "Available releases:"
for i in "${!RELEASES[@]}"; do
    if [ "${RELEASES[$i]}" = "$CURRENT_RELEASE" ]; then
        echo "  $((i+1)). ${RELEASES[$i]} (CURRENT)"
    else
        echo "  $((i+1)). ${RELEASES[$i]}"
    fi
done

# Step 2: Select rollback target
log_step "Step 2: Selecting rollback target"

if [ ${#RELEASES[@]} -lt 2 ]; then
    log_error "No previous releases available for rollback"
    exit 1
fi

# Find previous release (skip current)
PREVIOUS_RELEASE=""
for release in "${RELEASES[@]}"; do
    if [ "$release" != "$CURRENT_RELEASE" ]; then
        PREVIOUS_RELEASE="$release"
        break
    fi
done

if [ -z "$PREVIOUS_RELEASE" ]; then
    log_error "No previous release found"
    exit 1
fi

log_info "Rollback target: $PREVIOUS_RELEASE"

# Confirm rollback
echo ""
log_warning "This will rollback from $CURRENT_RELEASE to $PREVIOUS_RELEASE"
read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Rollback cancelled by user"
    exit 0
fi

# Step 3: Create backup of current state
log_step "Step 3: Creating backup of current state"

ROLLBACK_BACKUP_DIR="$BACKUP_PATH/rollback_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$ROLLBACK_BACKUP_DIR"

# Backup current database
log_info "Backing up current database..."
if [ -f "$PROJECT_PATH/current/.env" ]; then
    DB_PASSWORD=$(grep "^DB_PASSWORD=" "$PROJECT_PATH/current/.env" | cut -d'=' -f2)
    DB_NAME=$(grep "^DB_NAME=" "$PROJECT_PATH/current/.env" | cut -d'=' -f2)
    DB_USER=$(grep "^DB_USER=" "$PROJECT_PATH/current/.env" | cut -d'=' -f2)
    
    mysqldump -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" | gzip > "$ROLLBACK_BACKUP_DIR/database_before_rollback.sql.gz"
    log_success "Database backup created"
else
    log_warning "Could not backup database - .env file not found"
fi

# Backup current application files
log_info "Backing up current application state..."
tar -czf "$ROLLBACK_BACKUP_DIR/application_before_rollback.tar.gz" -C "$PROJECT_PATH" current
log_success "Application backup created"

# Step 4: Stop application
log_step "Step 4: Stopping application"

log_info "Stopping PM2 processes..."
sudo -u nirvana pm2 stop all
log_success "Application stopped"

# Step 5: Switch to previous release
log_step "Step 5: Switching to previous release"

PREVIOUS_RELEASE_PATH="$PROJECT_PATH/releases/$PREVIOUS_RELEASE"

if [ ! -d "$PREVIOUS_RELEASE_PATH" ]; then
    log_error "Previous release directory not found: $PREVIOUS_RELEASE_PATH"
    log_info "Restarting current application..."
    sudo -u nirvana pm2 start all
    exit 1
fi

# Update symlink
log_info "Updating symlink to previous release..."
ln -sfn "$PREVIOUS_RELEASE_PATH" "$PROJECT_PATH/current"
log_success "Symlink updated"

# Step 6: Restore previous configuration
log_step "Step 6: Restoring previous configuration"

# Check if previous release has .env file
if [ -f "$PROJECT_PATH/current/.env" ]; then
    log_success "Previous environment configuration found"
else
    log_warning "No .env file in previous release"
    
    # Try to restore from backup
    if [ -f "$ROLLBACK_BACKUP_DIR/application_before_rollback.tar.gz" ]; then
        log_info "Extracting .env from backup..."
        tar -xzf "$ROLLBACK_BACKUP_DIR/application_before_rollback.tar.gz" -C /tmp current/.env
        cp /tmp/current/.env "$PROJECT_PATH/current/.env"
        chown nirvana:nirvana "$PROJECT_PATH/current/.env"
        log_success "Environment configuration restored from backup"
    else
        log_error "Could not restore environment configuration"
    fi
fi

# Step 7: Install dependencies for previous release
log_step "Step 7: Installing dependencies"

cd "$PROJECT_PATH/current"

log_info "Installing Node.js dependencies..."
sudo -u nirvana npm ci --production
log_success "Dependencies installed"

# Step 8: Database rollback (optional)
log_step "Step 8: Database rollback check"

echo ""
log_warning "Database rollback options:"
echo "1. Keep current database (recommended for minor rollbacks)"
echo "2. Restore database from backup (use for major rollbacks)"
echo "3. Skip database rollback"

read -p "Select option (1-3): " -n 1 -r DB_ROLLBACK_OPTION
echo

case $DB_ROLLBACK_OPTION in
    1)
        log_info "Keeping current database"
        ;;
    2)
        log_warning "Database rollback not implemented in this script"
        log_info "To restore database manually:"
        echo "  1. Find backup file in $ROLLBACK_BACKUP_DIR/"
        echo "  2. Use: $BACKUP_PATH/scripts/restore-database.sh <backup_file>"
        ;;
    3)
        log_info "Skipping database rollback"
        ;;
    *)
        log_info "Invalid option, keeping current database"
        ;;
esac

# Step 9: Start application
log_step "Step 9: Starting application"

log_info "Starting PM2 processes..."
sudo -u nirvana pm2 start ecosystem.config.js --env production
log_success "Application started"

# Wait for application to start
log_info "Waiting for application to start..."
sleep 10

# Step 10: Health check
log_step "Step 10: Running health checks"

# Check PM2 status
if sudo -u nirvana pm2 list | grep -q "online"; then
    log_success "PM2 processes are running"
else
    log_error "PM2 processes failed to start"
fi

# Check application health
if curl -f -s http://localhost:5000/health >/dev/null 2>&1; then
    log_success "Application health check passed"
else
    log_warning "Application health check failed"
fi

# Check HTTPS health
if curl -f -s https://shopnirvanaorganics.com/health >/dev/null 2>&1; then
    log_success "HTTPS health check passed"
else
    log_warning "HTTPS health check failed"
fi

# Step 11: Cleanup old releases
log_step "Step 11: Cleanup old releases"

# Keep only last 5 releases
RELEASES_TO_KEEP=5
RELEASE_COUNT=$(ls -1 "$PROJECT_PATH/releases/" | wc -l)

if [ "$RELEASE_COUNT" -gt "$RELEASES_TO_KEEP" ]; then
    log_info "Cleaning up old releases (keeping last $RELEASES_TO_KEEP)..."
    ls -1t "$PROJECT_PATH/releases/" | tail -n +$((RELEASES_TO_KEEP + 1)) | while read old_release; do
        rm -rf "$PROJECT_PATH/releases/$old_release"
        log_info "Removed old release: $old_release"
    done
    log_success "Old releases cleaned up"
else
    log_info "No old releases to clean up"
fi

# Final status
echo ""
echo "============================================================================"
log_success "🎉 ROLLBACK COMPLETED!"
echo "============================================================================"
echo "Completed: $(date)"
echo ""

log_info "Rollback Summary:"
echo "• Rolled back from: $CURRENT_RELEASE"
echo "• Rolled back to: $PREVIOUS_RELEASE"
echo "• Backup location: $ROLLBACK_BACKUP_DIR"
echo "• Application status: $(sudo -u nirvana pm2 list | grep nirvana | awk '{print $10}' | head -1)"

echo ""
log_info "Post-Rollback Checklist:"
echo "□ Test all critical application functionality"
echo "□ Verify database integrity"
echo "□ Check application logs for errors"
echo "□ Monitor application performance"
echo "□ Notify team of rollback completion"

echo ""
log_warning "Important Notes:"
echo "• Backup created at: $ROLLBACK_BACKUP_DIR"
echo "• Monitor application closely after rollback"
echo "• Review logs: sudo -u nirvana pm2 logs"
echo "• Check status: sudo -u nirvana pm2 status"

echo ""
log_info "Useful Commands:"
echo "• View logs: sudo -u nirvana pm2 logs"
echo "• Check status: sudo -u nirvana pm2 status"
echo "• Restart app: sudo -u nirvana pm2 restart all"
echo "• View rollback log: tail -f $LOG_FILE"

log_success "Rollback completed successfully! 🔄"
