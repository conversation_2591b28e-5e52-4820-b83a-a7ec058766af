<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="delta8Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#delta8Gradient)"/>
  
  <!-- Delta symbol design -->
  <g transform="translate(200,150)">
    <!-- Triangle (Delta symbol) -->
    <path d="M0,-50 L-40,30 L40,30 Z" fill="rgba(255,255,255,0.3)" stroke="rgba(255,255,255,0.5)" stroke-width="2"/>
    
    <!-- Inner triangle -->
    <path d="M0,-30 L-25,15 L25,15 Z" fill="rgba(255,255,255,0.2)"/>
    
    <!-- Decorative elements -->
    <circle cx="-60" cy="-20" r="8" fill="rgba(255,255,255,0.2)"/>
    <circle cx="60" cy="-20" r="8" fill="rgba(255,255,255,0.2)"/>
    <circle cx="-50" cy="40" r="6" fill="rgba(255,255,255,0.15)"/>
    <circle cx="50" cy="40" r="6" fill="rgba(255,255,255,0.15)"/>
  </g>
  
  <!-- Delta-8 Text -->
  <text x="200" y="250" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">DELTA-8</text>
  <text x="200" y="275" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="14">Smooth Experience</text>
</svg>
