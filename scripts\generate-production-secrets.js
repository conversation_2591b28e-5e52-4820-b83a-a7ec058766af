#!/usr/bin/env node

/**
 * Generate Production Secrets
 * Generates secure secrets for production environment
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔧${colors.reset} ${msg}`)
};

/**
 * Generate a secure random string
 */
function generateSecureSecret(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate a base64 encoded secret
 */
function generateBase64Secret(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

/**
 * Generate JWT secret (256-bit minimum)
 */
function generateJWTSecret() {
  return generateSecureSecret(32); // 256 bits
}

/**
 * Generate encryption key (256-bit)
 */
function generateEncryptionKey() {
  return generateSecureSecret(32); // 256 bits
}

/**
 * Generate session secret
 */
function generateSessionSecret() {
  return generateSecureSecret(32); // 256 bits
}

/**
 * Main function to generate all secrets
 */
function generateProductionSecrets() {
  console.log(`${colors.bright}🔐 Production Secrets Generator${colors.reset}`);
  console.log('='.repeat(50));

  const secrets = {
    JWT_SECRET: generateJWTSecret(),
    JWT_REFRESH_SECRET: generateJWTSecret(),
    SESSION_SECRET: generateSessionSecret(),
    ENCRYPTION_KEY: generateEncryptionKey()
  };

  log.step('Generated secure secrets:');
  console.log('');

  // Display secrets
  Object.entries(secrets).forEach(([key, value]) => {
    console.log(`${colors.yellow}${key}${colors.reset}=${value}`);
  });

  console.log('');
  log.warning('IMPORTANT SECURITY NOTES:');
  console.log('1. These secrets are cryptographically secure and unique');
  console.log('2. Store them securely and never commit to version control');
  console.log('3. Use different secrets for different environments');
  console.log('4. Rotate secrets periodically for enhanced security');
  console.log('');

  // Option to save to file
  const envProductionPath = path.join(__dirname, '../.env.production');
  
  if (fs.existsSync(envProductionPath)) {
    log.step('Updating .env.production with new secrets...');
    
    let envContent = fs.readFileSync(envProductionPath, 'utf8');
    
    // Replace placeholder secrets
    Object.entries(secrets).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      if (envContent.match(regex)) {
        envContent = envContent.replace(regex, `${key}=${value}`);
        log.success(`Updated ${key}`);
      } else {
        log.warning(`${key} not found in .env.production`);
      }
    });
    
    // Create backup
    const backupPath = `${envProductionPath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(envProductionPath));
    log.info(`Backup created: ${backupPath}`);
    
    // Write updated content
    fs.writeFileSync(envProductionPath, envContent);
    log.success('Updated .env.production with new secrets');
  } else {
    log.warning('.env.production not found. Please create it first.');
  }

  console.log('');
  log.step('Next steps:');
  console.log('1. Review and update .env.production with your production values');
  console.log('2. Update Square API credentials to production values');
  console.log('3. Configure production database credentials');
  console.log('4. Set up SSL certificates');
  console.log('5. Test all configurations before deployment');
  
  return secrets;
}

/**
 * Generate Square webhook signature key
 */
function generateSquareWebhookKey() {
  return generateBase64Secret(32);
}

/**
 * Generate VAPID keys for web push notifications
 */
function generateVAPIDKeys() {
  // Note: For production, you should use the web-push library to generate proper VAPID keys
  log.warning('VAPID keys should be generated using the web-push library for production');
  console.log('Run: npx web-push generate-vapid-keys');
}

// Run if called directly
if (require.main === module) {
  try {
    generateProductionSecrets();
    
    console.log('');
    log.step('Additional security recommendations:');
    console.log('• Generate new VAPID keys: npx web-push generate-vapid-keys');
    console.log('• Generate Square webhook signature key if needed');
    console.log('• Use environment-specific secrets for staging and production');
    console.log('• Set up secret rotation policies');
    
  } catch (error) {
    log.error(`Failed to generate secrets: ${error.message}`);
    process.exit(1);
  }
}

module.exports = {
  generateProductionSecrets,
  generateJWTSecret,
  generateEncryptionKey,
  generateSessionSecret,
  generateSquareWebhookKey,
  generateVAPIDKeys
};
