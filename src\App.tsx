import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { HelmetProvider } from 'react-helmet-async';
import { store } from './store';
import { useAppDispatch, useAppSelector } from './hooks/redux';
import { getProfile } from './store/slices/authSlice';
import { initBrowserCompatibility } from './utils/browserCompatibility';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import WhatsAppWidget from './components/common/WhatsAppWidget';

// Page Components
import Home from './pages/Home';
import Shop from './pages/Shop';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import VerifyEmail from './pages/VerifyEmail';
import AuthCallback from './pages/AuthCallback';
import Profile from './pages/Profile';
import Orders from './pages/Orders';
import OrderDetail from './pages/OrderDetail';
import About from './pages/About';
import Contact from './pages/Contact';
import FAQ from './pages/FAQ';
import TrackOrder from './pages/TrackOrder';
import Privacy from './pages/Privacy';
import Terms from './pages/Terms';
import Shipping from './pages/Shipping';
import Returns from './pages/Returns';
import ShopFinder from './pages/ShopFinder';
import SocialMedia from './pages/SocialMedia';
import NotFound from './pages/NotFound';

// Admin Components
import AdminDashboard from './pages/admin/Dashboard';
import AdminProducts from './pages/admin/Products';
import ProductNew from './pages/admin/ProductNew';
import ProductEdit from './pages/admin/ProductEdit';
import CategoryManagement from './pages/admin/CategoryManagement';
import AdminOrders from './pages/admin/Orders';

// Components
import ProtectedRoute from './components/common/ProtectedRoute';
import LoadingSpinner from './components/common/LoadingSpinner';
import Toast from './components/common/Toast';
import ErrorBoundary from './components/common/ErrorBoundary';

function AppContent() {
  const dispatch = useAppDispatch();
  const { token, loading } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (token) {
      // Add timeout to prevent infinite loading
      const profileTimeout = setTimeout(() => {
        dispatch(getProfile()).catch(() => {
          // If profile fails, continue anyway
          console.warn('Profile fetch failed, continuing...');
        });
      }, 100);

      return () => clearTimeout(profileTimeout);
    }
  }, [dispatch, token]);

  // Initialize browser compatibility checks
  useEffect(() => {
    initBrowserCompatibility().then((browserInfo) => {
      console.log('Browser compatibility initialized:', browserInfo);
    }).catch((error) => {
      console.warn('Browser compatibility check failed:', error);
    });
  }, []);

  // Signal that the app is fully loaded
  useEffect(() => {
    const signalAppLoaded = () => {
      // Mark the app as loaded after initial render
      setTimeout(() => {
        document.documentElement.classList.add('app-ready');
        window.dispatchEvent(new CustomEvent('react-app-loaded'));
      }, 200);
    };

    signalAppLoaded();
  }, []);

  // Remove loading screen to prevent infinite loading
  // if (loading) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center">
  //       <LoadingSpinner size="large" />
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen bg-gray-50 mobile-stable no-horizontal-scroll">
      <Header />
      <main className="flex-1 mobile-viewport-fix">
        <ErrorBoundary>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Home />} />
          <Route path="/shop" element={<Shop />} />
          <Route path="/shop/:category" element={<Shop />} />
          <Route path="/product/:slug" element={<ProductDetail />} />
          <Route path="/about" element={<About />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/track-order" element={<TrackOrder />} />
          <Route path="/privacy" element={<Privacy />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/shipping" element={<Shipping />} />
          <Route path="/returns" element={<Returns />} />
          <Route path="/shop-finder" element={<ShopFinder />} />
          <Route path="/social-media" element={<SocialMedia />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          <Route path="/verify-email" element={<VerifyEmail />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Protected Routes */}
          <Route path="/cart" element={
            <ProtectedRoute>
              <Cart />
            </ProtectedRoute>
          } />
          <Route path="/checkout" element={
            <ProtectedRoute>
              <Checkout />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          } />
          <Route path="/orders" element={
            <ProtectedRoute>
              <Orders />
            </ProtectedRoute>
          } />
          <Route path="/orders/:id" element={
            <ProtectedRoute>
              <OrderDetail />
            </ProtectedRoute>
          } />

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute requireAdmin>
              <AdminDashboard />
            </ProtectedRoute>
          } />
          <Route path="/admin/products" element={
            <ProtectedRoute requireAdmin>
              <AdminProducts />
            </ProtectedRoute>
          } />
          <Route path="/admin/products/new" element={
            <ProtectedRoute requireAdmin>
              <ProductNew />
            </ProtectedRoute>
          } />
          <Route path="/admin/products/:id/edit" element={
            <ProtectedRoute requireAdmin>
              <ProductEdit />
            </ProtectedRoute>
          } />
          <Route path="/admin/categories" element={
            <ProtectedRoute requireAdmin>
              <CategoryManagement />
            </ProtectedRoute>
          } />
          <Route path="/admin/orders" element={
            <ProtectedRoute requireAdmin>
              <AdminOrders />
            </ProtectedRoute>
          } />

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        </ErrorBoundary>
      </main>
      <Footer />
      <Toast />
      <WhatsAppWidget
        phoneNumber="+**********"
        businessName="Nirvana Organics"
        welcomeMessage="Hello! Welcome to Nirvana Organics. How can we help you today?"
      />
    </div>
  );
}

function App() {
  return (
    <HelmetProvider>
      <Provider store={store}>
        <Router>
          <AppContent />
        </Router>
      </Provider>
    </HelmetProvider>
  );
}

export default App;
