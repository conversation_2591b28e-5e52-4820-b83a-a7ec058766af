const { Banner } = require('../models');
const { Op } = require('sequelize');

// Get active banner for public display
const getActiveBanner = async (req, res) => {
  try {
    const banner = await Banner.findOne({
      where: {
        isActive: true,
        [Op.or]: [
          { expiresAt: null },
          { expiresAt: { [Op.gt]: new Date() } }
        ]
      },
      order: [['priority', 'DESC'], ['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: { banner }
    });

  } catch (error) {
    console.error('Get active banner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active banner',
      error: error.message
    });
  }
};

// Get all banners (Admin only)
const getBanners = async (req, res) => {
  try {
    const { page = 1, limit = 20, status = 'all' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    
    if (status === 'active') {
      whereClause.isActive = true;
    } else if (status === 'inactive') {
      whereClause.isActive = false;
    } else if (status === 'expired') {
      whereClause.expiresAt = { [Op.lt]: new Date() };
    }

    const banners = await Banner.findAndCountAll({
      where: whereClause,
      order: [['priority', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        banners: banners.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(banners.count / limit),
          totalBanners: banners.count,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get banners error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch banners',
      error: error.message
    });
  }
};

// Create new banner (Admin only)
const createBanner = async (req, res) => {
  try {
    const {
      title,
      message,
      type = 'info',
      backgroundColor = '#3B82F6',
      textColor = '#FFFFFF',
      linkUrl,
      linkText,
      priority = 1,
      expiresAt,
      isActive = true
    } = req.body;

    const banner = await Banner.create({
      title,
      message,
      type,
      backgroundColor,
      textColor,
      linkUrl,
      linkText,
      priority,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      isActive,
      createdBy: req.user.id
    });

    res.status(201).json({
      success: true,
      message: 'Banner created successfully',
      data: { banner }
    });

  } catch (error) {
    console.error('Create banner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create banner',
      error: error.message
    });
  }
};

// Update banner (Admin only)
const updateBanner = async (req, res) => {
  try {
    const { bannerId } = req.params;
    const {
      title,
      message,
      type,
      backgroundColor,
      textColor,
      linkUrl,
      linkText,
      priority,
      expiresAt,
      isActive
    } = req.body;

    const banner = await Banner.findByPk(bannerId);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    await banner.update({
      title,
      message,
      type,
      backgroundColor,
      textColor,
      linkUrl,
      linkText,
      priority,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      isActive,
      updatedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Banner updated successfully',
      data: { banner }
    });

  } catch (error) {
    console.error('Update banner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update banner',
      error: error.message
    });
  }
};

// Toggle banner status (Admin only)
const toggleBannerStatus = async (req, res) => {
  try {
    const { bannerId } = req.params;

    const banner = await Banner.findByPk(bannerId);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    await banner.update({
      isActive: !banner.isActive,
      updatedBy: req.user.id
    });

    res.json({
      success: true,
      message: `Banner ${banner.isActive ? 'activated' : 'deactivated'} successfully`,
      data: { banner }
    });

  } catch (error) {
    console.error('Toggle banner status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle banner status',
      error: error.message
    });
  }
};

// Delete banner (Admin only)
const deleteBanner = async (req, res) => {
  try {
    const { bannerId } = req.params;

    const banner = await Banner.findByPk(bannerId);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    await banner.destroy();

    res.json({
      success: true,
      message: 'Banner deleted successfully'
    });

  } catch (error) {
    console.error('Delete banner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete banner',
      error: error.message
    });
  }
};

// Preview banner (Admin only)
const previewBanner = async (req, res) => {
  try {
    const {
      title,
      message,
      type = 'info',
      backgroundColor = '#3B82F6',
      textColor = '#FFFFFF',
      linkUrl,
      linkText
    } = req.body;

    // Return banner data for preview without saving
    const previewData = {
      title,
      message,
      type,
      backgroundColor,
      textColor,
      linkUrl,
      linkText,
      isPreview: true
    };

    res.json({
      success: true,
      data: { banner: previewData }
    });

  } catch (error) {
    console.error('Preview banner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to preview banner',
      error: error.message
    });
  }
};

// Get banner analytics (Admin only)
const getBannerAnalytics = async (req, res) => {
  try {
    const { bannerId } = req.params;
    const { timeframe = '30d' } = req.query;

    const banner = await Banner.findByPk(bannerId);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    // Calculate date range
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { [Op.gte]: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case '30d':
        dateFilter = { [Op.gte]: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
        break;
      case '90d':
        dateFilter = { [Op.gte]: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) };
        break;
    }

    // For now, return mock analytics data
    // In a real implementation, you would track banner views and clicks
    const analytics = {
      views: Math.floor(Math.random() * 10000) + 1000,
      clicks: Math.floor(Math.random() * 500) + 50,
      clickThroughRate: 0,
      conversionRate: 0,
      timeframe
    };

    analytics.clickThroughRate = ((analytics.clicks / analytics.views) * 100).toFixed(2);
    analytics.conversionRate = (Math.random() * 5).toFixed(2); // Mock conversion rate

    res.json({
      success: true,
      data: { analytics }
    });

  } catch (error) {
    console.error('Get banner analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch banner analytics',
      error: error.message
    });
  }
};

module.exports = {
  getActiveBanner,
  getBanners,
  createBanner,
  updateBanner,
  toggleBannerStatus,
  deleteBanner,
  previewBanner,
  getBannerAnalytics
};
