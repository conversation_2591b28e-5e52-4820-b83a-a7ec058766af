const express = require('express');
const router = express.Router();
const { authenticate, requireCustomerOrAdmin } = require('../middleware/auth');
const { body, query, validationResult } = require('express-validator');
const userService = require('../services/userService');

/**
 * @route   GET /api/profile
 * @desc    Get current user profile with related data
 * @access  Private
 */
router.get('/', authenticate, async (req, res) => {
  try {
    const user = await userService.getUserById(req.user.id, true);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user },
      message: 'Profile retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route   PUT /api/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/', 
  authenticate,
  [
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('preferences')
      .optional()
      .isObject()
      .withMessage('Preferences must be an object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const updatedUser = await userService.updateProfile(req.user.id, req.body);

      res.json({
        success: true,
        data: { user: updatedUser },
        message: 'Profile updated successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   POST /api/profile/change-password
 * @desc    Change user password
 * @access  Private
 */
router.post('/change-password',
  authenticate,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Password confirmation does not match new password');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const result = await userService.changePassword(req.user.id, currentPassword, newPassword);

      res.json({
        success: true,
        data: result,
        message: 'Password changed successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   GET /api/profile/addresses
 * @desc    Get user addresses
 * @access  Private
 */
router.get('/addresses', authenticate, async (req, res) => {
  try {
    const addresses = await userService.getUserAddresses(req.user.id);

    res.json({
      success: true,
      data: { addresses },
      message: 'Addresses retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route   POST /api/profile/addresses
 * @desc    Add new address
 * @access  Private
 */
router.post('/addresses',
  authenticate,
  [
    body('type')
      .isIn(['billing', 'shipping'])
      .withMessage('Type must be billing or shipping'),
    body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('address1')
      .trim()
      .isLength({ min: 5, max: 100 })
      .withMessage('Address line 1 must be between 5 and 100 characters'),
    body('city')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('City must be between 2 and 50 characters'),
    body('state')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('State must be between 2 and 50 characters'),
    body('zipCode')
      .trim()
      .matches(/^\d{5}(-\d{4})?$/)
      .withMessage('Please provide a valid ZIP code'),
    body('country')
      .optional()
      .isLength({ min: 2, max: 2 })
      .withMessage('Country must be a 2-letter code'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('isDefault')
      .optional()
      .isBoolean()
      .withMessage('isDefault must be a boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const address = await userService.addAddress(req.user.id, req.body);

      res.status(201).json({
        success: true,
        data: { address },
        message: 'Address added successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   PUT /api/profile/addresses/:id
 * @desc    Update address
 * @access  Private
 */
router.put('/addresses/:id',
  authenticate,
  [
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('address1')
      .optional()
      .trim()
      .isLength({ min: 5, max: 100 })
      .withMessage('Address line 1 must be between 5 and 100 characters'),
    body('city')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('City must be between 2 and 50 characters'),
    body('state')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('State must be between 2 and 50 characters'),
    body('zipCode')
      .optional()
      .trim()
      .matches(/^\d{5}(-\d{4})?$/)
      .withMessage('Please provide a valid ZIP code'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('isDefault')
      .optional()
      .isBoolean()
      .withMessage('isDefault must be a boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const addressId = req.params.id;
      const address = await userService.updateAddress(req.user.id, addressId, req.body);

      res.json({
        success: true,
        data: { address },
        message: 'Address updated successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   DELETE /api/profile/addresses/:id
 * @desc    Delete address
 * @access  Private
 */
router.delete('/addresses/:id', authenticate, async (req, res) => {
  try {
    const addressId = req.params.id;
    const result = await userService.deleteAddress(req.user.id, addressId);

    res.json({
      success: true,
      data: result,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route   GET /api/profile/orders
 * @desc    Get user orders
 * @access  Private
 */
router.get('/orders',
  authenticate,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('status')
      .optional()
      .isIn(['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
      .withMessage('Invalid status')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        status: req.query.status
      };

      const result = await userService.getUserOrders(req.user.id, options);

      res.json({
        success: true,
        data: result,
        message: 'Orders retrieved successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   GET /api/profile/wishlist
 * @desc    Get user wishlist
 * @access  Private
 */
router.get('/wishlist', authenticate, async (req, res) => {
  try {
    const wishlist = await userService.getUserWishlist(req.user.id);

    res.json({
      success: true,
      data: { wishlist },
      message: 'Wishlist retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route   POST /api/profile/wishlist
 * @desc    Add item to wishlist
 * @access  Private
 */
router.post('/wishlist',
  authenticate,
  [
    body('productId')
      .isInt({ min: 1 })
      .withMessage('Product ID must be a positive integer'),
    body('variantId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Variant ID must be a positive integer'),
    body('notes')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Notes must not exceed 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { productId, variantId, notes } = req.body;
      const wishlistItem = await userService.addToWishlist(req.user.id, productId, variantId, notes);

      res.status(201).json({
        success: true,
        data: { wishlistItem },
        message: 'Item added to wishlist'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * @route   DELETE /api/profile/wishlist/:id
 * @desc    Remove item from wishlist
 * @access  Private
 */
router.delete('/wishlist/:id', authenticate, async (req, res) => {
  try {
    const wishlistItemId = req.params.id;
    const result = await userService.removeFromWishlist(req.user.id, wishlistItemId);

    res.json({
      success: true,
      data: result,
      message: 'Item removed from wishlist'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route   DELETE /api/profile/account
 * @desc    Deactivate user account
 * @access  Private
 */
router.delete('/account',
  authenticate,
  [
    body('password')
      .notEmpty()
      .withMessage('Password is required to deactivate account')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { password } = req.body;
      const result = await userService.deactivateAccount(req.user.id, password);

      res.json({
        success: true,
        data: result,
        message: 'Account deactivated successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

module.exports = router;
