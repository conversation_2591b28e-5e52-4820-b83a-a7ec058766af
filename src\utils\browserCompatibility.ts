/**
 * Browser Compatibility Utilities
 * Detects browser capabilities and provides fallbacks for unsupported features
 */

export interface BrowserInfo {
  name: string;
  version: string;
  isSupported: boolean;
  features: {
    es6: boolean;
    fetch: boolean;
    webp: boolean;
    intersectionObserver: boolean;
    customProperties: boolean;
    grid: boolean;
    flexbox: boolean;
  };
}

/**
 * Detect current browser and version
 */
export const detectBrowser = (): BrowserInfo => {
  const userAgent = navigator.userAgent;
  let name = 'Unknown';
  let version = '0';

  // Chrome
  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    name = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    version = match ? match[1] : '0';
  }
  // Firefox
  else if (userAgent.includes('Firefox')) {
    name = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    version = match ? match[1] : '0';
  }
  // Safari
  else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    name = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    version = match ? match[1] : '0';
  }
  // Edge
  else if (userAgent.includes('Edg')) {
    name = 'Edge';
    const match = userAgent.match(/Edg\/(\d+)/);
    version = match ? match[1] : '0';
  }

  const versionNumber = parseInt(version, 10);
  const isSupported = checkBrowserSupport(name, versionNumber);
  const features = checkFeatureSupport();

  return {
    name,
    version,
    isSupported,
    features
  };
};

/**
 * Check if browser version is supported
 */
const checkBrowserSupport = (name: string, version: number): boolean => {
  const minimumVersions = {
    Chrome: 120,
    Firefox: 115,
    Safari: 16,
    Edge: 120
  };

  return version >= (minimumVersions[name as keyof typeof minimumVersions] || 0);
};

/**
 * Check support for specific web features
 */
const checkFeatureSupport = () => {
  return {
    // ES6 features
    es6: typeof Symbol !== 'undefined' && typeof Promise !== 'undefined',
    
    // Fetch API
    fetch: typeof fetch !== 'undefined',
    
    // WebP image format
    webp: checkWebPSupport(),
    
    // Intersection Observer API
    intersectionObserver: 'IntersectionObserver' in window,
    
    // CSS Custom Properties
    customProperties: CSS.supports('color', 'var(--test)'),
    
    // CSS Grid
    grid: CSS.supports('display', 'grid'),
    
    // CSS Flexbox
    flexbox: CSS.supports('display', 'flex')
  };
};

/**
 * Check WebP image format support
 */
const checkWebPSupport = (): boolean => {
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  } catch {
    return false;
  }
};

/**
 * Display browser compatibility warning if needed
 */
export const showCompatibilityWarning = (browserInfo: BrowserInfo): void => {
  if (!browserInfo.isSupported) {
    const warningDiv = document.createElement('div');
    warningDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #f59e0b;
        color: white;
        padding: 12px;
        text-align: center;
        z-index: 9999;
        font-family: system-ui, -apple-system, sans-serif;
        font-size: 14px;
      ">
        <strong>Browser Compatibility Notice:</strong>
        You're using ${browserInfo.name} ${browserInfo.version}. 
        For the best experience, please update to the latest version or use a modern browser.
        <button onclick="this.parentElement.parentElement.remove()" style="
          margin-left: 10px;
          background: rgba(255,255,255,0.2);
          border: none;
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          cursor: pointer;
        ">×</button>
      </div>
    `;
    document.body.appendChild(warningDiv);
  }
};

/**
 * Simple fetch polyfill implementation
 */
const createFetchPolyfill = (): void => {
  if (typeof window !== 'undefined' && !window.fetch) {
    // Basic fetch polyfill using XMLHttpRequest
    (window as any).fetch = function(url: string, options: any = {}) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        const method = options.method || 'GET';

        xhr.open(method, url);

        // Set headers
        if (options.headers) {
          Object.keys(options.headers).forEach(key => {
            xhr.setRequestHeader(key, options.headers[key]);
          });
        }

        xhr.onload = () => {
          const response = {
            ok: xhr.status >= 200 && xhr.status < 300,
            status: xhr.status,
            statusText: xhr.statusText,
            json: () => Promise.resolve(JSON.parse(xhr.responseText)),
            text: () => Promise.resolve(xhr.responseText),
            headers: new Map()
          };
          resolve(response);
        };

        xhr.onerror = () => reject(new Error('Network error'));
        xhr.send(options.body || null);
      });
    };
  }
};

/**
 * Simple IntersectionObserver polyfill
 */
const createIntersectionObserverPolyfill = (): void => {
  if (typeof window !== 'undefined' && !window.IntersectionObserver) {
    // Basic IntersectionObserver polyfill
    (window as any).IntersectionObserver = class {
      private callback: Function;
      private elements: Element[] = [];

      constructor(callback: Function) {
        this.callback = callback;
      }

      observe(element: Element) {
        this.elements.push(element);
        // Immediately trigger callback for simplicity
        setTimeout(() => {
          this.callback([{
            target: element,
            isIntersecting: true,
            intersectionRatio: 1
          }]);
        }, 0);
      }

      unobserve(element: Element) {
        this.elements = this.elements.filter(el => el !== element);
      }

      disconnect() {
        this.elements = [];
      }
    };
  }
};

/**
 * Load polyfills for missing features
 */
export const loadPolyfills = async (browserInfo: BrowserInfo): Promise<void> => {
  const polyfillPromises: Promise<void>[] = [];

  // Fetch polyfill for older browsers
  if (!browserInfo.features.fetch) {
    polyfillPromises.push(
      new Promise<void>((resolve) => {
        try {
          createFetchPolyfill();
          console.log('Fetch polyfill loaded');
          resolve();
        } catch (error) {
          console.warn('Failed to load fetch polyfill:', error);
          resolve(); // Don't fail the entire process
        }
      })
    );
  }

  // Intersection Observer polyfill
  if (!browserInfo.features.intersectionObserver) {
    polyfillPromises.push(
      new Promise<void>((resolve) => {
        try {
          createIntersectionObserverPolyfill();
          console.log('IntersectionObserver polyfill loaded');
          resolve();
        } catch (error) {
          console.warn('Failed to load IntersectionObserver polyfill:', error);
          resolve(); // Don't fail the entire process
        }
      })
    );
  }

  await Promise.all(polyfillPromises);
};

/**
 * Initialize browser compatibility checks
 */
export const initBrowserCompatibility = async (): Promise<BrowserInfo> => {
  const browserInfo = detectBrowser();
  
  // Log browser info for debugging
  console.log('Browser detected:', browserInfo);
  
  // Show warning for unsupported browsers
  if (!browserInfo.isSupported) {
    showCompatibilityWarning(browserInfo);
  }
  
  // Load polyfills if needed
  await loadPolyfills(browserInfo);
  
  return browserInfo;
};

/**
 * Feature detection utilities
 */
export const features = {
  // Check if device supports touch
  hasTouch: (): boolean => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  // Check if device is mobile
  isMobile: (): boolean => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },

  // Check if device prefers reduced motion
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  // Check if device is in dark mode
  prefersDarkMode: (): boolean => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  },

  // Check viewport size
  getViewportSize: () => {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  },

  // Check if device supports hover
  canHover: (): boolean => {
    return window.matchMedia('(hover: hover)').matches;
  }
};

/**
 * Performance monitoring for different browsers
 */
export const performanceMonitor = {
  // Measure page load time
  getPageLoadTime: (): number => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return navigation ? navigation.loadEventEnd - navigation.fetchStart : 0;
  },

  // Measure resource load times
  getResourceTiming: (resourceName: string): PerformanceResourceTiming | null => {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    return resources.find(resource => resource.name.includes(resourceName)) || null;
  },

  // Monitor memory usage (Chrome only)
  getMemoryUsage: (): any => {
    return (performance as any).memory || null;
  }
};

export default {
  detectBrowser,
  showCompatibilityWarning,
  loadPolyfills,
  initBrowserCompatibility,
  features,
  performanceMonitor
};
