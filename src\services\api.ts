import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  LoginCredentials, 
  RegisterData, 
  User, 
  Product, 
  ProductFilters, 
  Category,
  Cart,
  Order,
  ApiResponse 
} from '../types';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post('/api/auth/refresh', {
            refreshToken,
          });

          const { token } = response.data.data;
          localStorage.setItem('token', token);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: LoginCredentials): Promise<AxiosResponse<ApiResponse<{ user: User; token: string; refreshToken: string }>>> =>
    api.post('/auth/login', credentials),

  register: (userData: RegisterData): Promise<AxiosResponse<ApiResponse<{ user: User; token: string; refreshToken: string }>>> =>
    api.post('/auth/register', userData),

  getProfile: (): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.get('/auth/profile'),

  updateProfile: (userData: Partial<User>): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put('/auth/profile', userData),

  refreshToken: (refreshToken: string): Promise<AxiosResponse<ApiResponse<{ token: string; refreshToken: string }>>> =>
    api.post('/auth/refresh', { refreshToken }),

  verifyEmail: (token: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/verify-email', { token }),

  requestPasswordReset: (email: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/request-password-reset', { email }),

  resetPassword: (token: string, password: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/reset-password', { token, password }),

  logout: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/logout'),
};

// Product API
export const productAPI = {
  getProducts: (filters: ProductFilters): Promise<AxiosResponse<ApiResponse<{ products: Product[]; pagination: any }>>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });
    return api.get(`/products?${params.toString()}`);
  },

  getProductBySlug: (slug: string): Promise<AxiosResponse<ApiResponse<{ product: Product; relatedProducts: Product[]; reviews: any[] }>>> =>
    api.get(`/products/${slug}`),

  getProductById: (id: number): Promise<AxiosResponse<ApiResponse<Product>>> =>
    api.get(`/products/id/${id}`),

  getFeaturedProducts: (limit: number = 8): Promise<AxiosResponse<ApiResponse<{ products: Product[] }>>> =>
    api.get(`/products/featured?limit=${limit}`),

  getBestSellerProducts: (limit: number = 8): Promise<AxiosResponse<ApiResponse<{ products: Product[] }>>> =>
    api.get(`/products/best-sellers?limit=${limit}`),

  searchProducts: (query: string, limit: number = 10): Promise<AxiosResponse<ApiResponse<{ products: Product[] }>>> =>
    api.get(`/products/search?q=${encodeURIComponent(query)}&limit=${limit}`),

  createProduct: (productData: Partial<Product>): Promise<AxiosResponse<ApiResponse<Product>>> =>
    api.post('/products', productData),

  updateProduct: (id: number, productData: Partial<Product>): Promise<AxiosResponse<ApiResponse<Product>>> =>
    api.put(`/products/${id}`, productData),

  deleteProduct: (id: number): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/products/${id}`),
};

// Category API
export const categoryAPI = {
  getCategories: (params?: any): Promise<AxiosResponse<ApiResponse<{ categories: Category[]; pagination?: any }>>> => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/categories${queryParams ? `?${queryParams}` : ''}`);
  },

  getCategoryBySlug: (slug: string): Promise<AxiosResponse<ApiResponse<{ category: Category; path: Category[]; children: Category[]; products: Product[] }>>> =>
    api.get(`/categories/${slug}`),

  getNavigationCategories: (): Promise<AxiosResponse<ApiResponse<{ categories: Category[] }>>> =>
    api.get('/categories/navigation'),

  createCategory: (categoryData: Partial<Category>): Promise<AxiosResponse<ApiResponse<{ category: Category }>>> =>
    api.post('/categories', categoryData),

  updateCategory: (id: string, categoryData: Partial<Category>): Promise<AxiosResponse<ApiResponse<{ category: Category }>>> =>
    api.put(`/categories/${id}`, categoryData),

  deleteCategory: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/categories/${id}`),
};

// Cart API
export const cartAPI = {
  getCart: (): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.get('/cart'),

  addToCart: (productId: string, variant?: any, quantity?: number): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.post('/cart/add', { productId, variant, quantity }),

  updateCartItem: (itemId: string, quantity: number): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.put(`/cart/items/${itemId}`, { quantity }),

  removeFromCart: (itemId: string): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.delete(`/cart/items/${itemId}`),

  clearCart: (): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.delete('/cart'),

  applyCoupon: (couponCode: string): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.post('/cart/coupon', { couponCode }),

  removeCoupon: (): Promise<AxiosResponse<ApiResponse<{ cart: Cart }>>> =>
    api.delete('/cart/coupon'),

  getCartSummary: (): Promise<AxiosResponse<ApiResponse<{ summary: { itemCount: number; total: number } }>>> =>
    api.get('/cart/summary'),
};

// Order API
export const orderAPI = {
  createOrder: (orderData: any): Promise<AxiosResponse<ApiResponse<{ order: Order; clientSecret?: string }>>> =>
    api.post('/orders', orderData),

  getUserOrders: (params?: any): Promise<AxiosResponse<ApiResponse<{ orders: Order[]; pagination: any }>>> => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/orders${queryParams ? `?${queryParams}` : ''}`);
  },

  getOrder: (id: string): Promise<AxiosResponse<ApiResponse<{ order: Order }>>> =>
    api.get(`/orders/${id}`),

  cancelOrder: (id: string, reason?: string): Promise<AxiosResponse<ApiResponse<{ order: Order }>>> =>
    api.put(`/orders/${id}/cancel`, { reason }),

  getAllOrders: (params?: any): Promise<AxiosResponse<ApiResponse<{ orders: Order[]; pagination: any }>>> => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/orders/all${queryParams ? `?${queryParams}` : ''}`);
  },

  updateOrderStatus: (id: string, status: string, trackingNumber?: string, notes?: string): Promise<AxiosResponse<ApiResponse<{ order: Order }>>> =>
    api.put(`/orders/${id}/status`, { status, trackingNumber, notes }),

  // Order tracking (public)
  trackOrder: (trackingNumber: string, email?: string): Promise<AxiosResponse<ApiResponse<{ order: Order }>>> => {
    const params = email ? `?email=${encodeURIComponent(email)}` : '';
    return api.get(`/orders/track/${trackingNumber}${params}`);
  },

  // Guest order creation
  createGuestOrder: (orderData: any): Promise<AxiosResponse<ApiResponse<{ order: Order; clientSecret?: string }>>> =>
    api.post('/guest/orders', orderData),

  // Guest order tracking
  trackGuestOrder: (orderNumber: string, email: string): Promise<AxiosResponse<ApiResponse<{ order: Order }>>> =>
    api.get(`/guest/orders/track?orderNumber=${orderNumber}&email=${encodeURIComponent(email)}`),
};

export default api;
