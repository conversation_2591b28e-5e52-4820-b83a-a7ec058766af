# ============================================================================
# Nirvana Organics Full-Stack Production Deployment Script (Windows)
# ============================================================================
# PowerShell script for Windows development environment
# ============================================================================

param(
    [switch]$Force,
    [switch]$SkipTests,
    [string]$Environment = "production"
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Cyan"
    White = "White"
}

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $Colors.Red
}

function Write-Step {
    param([string]$Message)
    Write-Host ""
    Write-Host "🔄 $Message" -ForegroundColor $Colors.Blue
    Write-Host "=" * 50
}

# Configuration
$ProjectRoot = Get-Location
$BuildDir = "dist"
$AdminBuildDir = "dist-admin"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Check if running in correct directory
if (-not (Test-Path "package.json")) {
    Write-Error "package.json not found. Please run this script from the project root directory."
    exit 1
}

Write-Step "Nirvana Organics Full-Stack Production Build"
Write-Info "Starting production build process..."
Write-Info "Timestamp: $Timestamp"
Write-Info "Environment: $Environment"

# Step 1: Environment Setup
Write-Step "Step 1: Environment Setup"

# Check if .env.production exists
if (-not (Test-Path ".env.production")) {
    Write-Error ".env.production file not found"
    exit 1
}

# Copy production environment
Copy-Item ".env.production" ".env" -Force
Write-Success "Production environment configured"

# Step 2: Install Dependencies
Write-Step "Step 2: Installing Dependencies"

Write-Info "Installing Node.js dependencies..."
try {
    npm ci --production
    Write-Success "Dependencies installed"
} catch {
    Write-Error "Failed to install dependencies: $_"
    exit 1
}

# Step 3: Validate Environment
Write-Step "Step 3: Environment Validation"

if (Test-Path "scripts/validate-env.js") {
    Write-Info "Validating environment variables..."
    try {
        node scripts/validate-env.js
        Write-Success "Environment validation passed"
    } catch {
        Write-Error "Environment validation failed: $_"
        if (-not $Force) {
            exit 1
        }
    }
} else {
    Write-Warning "Environment validation script not found, skipping..."
}

# Step 4: Test Square Configuration
Write-Step "Step 4: Testing Square Production Configuration"

if (Test-Path "scripts/test-square-production.js") {
    Write-Info "Testing Square production configuration..."
    try {
        node scripts/test-square-production.js
        Write-Success "Square production configuration verified"
    } catch {
        Write-Error "Square configuration test failed: $_"
        if (-not $Force) {
            exit 1
        }
    }
} else {
    Write-Warning "Square test script not found, skipping verification"
}

# Step 5: Run Tests (if not skipped)
if (-not $SkipTests) {
    Write-Step "Step 5: Running Tests"
    
    Write-Info "Running type checking..."
    try {
        npm run type-check
        Write-Success "Type checking passed"
    } catch {
        Write-Warning "Type checking failed, but continuing..."
    }
    
    Write-Info "Running linting..."
    try {
        npm run lint
        Write-Success "Linting passed"
    } catch {
        Write-Warning "Linting failed, but continuing..."
    }
} else {
    Write-Warning "Skipping tests as requested"
}

# Step 6: Clean Previous Builds
Write-Step "Step 6: Cleaning Previous Builds"

$DirsToClean = @($BuildDir, $AdminBuildDir)
foreach ($Dir in $DirsToClean) {
    if (Test-Path $Dir) {
        Remove-Item $Dir -Recurse -Force
        Write-Info "Removed: $Dir"
    }
}
Write-Success "Build directories cleaned"

# Step 7: Build Frontend
Write-Step "Step 7: Building Frontend"

Write-Info "Building React frontend for production..."
try {
    npm run build:frontend
    Write-Success "Frontend build completed"
} catch {
    Write-Error "Frontend build failed: $_"
    exit 1
}

# Verify build output
if (Test-Path $BuildDir) {
    $BuildFiles = Get-ChildItem $BuildDir -Recurse | Measure-Object
    Write-Info "Build output: $($BuildFiles.Count) files generated"
} else {
    Write-Error "Build directory not found after build"
    exit 1
}

# Step 8: Build Admin Panel
Write-Step "Step 8: Building Admin Panel"

if (Test-Path "vite.admin.config.ts") {
    Write-Info "Building admin panel..."
    try {
        npm run build:admin
        Write-Success "Admin panel build completed"
    } catch {
        Write-Warning "Admin panel build failed, but continuing..."
    }
} else {
    Write-Warning "Admin config not found, skipping admin build"
}

# Step 9: Generate Build Report
Write-Step "Step 9: Generating Build Report"

$BuildReport = @{
    timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
    environment = $Environment
    nodeVersion = node --version
    success = $true
    builds = @{}
}

# Analyze main build
if (Test-Path $BuildDir) {
    $MainBuildFiles = Get-ChildItem $BuildDir -Recurse -File
    $MainBuildSize = ($MainBuildFiles | Measure-Object -Property Length -Sum).Sum
    
    $BuildReport.builds.main = @{
        files = $MainBuildFiles.Count
        size = $MainBuildSize
        sizeFormatted = "{0:N2} MB" -f ($MainBuildSize / 1MB)
    }
}

# Analyze admin build
if (Test-Path $AdminBuildDir) {
    $AdminBuildFiles = Get-ChildItem $AdminBuildDir -Recurse -File
    $AdminBuildSize = ($AdminBuildFiles | Measure-Object -Property Length -Sum).Sum
    
    $BuildReport.builds.admin = @{
        files = $AdminBuildFiles.Count
        size = $AdminBuildSize
        sizeFormatted = "{0:N2} MB" -f ($AdminBuildSize / 1MB)
    }
}

# Save build report
$ReportPath = "build-report-$Timestamp.json"
$BuildReport | ConvertTo-Json -Depth 3 | Out-File $ReportPath -Encoding UTF8
Write-Success "Build report saved: $ReportPath"

# Step 10: Prepare Deployment Package
Write-Step "Step 10: Preparing Deployment Package"

$DeploymentFiles = @(
    "dist",
    "dist-admin",
    "server",
    "scripts",
    "public",
    "package.json",
    "ecosystem.config.js",
    ".env.production"
)

Write-Info "Deployment package will include:"
foreach ($File in $DeploymentFiles) {
    if (Test-Path $File) {
        Write-Info "  ✓ $File"
    } else {
        Write-Warning "  ✗ $File (not found)"
    }
}

# Step 11: Final Verification
Write-Step "Step 11: Final Verification"

# Check critical files
$CriticalFiles = @(
    "$BuildDir/index.html",
    "server/index.js",
    ".env"
)

$AllCriticalFilesExist = $true
foreach ($File in $CriticalFiles) {
    if (Test-Path $File) {
        Write-Success "✓ $File exists"
    } else {
        Write-Error "✗ $File missing"
        $AllCriticalFilesExist = $false
    }
}

if (-not $AllCriticalFilesExist) {
    Write-Error "Critical files missing. Build may be incomplete."
    exit 1
}

# Success Summary
Write-Step "Build Completed Successfully!"

Write-Host ""
Write-Host "🎉 Nirvana Organics Production Build Complete!" -ForegroundColor Green
Write-Host "=" * 50
Write-Host "✅ Frontend: Built and optimized" -ForegroundColor Green
Write-Host "✅ Admin Panel: Built successfully" -ForegroundColor Green
Write-Host "✅ Environment: Production configured" -ForegroundColor Green
Write-Host "✅ Square: Production configuration verified" -ForegroundColor Green
Write-Host ""
Write-Host "📦 Build Output:" -ForegroundColor Cyan
if ($BuildReport.builds.main) {
    Write-Host "   Main Build: $($BuildReport.builds.main.files) files ($($BuildReport.builds.main.sizeFormatted))"
}
if ($BuildReport.builds.admin) {
    Write-Host "   Admin Build: $($BuildReport.builds.admin.files) files ($($BuildReport.builds.admin.sizeFormatted))"
}
Write-Host ""
Write-Host "📋 Next Steps for Server Deployment:" -ForegroundColor Yellow
Write-Host "   1. Upload files to your production server"
Write-Host "   2. Run: npm ci --production"
Write-Host "   3. Run: npm run migrate:prod"
Write-Host "   4. Start with PM2: pm2 start ecosystem.config.js --env production"
Write-Host "   5. Configure Nginx reverse proxy"
Write-Host "   6. Set up SSL certificates"
Write-Host ""
Write-Host "🔧 Local Testing:" -ForegroundColor Cyan
Write-Host "   Preview build: npm run preview"
Write-Host "   Test integration: npm run test:integration"
Write-Host ""

Write-Success "Production build completed at $(Get-Date)"

# Create deployment instructions
$DeploymentInstructions = @"
# Nirvana Organics - Production Deployment Instructions

## Build Information
- Build Time: $(Get-Date)
- Environment: $Environment
- Node Version: $(node --version)

## Files Ready for Deployment
$($DeploymentFiles -join "`n")

## Server Deployment Steps

1. **Upload Files to Server**
   ```bash
   # Upload all files to /var/www/nirvana-organics/
   rsync -avz ./ user@server:/var/www/nirvana-organics/
   ```

2. **Install Dependencies**
   ```bash
   cd /var/www/nirvana-organics/
   npm ci --production
   ```

3. **Database Setup**
   ```bash
   npm run migrate:prod
   ```

4. **Start Application**
   ```bash
   pm2 start ecosystem.config.js --env production
   pm2 save
   ```

5. **Configure Web Server**
   - Set up Nginx reverse proxy
   - Configure SSL certificates
   - Set up domain routing

## Verification
- Health Check: https://shopnirvanaorganics.com/health
- Frontend: https://shopnirvanaorganics.com
- Admin: https://shopnirvanaorganics.com/admin
- API: https://shopnirvanaorganics.com/api

## Support
Refer to PRODUCTION_DEPLOYMENT_COMPLETE.md for detailed instructions.
"@

$InstructionsPath = "DEPLOYMENT_INSTRUCTIONS_$Timestamp.md"
$DeploymentInstructions | Out-File $InstructionsPath -Encoding UTF8
Write-Info "Deployment instructions saved: $InstructionsPath"
