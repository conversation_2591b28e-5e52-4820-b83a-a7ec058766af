import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '../../hooks/redux';
import { createProduct } from '../../store/slices/productSlice';
import { addToast } from '../../store/slices/uiSlice';
import { Product } from '../../types';
import ProductForm from '../../components/admin/ProductForm';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const ProductNew: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [creating, setCreating] = useState(false);

  const handleCreateProduct = async (productData: Partial<Product>) => {
    setCreating(true);
    try {
      await dispatch(createProduct(productData)).unwrap();

      dispatch(addToast({
        type: 'success',
        title: 'Product Created',
        message: 'Product has been created successfully'
      }));

      navigate('/admin/products');
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Creation Failed',
        message: error.message || 'Failed to create product'
      }));
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate('/admin/products')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Add New Product</h1>
              <p className="text-gray-600 mt-1">
                Create a new product with variants and detailed information
              </p>
            </div>
          </div>

          {/* Quick Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-blue-900 mb-2">Quick Tips</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Use descriptive product names that include key details like strain and potency</li>
              <li>• Add variants for different sizes, weights, or potencies of the same product</li>
              <li>• Include detailed descriptions to help customers make informed decisions</li>
              <li>• Set appropriate stock levels and low stock thresholds for inventory management</li>
            </ul>
          </div>
        </div>

        {/* Product Form */}
        <ProductForm
          isEditing={false}
          onSubmit={handleCreateProduct}
          loading={creating}
        />
      </div>
    </div>
  );
};

export default ProductNew;
