import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import <PERSON><PERSON><PERSON><PERSON> from './AdminApp'
import './index.css'

// Set admin mode globally
window.__ADMIN_MODE__ = true;

// Admin-specific configuration
const adminConfig = {
  apiUrl: import.meta.env.VITE_ADMIN_API_URL || '/api',
  appName: 'Nirvana Organics Admin Panel',
  version: '1.0.0',
  environment: import.meta.env.MODE,
  features: {
    analytics: true,
    userManagement: true,
    productManagement: true,
    orderManagement: true,
    categoryManagement: true,
    settingsManagement: true
  }
};

// Make config available globally
window.__ADMIN_CONFIG__ = adminConfig;

// Admin-specific error handling
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in admin panel:', event.reason);
  // You could send this to an admin-specific error tracking service
});

window.addEventListener('error', (event) => {
  console.error('Unhandled error in admin panel:', event.error);
  // You could send this to an admin-specific error tracking service
});

// Performance monitoring for admin panel
if (import.meta.env.PROD) {
  // Track admin panel performance
  window.addEventListener('load', () => {
    const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    console.log('Admin Panel Load Time:', perfData.loadEventEnd - perfData.fetchStart, 'ms');
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <AdminApp />
  </React.StrictMode>,
)
