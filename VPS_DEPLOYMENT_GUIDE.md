# VPS Deployment Guide
## Nirvana Organics E-commerce Platform

### 🎯 Overview

This guide provides complete instructions for deploying the Nirvana Organics backend to a Virtual Private Server (VPS). The deployment includes automated scripts for server setup, application deployment, SSL configuration, and production optimization.

## 📋 Prerequisites

### VPS Requirements
- **OS**: Ubuntu 20.04 LTS or newer
- **RAM**: Minimum 2GB (4GB recommended)
- **Storage**: Minimum 20GB SSD
- **CPU**: 2 cores minimum
- **Network**: Public IP with ports 80, 443, 22 open

### Domain Requirements
- Domain name pointing to VPS IP address
- DNS A records configured:
  - `shopnirvanaorganics.com` → VPS IP
  - `www.shopnirvanaorganics.com` → VPS IP

### Required Credentials
- VPS root/sudo access
- MySQL database credentials
- Square production API credentials
- Email service credentials
- SSL certificate email address

## 🚀 Quick Deployment (Automated)

### Step 1: Prepare Deployment Package

On your local machine:

```bash
# Create deployment package
npm run package:deployment

# This creates: deployment-package/nirvana-organics-backend-production-v1.0.0.tar.gz
```

### Step 2: Upload to VPS

```bash
# Upload deployment package to VPS
scp deployment-package/nirvana-organics-backend-production-*.tar.gz root@YOUR_VPS_IP:/root/

# SSH into VPS
ssh root@YOUR_VPS_IP
```

### Step 3: Run Automated Deployment

```bash
# Extract deployment package
tar -xzf nirvana-organics-backend-production-*.tar.gz
cd nirvana-organics-backend-production

# Make deployment script executable
chmod +x scripts/vps-deployment.sh

# Run automated deployment
sudo ./scripts/vps-deployment.sh
```

The script will:
- Update system and install prerequisites
- Install Node.js 18.x and PM2
- Configure MySQL database
- Setup firewall and security
- Extract and configure application
- Setup Nginx reverse proxy
- Obtain SSL certificates
- Start application with PM2
- Configure monitoring and logging

## 📝 Manual Deployment (Step-by-Step)

If you prefer manual control or need to troubleshoot:

### Step 1: System Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install prerequisites
sudo apt install -y curl wget git nginx mysql-server certbot \
    python3-certbot-nginx ufw fail2ban htop unzip build-essential
```

### Step 2: Install Node.js and PM2

```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Verify installations
node --version
npm --version
pm2 --version
```

### Step 3: Configure MySQL

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE u106832845_nirvana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'u106832845_root'@'localhost' IDENTIFIED BY 'YOUR_SECURE_PASSWORD';
GRANT ALL PRIVILEGES ON u106832845_nirvana.* TO 'u106832845_root'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Step 4: Configure Firewall

```bash
# Configure UFW firewall
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable
```

### Step 5: Setup Application

```bash
# Create project structure
sudo mkdir -p /var/www/nirvana-organics-backend/{current,shared/{logs,uploads,config},releases}

# Create application user
sudo useradd -r -s /bin/bash -d /var/www/nirvana-organics-backend nirvana

# Extract deployment package
RELEASE_DIR="/var/www/nirvana-organics-backend/releases/$(date +%Y%m%d_%H%M%S)"
sudo mkdir -p "$RELEASE_DIR"
sudo tar -xzf nirvana-organics-backend-production-*.tar.gz -C "$RELEASE_DIR" --strip-components=1

# Set permissions
sudo chown -R nirvana:nirvana /var/www/nirvana-organics-backend
sudo ln -sfn "$RELEASE_DIR" /var/www/nirvana-organics-backend/current
```

### Step 6: Configure Application

```bash
cd /var/www/nirvana-organics-backend/current

# Install dependencies
sudo -u nirvana npm ci --production

# Configure environment
sudo -u nirvana cp .env.production.final .env

# Update database credentials in .env
sudo -u nirvana nano .env
# Update DB_PASSWORD and other production values

# Generate production secrets
sudo -u nirvana npm run generate:secrets

# Setup database
sudo -u nirvana npm run setup:database
sudo -u nirvana npm run migrate:prod
```

### Step 7: Configure Nginx

```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/nirvana-organics-backend
```

Add the Nginx configuration (see full config in deployment script).

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/nirvana-organics-backend /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test and reload
sudo nginx -t
sudo systemctl reload nginx
```

### Step 8: Setup SSL

```bash
# Obtain SSL certificates
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com \
    --non-interactive --agree-tos --email <EMAIL> --redirect

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx" | sudo crontab -
```

### Step 9: Start Application

```bash
# Start with PM2
sudo -u nirvana pm2 start ecosystem.config.js --env production

# Save PM2 configuration
sudo -u nirvana pm2 save

# Setup PM2 startup
sudo pm2 startup systemd -u nirvana --hp /var/www/nirvana-organics-backend
```

## 🔧 Configuration Updates

### Critical Configuration Items

After deployment, update these in `/var/www/nirvana-organics-backend/current/.env`:

#### 1. Square Production API (CRITICAL)
```bash
SQUARE_APPLICATION_ID=sq0idp-YOUR_PRODUCTION_APP_ID
SQUARE_ACCESS_TOKEN=YOUR_PRODUCTION_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_PRODUCTION_LOCATION_ID
SQUARE_WEBHOOK_SIGNATURE_KEY=YOUR_PRODUCTION_WEBHOOK_KEY
```

#### 2. External API Keys
```bash
SHIPPING_API_KEY=your_production_shipping_key
USPS_API_KEY=your_production_usps_key
WHATSAPP_ACCESS_TOKEN=your_production_whatsapp_token
```

#### 3. Security Secrets
```bash
# These should be auto-generated by the deployment script
JWT_SECRET=your_generated_jwt_secret
JWT_REFRESH_SECRET=your_generated_refresh_secret
SESSION_SECRET=your_generated_session_secret
```

### Restart After Configuration Changes

```bash
# Restart application
sudo -u nirvana pm2 restart all

# Verify status
sudo -u nirvana pm2 status
```

## ✅ Post-Deployment Verification

### 1. Application Health Check

```bash
# Test local health endpoint
curl http://localhost:5000/health

# Test HTTPS endpoint
curl https://shopnirvanaorganics.com/health
```

### 2. SSL Verification

```bash
# Run SSL verification script
sudo -u nirvana npm run verify:ssl

# Check SSL rating
# Visit: https://www.ssllabs.com/ssltest/
```

### 3. Service Validation

```bash
# Validate third-party services
sudo -u nirvana npm run setup:services

# Test database connection
sudo -u nirvana npm run test:database
```

### 4. PM2 Status

```bash
# Check PM2 status
sudo -u nirvana pm2 status
sudo -u nirvana pm2 logs
sudo -u nirvana pm2 monit
```

## 📊 Monitoring and Maintenance

### Log Files

```bash
# Application logs
sudo -u nirvana pm2 logs

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
sudo journalctl -u nginx -f
sudo journalctl -u mysql -f
```

### Regular Maintenance

```bash
# Update system packages (monthly)
sudo apt update && sudo apt upgrade

# Restart services (as needed)
sudo -u nirvana pm2 restart all
sudo systemctl restart nginx

# Check disk space
df -h

# Check memory usage
free -h

# Check SSL certificate expiration
sudo certbot certificates
```

### Backup Procedures

```bash
# Database backup
mysqldump -u u106832845_root -p u106832845_nirvana > backup_$(date +%Y%m%d).sql

# Application files backup
tar -czf app_backup_$(date +%Y%m%d).tar.gz /var/www/nirvana-organics-backend/current

# Uploads backup
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /var/www/nirvana-organics-backend/shared/uploads
```

## 🆘 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check PM2 logs
sudo -u nirvana pm2 logs

# Check environment variables
sudo -u nirvana cat /var/www/nirvana-organics-backend/current/.env

# Restart application
sudo -u nirvana pm2 restart all
```

#### Database Connection Issues
```bash
# Test database connection
sudo -u nirvana npm run test:database

# Check MySQL status
sudo systemctl status mysql

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log
```

#### SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew

# Check Nginx configuration
sudo nginx -t
```

#### High Memory Usage
```bash
# Check memory usage
free -h
sudo -u nirvana pm2 monit

# Restart application if needed
sudo -u nirvana pm2 restart all
```

## 📞 Support

For additional support:

1. Check application logs: `sudo -u nirvana pm2 logs`
2. Review deployment logs: `/var/log/nirvana-organics-backend-deployment.log`
3. Verify configuration: Run validation scripts
4. Check system resources: `htop`, `df -h`, `free -h`

## 🎉 Success Checklist

- [ ] VPS accessible via SSH
- [ ] Domain pointing to VPS IP
- [ ] Application responding on HTTPS
- [ ] SSL certificate valid and auto-renewing
- [ ] Database connected and migrated
- [ ] PM2 managing application processes
- [ ] Nginx reverse proxy configured
- [ ] Firewall configured and active
- [ ] Monitoring and logging operational
- [ ] Backup procedures documented

**Congratulations! Your Nirvana Organics backend is now deployed and running in production! 🚀**
