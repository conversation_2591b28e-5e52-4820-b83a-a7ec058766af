import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchProducts, createProduct, updateProduct, deleteProduct } from '../../store/slices/productSlice';
import { addToast } from '../../store/slices/uiSlice';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminPageHeader from '../../components/admin/AdminPageHeader';
import AdminDataTable, { TableColumn, TableAction } from '../../components/admin/AdminDataTable';
import {
  CubeIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PhotoIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline';

interface Product {
  id: number;
  name: string;
  slug: string;
  sku: string;
  price: number;
  comparePrice?: number;
  quantity: number;
  category: string;
  status: 'active' | 'inactive' | 'draft';
  featured: boolean;
  images: string[];
  createdAt: string;
  updatedAt: string;
}

interface ProductFilters {
  search: string;
  category: string;
  status: string;
  stockLevel: string;
  priceRange: string;
  featured: string;
}

const ProductManagement: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { products, loading, pagination } = useAppSelector((state) => state.products);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<ProductFilters>({
    search: '',
    category: 'all',
    status: 'all',
    stockLevel: 'all',
    priceRange: 'all',
    featured: 'all'
  });

  // Fetch products from API
  const fetchProductsData = async () => {
    try {
      const filterParams: any = {
        page: pagination.current,
        limit: pagination.pageSize
      };

      // Add filters if they're not 'all'
      if (filters.search) filterParams.search = filters.search;
      if (filters.category !== 'all') filterParams.category = filters.category;
      if (filters.status !== 'all') filterParams.status = filters.status;
      if (filters.featured !== 'all') filterParams.featured = filters.featured;

      await dispatch(fetchProducts(filterParams)).unwrap();
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to fetch products'
      }));
    }
  };

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProductsData();
    }, filters.search ? 500 : 0); // 500ms delay for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [dispatch, pagination.current, pagination.pageSize, filters]);

  // Alias for easier use in action handlers
  const fetchProducts = fetchProductsData;

  // Calculate inventory stats
  const inventoryStats = React.useMemo(() => {
    const totalProducts = products.length;
    const activeProducts = products.filter(p => p.status === 'active').length;
    const lowStockProducts = products.filter(p => p.quantity <= 10).length;
    const outOfStockProducts = products.filter(p => p.quantity === 0).length;

    return {
      totalProducts,
      activeProducts,
      lowStockProducts,
      outOfStockProducts
    };
  }, [products]);

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    // Reset to first page when searching
    setPagination(prev => ({ ...prev, current: 1 }));
    // Trigger refetch with new search term
    fetchProductsData();
  };

  const handleFilterChange = (key: keyof ProductFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    // Reset to first page when filtering
    setPagination(prev => ({ ...prev, current: 1 }));
    // Trigger refetch with new filters
    fetchProductsData();
  };

  const handleBulkAction = async (action: string, productIds: string[]) => {
    if (productIds.length === 0) {
      dispatch(addToast({
        type: 'warning',
        title: 'No Selection',
        message: 'Please select products to perform bulk actions'
      }));
      return;
    }

    const confirmMessage = `Are you sure you want to ${action} ${productIds.length} product(s)?`;
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const promises = productIds.map(async (id) => {
        switch (action) {
          case 'activate':
            return dispatch(updateProduct({
              id: parseInt(id),
              productData: { status: 'active' }
            })).unwrap();
          case 'deactivate':
            return dispatch(updateProduct({
              id: parseInt(id),
              productData: { status: 'draft' }
            })).unwrap();
          case 'feature':
            return dispatch(updateProduct({
              id: parseInt(id),
              productData: { featured: true }
            })).unwrap();
          case 'delete':
            return dispatch(deleteProduct(parseInt(id))).unwrap();
          default:
            throw new Error(`Unknown action: ${action}`);
        }
      });

      await Promise.all(promises);

      dispatch(addToast({
        type: 'success',
        title: 'Bulk Action Completed',
        message: `Successfully ${action}d ${productIds.length} product(s)`
      }));

      setSelectedProducts([]);
      fetchProducts();
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Bulk Action Failed',
        message: error.message || `Failed to ${action} products`
      }));
    }
  };

  const handleProductAction = async (action: string, product: Product) => {
    switch (action) {
      case 'view':
        navigate(`/admin/products/${product.id}`);
        break;
      case 'edit':
        navigate(`/admin/products/${product.id}/edit`);
        break;
      case 'duplicate':
        try {
          const duplicateData = {
            ...product,
            name: `${product.name} (Copy)`,
            slug: `${product.slug}-copy-${Date.now()}`,
            sku: `${product.sku}-copy`
          };
          delete duplicateData.id;
          await dispatch(createProduct(duplicateData)).unwrap();
          dispatch(addToast({
            type: 'success',
            title: 'Product Duplicated',
            message: 'Product has been duplicated successfully'
          }));
          fetchProducts();
        } catch (error: any) {
          dispatch(addToast({
            type: 'error',
            title: 'Duplication Failed',
            message: error.message || 'Failed to duplicate product'
          }));
        }
        break;
      case 'delete':
        if (window.confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
          try {
            await dispatch(deleteProduct(product.id)).unwrap();
            dispatch(addToast({
              type: 'success',
              title: 'Product Deleted',
              message: 'Product has been deleted successfully'
            }));
            fetchProducts();
          } catch (error: any) {
            dispatch(addToast({
              type: 'error',
              title: 'Deletion Failed',
              message: error.message || 'Failed to delete product'
            }));
          }
        }
        break;
      case 'toggle-status':
        try {
          const newStatus = product.status === 'active' ? 'draft' : 'active';
          await dispatch(updateProduct({
            id: product.id,
            productData: { status: newStatus }
          })).unwrap();
          dispatch(addToast({
            type: 'success',
            title: 'Status Updated',
            message: `Product has been ${newStatus === 'active' ? 'activated' : 'deactivated'}`
          }));
          fetchProducts();
        } catch (error: any) {
          dispatch(addToast({
            type: 'error',
            title: 'Update Failed',
            message: error.message || 'Failed to update product status'
          }));
        }
        break;
      case 'toggle-featured':
        try {
          const newFeatured = !product.featured;
          await dispatch(updateProduct({
            id: product.id,
            productData: { featured: newFeatured }
          })).unwrap();
          dispatch(addToast({
            type: 'success',
            title: 'Featured Status Updated',
            message: `Product has been ${newFeatured ? 'added to' : 'removed from'} featured products`
          }));
          fetchProducts();
        } catch (error: any) {
          dispatch(addToast({
            type: 'error',
            title: 'Update Failed',
            message: error.message || 'Failed to update featured status'
          }));
        }
        break;
    }
  };

  const getStockStatus = (quantity: number) => {
    if (quantity === 0) {
      return { label: 'Out of Stock', color: 'bg-red-100 text-red-800', icon: XCircleIcon };
    } else if (quantity <= 10) {
      return { label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800', icon: ExclamationTriangleIcon };
    } else {
      return { label: 'In Stock', color: 'bg-green-100 text-green-800', icon: CheckCircleIcon };
    }
  };

  const columns: TableColumn<Product>[] = [
    {
      key: 'product',
      title: 'Product',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-12 w-12">
            {record.images.length > 0 ? (
              <img
                className="h-12 w-12 rounded-lg object-cover"
                src={record.images[0]}
                alt={record.name}
                onError={(e) => {
                  e.currentTarget.src = '/placeholder-product.png';
                }}
              />
            ) : (
              <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                <PhotoIcon className="h-6 w-6 text-gray-400" />
              </div>
            )}
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{record.name}</div>
            <div className="text-sm text-gray-500">SKU: {record.sku}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
          {value}
        </span>
      )
    },
    {
      key: 'price',
      title: 'Price',
      align: 'right',
      render: (value, record) => (
        <div className="text-right">
          <div className="text-sm font-medium text-gray-900">${value.toFixed(2)}</div>
          {record.comparePrice && (
            <div className="text-xs text-gray-500 line-through">
              ${record.comparePrice.toFixed(2)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'quantity',
      title: 'Stock',
      align: 'center',
      render: (value) => {
        const status = getStockStatus(value);
        const IconComponent = status.icon;
        return (
          <div className="flex items-center justify-center">
            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${status.color}`}>
              <IconComponent className="h-3 w-3 mr-1" />
              {value} {status.label}
            </span>
          </div>
        );
      }
    },
    {
      key: 'status',
      title: 'Status',
      render: (value) => {
        const statusColors = {
          active: 'bg-green-100 text-green-800',
          inactive: 'bg-red-100 text-red-800',
          draft: 'bg-gray-100 text-gray-800'
        };
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[value]}`}>
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </span>
        );
      }
    },
    {
      key: 'featured',
      title: 'Featured',
      align: 'center',
      render: (value) => (
        <div className="flex justify-center">
          {value ? (
            <CheckCircleIcon className="h-5 w-5 text-green-500" />
          ) : (
            <XCircleIcon className="h-5 w-5 text-gray-300" />
          )}
        </div>
      )
    },
    {
      key: 'updatedAt',
      title: 'Last Updated',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  const actions: TableAction<Product>[] = [
    {
      label: 'View Details',
      onClick: (product) => handleProductAction('view', product),
      icon: EyeIcon
    },
    {
      label: 'Edit Product',
      onClick: (product) => handleProductAction('edit', product),
      icon: PencilIcon
    },
    {
      label: 'Duplicate',
      onClick: (product) => handleProductAction('duplicate', product)
    },
    {
      label: product => product.status === 'active' ? 'Deactivate' : 'Activate',
      onClick: (product) => handleProductAction('toggle-status', product),
      variant: product => product.status === 'active' ? 'secondary' : 'primary'
    },
    {
      label: product => product.featured ? 'Remove from Featured' : 'Add to Featured',
      onClick: (product) => handleProductAction('toggle-featured', product)
    },
    {
      label: 'Delete Product',
      onClick: (product) => handleProductAction('delete', product),
      icon: TrashIcon,
      variant: 'danger'
    }
  ];

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Product Management"
        subtitle="Manage your product catalog, inventory, and pricing"
        stats={[
          {
            label: 'Total Products',
            value: inventoryStats.totalProducts.toString()
          },
          {
            label: 'Active Products',
            value: inventoryStats.activeProducts.toString(),
            change: inventoryStats.activeProducts > 0 ? { value: 2.1, type: 'increase' } : undefined
          },
          {
            label: 'Low Stock Items',
            value: inventoryStats.lowStockProducts.toString(),
            change: inventoryStats.lowStockProducts > 0 ? { value: inventoryStats.lowStockProducts, type: 'warning' } : undefined
          },
          {
            label: 'Out of Stock',
            value: inventoryStats.outOfStockProducts.toString(),
            change: inventoryStats.outOfStockProducts > 0 ? { value: inventoryStats.outOfStockProducts, type: 'decrease' } : undefined
          }
        ]}
        actions={[
          {
            label: 'Add Product',
            href: '/admin/products/new',
            variant: 'primary',
            icon: PlusIcon
          },
          {
            label: 'Import Products',
            onClick: () => console.log('Import products'),
            variant: 'secondary',
            icon: ArrowUpTrayIcon
          },
          {
            label: 'Export Products',
            onClick: () => console.log('Export products'),
            variant: 'secondary',
            icon: ArrowDownTrayIcon
          }
        ]}
      />

      {/* Low Stock Alert */}
      {inventoryStats.lowStockProducts > 0 && (
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Low Stock Alert
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  You have {inventoryStats.lowStockProducts} product(s) with low stock levels.
                  Consider restocking these items to avoid stockouts.
                </p>
              </div>
              <div className="mt-4">
                <div className="-mx-2 -my-1.5 flex">
                  <button
                    type="button"
                    onClick={() => handleFilterChange('stockLevel', 'low')}
                    className="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                  >
                    View Low Stock Items
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-indigo-600 hover:text-indigo-900 flex items-center"
            >
              <FunnelIcon className="h-4 w-4 mr-1" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </button>
          </div>
        </div>

        {showFilters && (
          <div className="px-6 py-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={filters.search}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Categories</option>
                  <option value="spices">Spices & Herbs</option>
                  <option value="oils">Oils & Vinegars</option>
                  <option value="grains">Grains & Seeds</option>
                  <option value="sweeteners">Sweeteners</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stock Level
                </label>
                <select
                  value={filters.stockLevel}
                  onChange={(e) => handleFilterChange('stockLevel', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Stock Levels</option>
                  <option value="in-stock">In Stock</option>
                  <option value="low-stock">Low Stock</option>
                  <option value="out-of-stock">Out of Stock</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price Range
                </label>
                <select
                  value={filters.priceRange}
                  onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Prices</option>
                  <option value="0-20">$0 - $20</option>
                  <option value="20-50">$20 - $50</option>
                  <option value="50-100">$50 - $100</option>
                  <option value="100+">$100+</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Featured
                </label>
                <select
                  value={filters.featured}
                  onChange={(e) => handleFilterChange('featured', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Products</option>
                  <option value="featured">Featured Only</option>
                  <option value="not-featured">Not Featured</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Products Table */}
      <AdminDataTable
        columns={columns}
        data={products}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(page, pageSize) => setPagination(prev => ({ ...prev, current: page, pageSize }))}
        actions={actions}
        bulkActions={{
          actions: [
            {
              label: 'Activate Selected',
              onClick: (ids) => handleBulkAction('activate', ids),
              variant: 'primary'
            },
            {
              label: 'Deactivate Selected',
              onClick: (ids) => handleBulkAction('deactivate', ids),
              variant: 'secondary'
            },
            {
              label: 'Add to Featured',
              onClick: (ids) => handleBulkAction('feature', ids)
            },
            {
              label: 'Delete Selected',
              onClick: (ids) => handleBulkAction('delete', ids),
              variant: 'danger'
            }
          ],
          getRowId: (product) => product.id.toString()
        }}
        emptyText="No products found"
      />
    </AdminLayout>
  );
};

export default ProductManagement;
