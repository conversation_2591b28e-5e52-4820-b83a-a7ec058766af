#!/usr/bin/env node

/**
 * Database Table Creation Script for Nirvana Organics
 * Creates all necessary tables in the Hostinger MariaDB database
 */

const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`)
};

/**
 * Create Sequelize instance for MariaDB
 */
function createSequelizeInstance() {
  return new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      dialect: 'mysql',
      timezone: '+00:00',
      logging: false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      dialectOptions: {
        charset: 'utf8mb4',
        supportBigNumbers: true,
        bigNumberStrings: true
      }
    }
  );
}

/**
 * Define all models inline to avoid import issues
 */
function defineModels(sequelize) {
  const models = {};

  // User model
  models.User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.ENUM('customer', 'admin'),
      defaultValue: 'customer'
    },
    isEmailVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  });

  // Category model
  models.Category = sequelize.define('Category', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT
    },
    image: {
      type: DataTypes.STRING
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    }
  });

  // Product model
  models.Product = sequelize.define('Product', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT
    },
    shortDescription: {
      type: DataTypes.STRING
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    comparePrice: {
      type: DataTypes.DECIMAL(10, 2)
    },
    sku: {
      type: DataTypes.STRING,
      unique: true
    },
    trackQuantity: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    quantity: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    stock: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    lowStockThreshold: {
      type: DataTypes.INTEGER,
      defaultValue: 10
    },
    weight: {
      type: DataTypes.DECIMAL(8, 2)
    },
    weightUnit: {
      type: DataTypes.STRING,
      defaultValue: 'g'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    isFeatured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    tags: {
      type: DataTypes.JSON
    },
    images: {
      type: DataTypes.JSON
    },
    seoTitle: {
      type: DataTypes.STRING
    },
    seoDescription: {
      type: DataTypes.TEXT
    },
    rating: {
      type: DataTypes.DECIMAL(3, 2),
      defaultValue: 0
    },
    reviewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    categoryId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'Categories',
        key: 'id'
      }
    }
  });

  // Set up associations
  models.Category.hasMany(models.Product, { foreignKey: 'categoryId' });
  models.Product.belongsTo(models.Category, { foreignKey: 'categoryId' });

  return models;
}

/**
 * Create database tables
 */
async function createDatabaseTables() {
  log.info('Creating Database Tables for Nirvana Organics...');
  log.info('=================================================');

  const sequelize = createSequelizeInstance();

  try {
    // Test connection
    log.step('Testing database connection...');
    await sequelize.authenticate();
    log.success('Database connection established');

    // Define models
    log.step('Defining database models...');
    const models = defineModels(sequelize);
    log.success(`Defined ${Object.keys(models).length} models`);

    // Create tables
    log.step('Creating database tables...');
    await sequelize.sync({ force: false, alter: true });
    log.success('All tables created successfully');

    // Verify tables
    log.step('Verifying created tables...');
    const [tables] = await sequelize.query(`
      SELECT TABLE_NAME, TABLE_ROWS 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = '${process.env.DB_NAME}'
      ORDER BY TABLE_NAME
    `);

    log.success(`Created ${tables.length} tables:`);
    tables.forEach(table => {
      log.info(`  - ${table.TABLE_NAME}: ${table.TABLE_ROWS} rows`);
    });

    return true;

  } catch (error) {
    log.error(`Failed to create tables: ${error.message}`);
    return false;
  } finally {
    await sequelize.close();
    log.info('Database connection closed');
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const success = await createDatabaseTables();
    
    log.info('=================================================');
    if (success) {
      log.success('Database tables created successfully!');
      log.info('You can now run: npm run seed:database');
      process.exit(0);
    } else {
      log.error('Failed to create database tables!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Table creation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { createDatabaseTables };
