// US Shipping Configuration with USPS Integration
export interface ShippingZone {
  name: string;
  states: string[];
  baseRate: number;
  freeShippingThreshold: number;
  estimatedDays: string;
  restrictions?: string[];
}

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  carrier: 'USPS' | 'UPS' | 'FedEx';
  service: string;
  estimatedDays: string;
  baseRate: number;
  maxWeight?: number;
  features: string[];
}

// US States and Territories
export const US_STATES = {
  'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas', 'CA': 'California',
  'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware', 'FL': 'Florida', 'GA': 'Georgia',
  'HI': 'Hawaii', 'ID': 'Idaho', 'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa',
  'KS': 'Kansas', 'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
  'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi', 'MO': 'Missouri',
  'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada', 'NH': 'New Hampshire', 'NJ': 'New Jersey',
  'NM': 'New Mexico', 'NY': 'New York', 'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio',
  'OK': 'Oklahoma', 'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
  'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah', 'VT': 'Vermont',
  'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia', 'WI': 'Wisconsin', 'WY': 'Wyoming',
  'DC': 'District of Columbia', 'PR': 'Puerto Rico', 'VI': 'Virgin Islands', 'GU': 'Guam',
  'AS': 'American Samoa', 'MP': 'Northern Mariana Islands'
};

// States where hemp products are restricted
export const RESTRICTED_STATES = ['ID', 'IA', 'SD'];

// Shipping zones for different rates
export const SHIPPING_ZONES: ShippingZone[] = [
  {
    name: 'Zone 1 - Continental US',
    states: [
      'AL', 'AR', 'AZ', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'IL', 'IN', 'KS', 'KY', 'LA',
      'MA', 'MD', 'ME', 'MI', 'MN', 'MO', 'MS', 'MT', 'NC', 'ND', 'NE', 'NH', 'NJ', 'NM',
      'NV', 'NY', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'TN', 'TX', 'UT', 'VA', 'VT', 'WA',
      'WV', 'WI', 'WY', 'DC'
    ],
    baseRate: 6.99,
    freeShippingThreshold: 100,
    estimatedDays: '3-5 business days'
  },
  {
    name: 'Zone 2 - Alaska & Hawaii',
    states: ['AK', 'HI'],
    baseRate: 12.99,
    freeShippingThreshold: 150,
    estimatedDays: '5-7 business days'
  },
  {
    name: 'Zone 3 - US Territories',
    states: ['PR', 'VI', 'GU', 'AS', 'MP'],
    baseRate: 19.99,
    freeShippingThreshold: 200,
    estimatedDays: '7-14 business days',
    restrictions: ['Contact customer service for availability']
  }
];

// USPS Shipping Methods
export const USPS_SHIPPING_METHODS: ShippingMethod[] = [
  {
    id: 'usps_ground_advantage',
    name: 'USPS Ground Advantage',
    description: 'Reliable ground shipping with tracking',
    carrier: 'USPS',
    service: 'Ground Advantage',
    estimatedDays: '3-5 business days',
    baseRate: 6.99,
    maxWeight: 70,
    features: ['Tracking included', 'Insurance up to $100', 'Delivery confirmation']
  },
  {
    id: 'usps_priority',
    name: 'USPS Priority Mail',
    description: 'Faster delivery with priority handling',
    carrier: 'USPS',
    service: 'Priority Mail',
    estimatedDays: '1-3 business days',
    baseRate: 9.99,
    maxWeight: 70,
    features: ['Tracking included', 'Insurance up to $100', 'Priority handling', 'Free boxes']
  },
  {
    id: 'usps_priority_express',
    name: 'USPS Priority Mail Express',
    description: 'Overnight delivery with money-back guarantee',
    carrier: 'USPS',
    service: 'Priority Mail Express',
    estimatedDays: '1-2 business days',
    baseRate: 24.99,
    maxWeight: 70,
    features: ['Overnight delivery', 'Money-back guarantee', 'Tracking included', 'Insurance up to $100', 'Signature confirmation']
  }
];

// Calculate shipping cost based on destination and cart value
export const calculateShippingCost = (
  state: string,
  cartTotal: number,
  weight: number = 1,
  method: string = 'usps_ground_advantage'
): { cost: number; isFree: boolean; zone: ShippingZone | null; estimatedDays: string } => {
  // Check if state is restricted
  if (RESTRICTED_STATES.includes(state)) {
    throw new Error(`Shipping to ${state} is not available due to local regulations.`);
  }

  // Find the shipping zone for the state
  const zone = SHIPPING_ZONES.find(z => z.states.includes(state));
  if (!zone) {
    throw new Error(`Shipping to ${state} is not available.`);
  }

  // Find the shipping method
  const shippingMethod = USPS_SHIPPING_METHODS.find(m => m.id === method);
  if (!shippingMethod) {
    throw new Error(`Shipping method ${method} is not available.`);
  }

  // Check if free shipping applies
  const isFree = cartTotal >= zone.freeShippingThreshold;
  
  // Calculate base cost
  let cost = isFree ? 0 : Math.max(zone.baseRate, shippingMethod.baseRate);

  // Add weight-based surcharge for heavy items (over 5 lbs)
  if (weight > 5) {
    const extraWeight = weight - 5;
    cost += Math.ceil(extraWeight / 5) * 2.99; // $2.99 per additional 5 lbs
  }

  // Special handling for Alaska/Hawaii and territories
  if (zone.name.includes('Alaska & Hawaii') && method === 'usps_priority_express') {
    cost += 10; // Additional surcharge for express to AK/HI
  }

  return {
    cost: isFree ? 0 : cost,
    isFree,
    zone,
    estimatedDays: shippingMethod.estimatedDays
  };
};

// Get available shipping methods for a state
export const getAvailableShippingMethods = (state: string): ShippingMethod[] => {
  if (RESTRICTED_STATES.includes(state)) {
    return [];
  }

  const zone = SHIPPING_ZONES.find(z => z.states.includes(state));
  if (!zone) {
    return [];
  }

  // All USPS methods are available for continental US
  if (zone.name.includes('Continental US')) {
    return USPS_SHIPPING_METHODS;
  }

  // Limited methods for Alaska/Hawaii and territories
  if (zone.name.includes('Alaska & Hawaii')) {
    return USPS_SHIPPING_METHODS.filter(m => 
      m.id === 'usps_ground_advantage' || m.id === 'usps_priority'
    );
  }

  // Only ground shipping for territories
  return USPS_SHIPPING_METHODS.filter(m => m.id === 'usps_ground_advantage');
};

// Validate US address format
export const validateUSAddress = (address: {
  address1: string;
  city: string;
  state: string;
  zip: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validate state
  if (!US_STATES[address.state as keyof typeof US_STATES]) {
    errors.push('Invalid state code');
  }

  // Validate ZIP code (5 digits or 5+4 format)
  const zipRegex = /^\d{5}(-\d{4})?$/;
  if (!zipRegex.test(address.zip)) {
    errors.push('Invalid ZIP code format (use 12345 or 12345-6789)');
  }

  // Basic address validation
  if (!address.address1 || address.address1.length < 5) {
    errors.push('Address line 1 must be at least 5 characters');
  }

  if (!address.city || address.city.length < 2) {
    errors.push('City must be at least 2 characters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Get shipping restrictions for a state
export const getShippingRestrictions = (state: string): string[] => {
  if (RESTRICTED_STATES.includes(state)) {
    return [
      'Hemp-derived products are not legal in this state',
      'We cannot ship to this location due to local regulations',
      'Please check your local laws before ordering'
    ];
  }

  const zone = SHIPPING_ZONES.find(z => z.states.includes(state));
  return zone?.restrictions || [];
};

// Format shipping method display
export const formatShippingMethod = (method: ShippingMethod, cost: number, isFree: boolean): string => {
  const costDisplay = isFree ? 'FREE' : `$${cost.toFixed(2)}`;
  return `${method.name} (${method.estimatedDays}) - ${costDisplay}`;
};
