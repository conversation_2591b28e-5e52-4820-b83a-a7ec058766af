import React, { useState } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
  width?: number;
  height?: number;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  placeholder = '/images/placeholder-product.jpg',
  onLoad,
  onError,
  width,
  height
}) => {
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const imageSrc = hasError ? placeholder : src;

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onLoad={handleLoad}
      onError={handleError}
      width={width}
      height={height}
      loading="lazy"
      decoding="async"
      style={{
        imageRendering: 'crisp-edges',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)'
      }}
    />
  );
};

export default LazyImage;
