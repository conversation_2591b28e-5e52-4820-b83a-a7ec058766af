import { User } from '../types';

/**
 * Role-based utility functions for frontend permission checking
 */

export type UserRole = 'customer' | 'admin' | 'manager';

export interface Permission {
  // Product permissions
  canViewProducts: boolean;
  canCreateProducts: boolean;
  canEditProducts: boolean;
  canDeleteProducts: boolean;
  canManageProductImages: boolean;
  
  // User management permissions
  canViewUsers: boolean;
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canInviteManagers: boolean;
  
  // Order permissions
  canViewOrders: boolean;
  canEditOrders: boolean;
  canDeleteOrders: boolean;
  canManageOrders: boolean;
  
  // Analytics and reporting
  canViewAnalytics: boolean;
  canExportData: boolean;
  
  // System permissions
  canViewSystemLogs: boolean;
  canViewAuditLogs: boolean;
  canAccessSystemSettings: boolean;
  
  // Category and inventory permissions
  canViewCategories: boolean;
  canEditCategories: boolean;
  canViewInventory: boolean;
  canEditInventory: boolean;
}

/**
 * Get user permissions based on role
 */
export const getUserPermissions = (role: UserRole): Permission => {
  const basePermissions: Permission = {
    canViewProducts: false,
    canCreateProducts: false,
    canEditProducts: false,
    canDeleteProducts: false,
    canManageProductImages: false,
    canViewUsers: false,
    canCreateUsers: false,
    canEditUsers: false,
    canDeleteUsers: false,
    canInviteManagers: false,
    canViewOrders: false,
    canEditOrders: false,
    canDeleteOrders: false,
    canManageOrders: false,
    canViewAnalytics: false,
    canExportData: false,
    canViewSystemLogs: false,
    canViewAuditLogs: false,
    canAccessSystemSettings: false,
    canViewCategories: false,
    canEditCategories: false,
    canViewInventory: false,
    canEditInventory: false
  };

  switch (role) {
    case 'admin':
      return {
        ...basePermissions,
        // Full access to everything
        canViewProducts: true,
        canCreateProducts: true,
        canEditProducts: true,
        canDeleteProducts: true,
        canManageProductImages: true,
        canViewUsers: true,
        canCreateUsers: true,
        canEditUsers: true,
        canDeleteUsers: true,
        canInviteManagers: true,
        canViewOrders: true,
        canEditOrders: true,
        canDeleteOrders: true,
        canManageOrders: true,
        canViewAnalytics: true,
        canExportData: true,
        canViewSystemLogs: true,
        canViewAuditLogs: true,
        canAccessSystemSettings: true,
        canViewCategories: true,
        canEditCategories: true,
        canViewInventory: true,
        canEditInventory: true
      };

    case 'manager':
      return {
        ...basePermissions,
        // Read-only product access
        canViewProducts: true,
        canViewCategories: true,
        canViewInventory: true,
        // Order management
        canViewOrders: true,
        canEditOrders: true,
        canManageOrders: true,
        // Basic analytics
        canViewAnalytics: true,
        // User viewing (customers only)
        canViewUsers: true
      };

    case 'customer':
    default:
      return basePermissions; // Very limited permissions
  }
};

/**
 * Check if user has specific role
 */
export const hasRole = (user: User | null, role: UserRole | UserRole[]): boolean => {
  if (!user) return false;
  
  if (Array.isArray(role)) {
    return role.includes(user.role);
  }
  
  return user.role === role;
};

/**
 * Check if user is admin
 */
export const isAdmin = (user: User | null): boolean => {
  return hasRole(user, 'admin');
};

/**
 * Check if user is manager
 */
export const isManager = (user: User | null): boolean => {
  return hasRole(user, 'manager');
};

/**
 * Check if user is customer
 */
export const isCustomer = (user: User | null): boolean => {
  return hasRole(user, 'customer');
};

/**
 * Check if user is manager or admin
 */
export const isManagerOrAdmin = (user: User | null): boolean => {
  return hasRole(user, ['manager', 'admin']);
};

/**
 * Check if user has specific permission
 */
export const hasPermission = (user: User | null, permission: keyof Permission): boolean => {
  if (!user) return false;
  
  const permissions = getUserPermissions(user.role);
  return permissions[permission];
};

/**
 * Get user's dashboard route based on role
 */
export const getDashboardRoute = (user: User | null): string => {
  if (!user) return '/login';
  
  switch (user.role) {
    case 'admin':
      return '/admin/dashboard';
    case 'manager':
      return '/manager/dashboard';
    case 'customer':
    default:
      return '/account';
  }
};

/**
 * Get navigation items based on user role
 */
export const getNavigationItems = (user: User | null) => {
  if (!user) return [];
  
  const permissions = getUserPermissions(user.role);
  const items = [];
  
  if (permissions.canViewProducts || permissions.canViewOrders || permissions.canViewAnalytics) {
    items.push({
      name: 'Dashboard',
      href: getDashboardRoute(user),
      icon: 'HomeIcon'
    });
  }
  
  if (permissions.canViewProducts) {
    items.push({
      name: 'Products',
      href: user.role === 'admin' ? '/admin/products' : '/manager/products',
      icon: 'CubeIcon'
    });
  }
  
  if (permissions.canViewOrders) {
    items.push({
      name: 'Orders',
      href: user.role === 'admin' ? '/admin/orders' : '/manager/orders',
      icon: 'ShoppingBagIcon'
    });
  }
  
  if (permissions.canViewUsers) {
    items.push({
      name: user.role === 'admin' ? 'Users' : 'Customers',
      href: user.role === 'admin' ? '/admin/users' : '/manager/customers',
      icon: 'UsersIcon'
    });
  }
  
  if (permissions.canViewAnalytics) {
    items.push({
      name: 'Analytics',
      href: user.role === 'admin' ? '/admin/analytics' : '/manager/analytics',
      icon: 'ChartBarIcon'
    });
  }
  
  if (permissions.canViewAuditLogs) {
    items.push({
      name: 'Audit Logs',
      href: '/admin/audit',
      icon: 'DocumentTextIcon'
    });
  }
  
  if (permissions.canAccessSystemSettings) {
    items.push({
      name: 'Settings',
      href: '/admin/settings',
      icon: 'CogIcon'
    });
  }
  
  return items;
};

/**
 * Check if user can access a specific route
 */
export const canAccessRoute = (user: User | null, route: string): boolean => {
  if (!user) return false;
  
  const permissions = getUserPermissions(user.role);
  
  // Admin routes
  if (route.startsWith('/admin')) {
    if (route.includes('/products')) return permissions.canViewProducts;
    if (route.includes('/orders')) return permissions.canViewOrders;
    if (route.includes('/users')) return permissions.canViewUsers;
    if (route.includes('/analytics')) return permissions.canViewAnalytics;
    if (route.includes('/audit')) return permissions.canViewAuditLogs;
    if (route.includes('/settings')) return permissions.canAccessSystemSettings;
    return isAdmin(user); // Default admin access
  }
  
  // Manager routes
  if (route.startsWith('/manager')) {
    if (route.includes('/products')) return permissions.canViewProducts;
    if (route.includes('/orders')) return permissions.canViewOrders;
    if (route.includes('/customers')) return permissions.canViewUsers;
    if (route.includes('/analytics')) return permissions.canViewAnalytics;
    return isManagerOrAdmin(user); // Default manager/admin access
  }
  
  // Customer routes
  if (route.startsWith('/account')) {
    return true; // All authenticated users can access account
  }
  
  return true; // Default allow access
};

/**
 * Get role display name
 */
export const getRoleDisplayName = (role: UserRole): string => {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'manager':
      return 'Manager';
    case 'customer':
      return 'Customer';
    default:
      return 'Unknown';
  }
};

/**
 * Get role badge color
 */
export const getRoleBadgeColor = (role: UserRole): string => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'manager':
      return 'bg-blue-100 text-blue-800';
    case 'customer':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};
