import { useState, useEffect, useCallback } from 'react';

interface GeolocationState {
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  error: string | null;
  loading: boolean;
  permission: PermissionState | null;
}

interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  watch?: boolean;
}

interface AddressComponents {
  streetNumber?: string;
  streetName?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  formattedAddress?: string;
}

export const useGeolocation = (options: GeolocationOptions = {}) => {
  const {
    enableHighAccuracy = true,
    timeout = 10000,
    maximumAge = 300000, // 5 minutes
    watch = false
  } = options;

  const [state, setState] = useState<GeolocationState>({
    latitude: null,
    longitude: null,
    accuracy: null,
    error: null,
    loading: false,
    permission: null
  });

  const [address, setAddress] = useState<AddressComponents | null>(null);
  const [addressLoading, setAddressLoading] = useState(false);

  // Check geolocation permission
  const checkPermission = useCallback(async () => {
    if (!navigator.permissions) {
      return 'prompt' as PermissionState;
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      setState(prev => ({ ...prev, permission: permission.state }));
      return permission.state;
    } catch (error) {
      console.error('Error checking geolocation permission:', error);
      return 'prompt' as PermissionState;
    }
  }, []);

  // Request geolocation permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!navigator.geolocation) {
      setState(prev => ({ 
        ...prev, 
        error: 'Geolocation is not supported by this browser' 
      }));
      return false;
    }

    const permission = await checkPermission();
    
    if (permission === 'denied') {
      setState(prev => ({ 
        ...prev, 
        error: 'Geolocation permission denied. Please enable location access in your browser settings.' 
      }));
      return false;
    }

    return true;
  }, [checkPermission]);

  // Get current position
  const getCurrentPosition = useCallback(async (): Promise<GeolocationPosition | null> => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return null;

    setState(prev => ({ ...prev, loading: true, error: null }));

    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setState(prev => ({
            ...prev,
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            loading: false,
            error: null
          }));
          resolve(position);
        },
        (error) => {
          let errorMessage = 'Failed to get location';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
          }

          setState(prev => ({
            ...prev,
            loading: false,
            error: errorMessage
          }));
          reject(error);
        },
        {
          enableHighAccuracy,
          timeout,
          maximumAge
        }
      );
    });
  }, [requestPermission, enableHighAccuracy, timeout, maximumAge]);

  // Reverse geocoding to get address from coordinates
  const getAddressFromCoordinates = useCallback(async (lat: number, lng: number): Promise<AddressComponents | null> => {
    setAddressLoading(true);
    
    try {
      // Using a free geocoding service (you might want to use Google Maps API or similar)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`
      );
      
      if (!response.ok) {
        throw new Error('Geocoding service unavailable');
      }
      
      const data = await response.json();
      
      const addressComponents: AddressComponents = {
        streetNumber: data.localityInfo?.administrative?.[0]?.name || '',
        streetName: data.localityInfo?.administrative?.[1]?.name || '',
        city: data.city || data.locality || '',
        state: data.principalSubdivision || '',
        country: data.countryName || '',
        postalCode: data.postcode || '',
        formattedAddress: data.localityLanguageRequested || ''
      };
      
      setAddress(addressComponents);
      return addressComponents;
      
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      setAddress(null);
      return null;
    } finally {
      setAddressLoading(false);
    }
  }, []);

  // Get current location and address
  const getCurrentLocationAndAddress = useCallback(async () => {
    try {
      const position = await getCurrentPosition();
      if (position) {
        const addressData = await getAddressFromCoordinates(
          position.coords.latitude,
          position.coords.longitude
        );
        return { position, address: addressData };
      }
      return null;
    } catch (error) {
      console.error('Error getting location and address:', error);
      return null;
    }
  }, [getCurrentPosition, getAddressFromCoordinates]);

  // Watch position (for continuous tracking)
  useEffect(() => {
    let watchId: number | null = null;

    if (watch && navigator.geolocation) {
      const startWatching = async () => {
        const hasPermission = await requestPermission();
        if (!hasPermission) return;

        watchId = navigator.geolocation.watchPosition(
          (position) => {
            setState(prev => ({
              ...prev,
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy,
              loading: false,
              error: null
            }));
          },
          (error) => {
            setState(prev => ({
              ...prev,
              loading: false,
              error: error.message
            }));
          },
          {
            enableHighAccuracy,
            timeout,
            maximumAge
          }
        );
      };

      startWatching();
    }

    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watch, requestPermission, enableHighAccuracy, timeout, maximumAge]);

  // Initialize permission check
  useEffect(() => {
    checkPermission();
  }, [checkPermission]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      latitude: null,
      longitude: null,
      accuracy: null,
      error: null,
      loading: false,
      permission: null
    });
    setAddress(null);
  }, []);

  return {
    ...state,
    address,
    addressLoading,
    getCurrentPosition,
    getAddressFromCoordinates,
    getCurrentLocationAndAddress,
    requestPermission,
    checkPermission,
    clearError,
    reset,
    isSupported: !!navigator.geolocation
  };
};
