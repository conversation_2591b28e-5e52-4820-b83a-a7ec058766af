// User Types
export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  dateOfBirth?: string;
  role: 'customer' | 'admin' | 'super_admin';
  isVerified: boolean;
  isActive: boolean;
  fullName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id?: number;
  userId: number;
  type: 'billing' | 'shipping';
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

// Product Types
export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  sku: string;
  price: number;
  salePrice?: number;
  comparePrice?: number;
  costPrice?: number;
  trackQuantity: boolean;
  quantity: number;
  stock: number;
  lowStockThreshold: number;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images: ProductImage[];
  categoryId: number;
  subcategoryId?: number;
  category?: Category;
  subcategory?: Category;
  tags: string[];
  variants: ProductVariant[];
  attributes: ProductAttribute[];
  seoTitle?: string;
  seoDescription?: string;
  status: 'active' | 'draft' | 'archived';
  featured: boolean;
  bestSeller: boolean;
  cannabinoid: 'THC-A' | 'CBD' | 'Delta-8' | 'Delta-9' | 'THC-P';
  strain?: 'Sativa' | 'Indica' | 'Hybrid';
  effects?: string[];
  potency?: string;
  labReports?: LabReport[];
  viewCount: number;
  salesCount: number;
  averageRating: number;
  reviewCount: number;
  createdAt: string;
  updatedAt: string;
  isInStock?: () => boolean;
}

export interface ProductImage {
  url: string;
  alt: string;
  position: number;
}

export interface ProductVariant {
  id?: string;
  name: string;
  value: string;
  price?: number;
  sku?: string;
  quantity?: number;
  image?: string;
}

export interface ProductAttribute {
  name: string;
  value: string;
}

export interface LabReport {
  name: string;
  url: string;
  date: string;
}

// Category Types
export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: number;
  level: number;
  status: 'active' | 'inactive';
  sortOrder: number;
  seoTitle?: string;
  seoDescription?: string;
  productCount: number;
  children?: Category[];
  createdAt: string;
  updatedAt: string;
}

// Cart Types
export interface CartItem {
  productId: number;
  product?: Product;
  variant?: ProductVariant;
  quantity: number;
  price: number;
}

export interface Cart {
  id: number;
  userId: number;
  items: CartItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  couponCode?: string;
  discount: number;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Order Types
export interface OrderAddress {
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

export interface Order {
  id: number;
  orderNumber: string;
  userId: number;
  user?: User;
  items: OrderItem[];
  billingAddress: OrderAddress;
  shippingAddress: OrderAddress;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  paymentId?: string;
  squarePaymentId?: string;
  trackingNumber?: string;
  shippingCarrier?: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  notes?: string;
  adminNotes?: string;
  couponCode?: string;
  refundAmount: number;
  refundReason?: string;
  statusHistory: Array<{
    status: string;
    date: string;
    note?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: number;
  product?: Product;
  productSnapshot: {
    name: string;
    sku: string;
    image?: string;
  };
  variant?: ProductVariant;
  quantity: number;
  price: number;
  total: number;
}

// Review Types
export interface Review {
  id: number;
  userId: number;
  productId: number;
  orderId?: number;
  user?: User;
  product?: Product;
  rating: number;
  title: string;
  comment: string;
  verified: boolean;
  helpful: number;
  status: 'pending' | 'approved' | 'rejected';
  adminResponse?: string;
  adminResponseDate?: string;
  images: Array<{
    url: string;
    alt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Auth Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  dateOfBirth?: Date;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// Filter Types
export interface ProductFilters {
  category?: string;
  subcategory?: string;
  cannabinoid?: string[] | string; // Allow both string and string[] for flexibility
  strain?: string[];
  priceRange?: [number, number];
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  featured?: boolean;
  bestSeller?: boolean;
  search?: string;
  sortBy?: 'name' | 'price' | 'createdAt' | 'rating' | 'quantity'; // Added 'quantity' for stock sorting
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  status?: 'active' | 'draft' | 'archived'; // Added status property
  lowStock?: boolean; // Added lowStock property
}

// UI Types
export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

export interface Modal {
  isOpen: boolean;
  type?: string;
  data?: any;
}

// Payment Types
export interface PaymentData {
  sourceId: string;
  amount: number;
  currency?: string;
  orderId: string;
  billingAddress: OrderAddress;
  shippingAddress: OrderAddress;
}

export interface SquarePaymentResult {
  success: boolean;
  payment?: any;
  error?: string;
  details?: any[];
}

// Form Types
export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

export interface NewsletterSubscription {
  email: string;
}

// Analytics Types
export interface AnalyticsData {
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  totalProducts: number;
  recentOrders: Order[];
  topProducts: Product[];
  salesData: {
    date: string;
    sales: number;
    orders: number;
  }[];
}
