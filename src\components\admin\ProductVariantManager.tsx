import React, { useState } from 'react';
import { ProductVariant } from '../../types';
import {
  PlusIcon,
  TrashIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface ProductVariantManagerProps {
  variants: ProductVariant[];
  onChange: (variants: ProductVariant[]) => void;
}

interface VariantFormData {
  name: string;
  value: string;
  price: number;
  sku: string;
  quantity: number;
  image: string;
}

const ProductVariantManager: React.FC<ProductVariantManagerProps> = ({
  variants,
  onChange
}) => {
  const [isAddingVariant, setIsAddingVariant] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState<VariantFormData>({
    name: '',
    value: '',
    price: 0,
    sku: '',
    quantity: 0,
    image: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Common variant types for cannabis products
  const variantTypes = [
    'Size',
    'Weight',
    'Potency',
    'Strain',
    'Format',
    'Flavor',
    'Concentration',
    'Package Size'
  ];

  const resetForm = () => {
    setFormData({
      name: '',
      value: '',
      price: 0,
      sku: '',
      quantity: 0,
      image: ''
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Variant name is required';
    }

    if (!formData.value.trim()) {
      newErrors.value = 'Variant value is required';
    }

    if (formData.price < 0) {
      newErrors.price = 'Price cannot be negative';
    }

    if (formData.quantity < 0) {
      newErrors.quantity = 'Quantity cannot be negative';
    }

    // Check for duplicate variant combinations
    const isDuplicate = variants.some((variant, index) => {
      if (editingIndex !== null && index === editingIndex) return false;
      return variant.name === formData.name && variant.value === formData.value;
    });

    if (isDuplicate) {
      newErrors.combination = 'This variant combination already exists';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof VariantFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddVariant = () => {
    if (!validateForm()) return;

    const newVariant: ProductVariant = {
      name: formData.name,
      value: formData.value,
      price: formData.price || undefined,
      sku: formData.sku || undefined,
      quantity: formData.quantity || undefined,
      image: formData.image || undefined
    };

    onChange([...variants, newVariant]);
    resetForm();
    setIsAddingVariant(false);
  };

  const handleEditVariant = (index: number) => {
    const variant = variants[index];
    setFormData({
      name: variant.name,
      value: variant.value,
      price: variant.price || 0,
      sku: variant.sku || '',
      quantity: variant.quantity || 0,
      image: variant.image || ''
    });
    setEditingIndex(index);
    setIsAddingVariant(true);
  };

  const handleUpdateVariant = () => {
    if (!validateForm()) return;

    const updatedVariants = [...variants];
    updatedVariants[editingIndex!] = {
      name: formData.name,
      value: formData.value,
      price: formData.price || undefined,
      sku: formData.sku || undefined,
      quantity: formData.quantity || undefined,
      image: formData.image || undefined
    };

    onChange(updatedVariants);
    resetForm();
    setIsAddingVariant(false);
    setEditingIndex(null);
  };

  const handleDeleteVariant = (index: number) => {
    const updatedVariants = variants.filter((_, i) => i !== index);
    onChange(updatedVariants);
  };

  const handleCancel = () => {
    resetForm();
    setIsAddingVariant(false);
    setEditingIndex(null);
  };

  return (
    <div className="space-y-4">
      {/* Existing Variants */}
      {variants.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Current Variants</h4>
          <div className="space-y-2">
            {variants.map((variant, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <div>
                      <span className="font-medium text-gray-900">
                        {variant.name}: {variant.value}
                      </span>
                      {variant.price && (
                        <span className="ml-2 text-sm text-gray-600">
                          ${variant.price.toFixed(2)}
                        </span>
                      )}
                    </div>
                    {variant.sku && (
                      <div className="text-sm text-gray-500">
                        SKU: {variant.sku}
                      </div>
                    )}
                    {variant.quantity !== undefined && (
                      <div className="text-sm text-gray-500">
                        Qty: {variant.quantity}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => handleEditVariant(index)}
                    className="p-2 text-gray-600 hover:text-primary-600 hover:bg-white rounded-md transition-colors"
                    title="Edit variant"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDeleteVariant(index)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-white rounded-md transition-colors"
                    title="Delete variant"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add/Edit Variant Form */}
      {isAddingVariant ? (
        <div className="p-4 bg-white border border-gray-300 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-4">
            {editingIndex !== null ? 'Edit Variant' : 'Add New Variant'}
          </h4>
          
          {errors.combination && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.combination}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Variant Type *
              </label>
              <div className="flex space-x-2">
                <select
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select type</option>
                  {variantTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Or enter custom"
                  className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
              </div>
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Value *
              </label>
              <input
                type="text"
                value={formData.value}
                onChange={(e) => handleInputChange('value', e.target.value)}
                placeholder="e.g., 1g, Small, Sativa"
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  errors.value ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.value && (
                <p className="mt-1 text-sm text-red-600">{errors.value}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price Adjustment
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  errors.price ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              <p className="mt-1 text-xs text-gray-500">
                Additional price for this variant (leave 0 for no change)
              </p>
              {errors.price && (
                <p className="mt-1 text-sm text-red-600">{errors.price}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SKU
              </label>
              <input
                type="text"
                value={formData.sku}
                onChange={(e) => handleInputChange('sku', e.target.value)}
                placeholder="Variant SKU (optional)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Quantity
              </label>
              <input
                type="number"
                min="0"
                value={formData.quantity}
                onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                placeholder="0"
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  errors.quantity ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.quantity && (
                <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image URL
              </label>
              <input
                type="url"
                value={formData.image}
                onChange={(e) => handleInputChange('image', e.target.value)}
                placeholder="https://example.com/image.jpg (optional)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <XMarkIcon className="h-4 w-4 inline mr-1" />
              Cancel
            </button>
            <button
              type="button"
              onClick={editingIndex !== null ? handleUpdateVariant : handleAddVariant}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
            >
              <CheckIcon className="h-4 w-4 inline mr-1" />
              {editingIndex !== null ? 'Update Variant' : 'Add Variant'}
            </button>
          </div>
        </div>
      ) : (
        <button
          type="button"
          onClick={() => setIsAddingVariant(true)}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-primary-400 hover:text-primary-600 transition-colors"
        >
          <PlusIcon className="h-5 w-5 inline mr-2" />
          Add Product Variant
        </button>
      )}

      {variants.length === 0 && !isAddingVariant && (
        <div className="text-center py-6 text-gray-500">
          <p className="text-sm">No variants added yet.</p>
          <p className="text-xs mt-1">
            Add variants like different sizes, weights, or potencies for this product.
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductVariantManager;
