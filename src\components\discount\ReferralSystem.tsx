import React, { useState, useEffect } from 'react';
import { 
  ShareIcon, 
  ClipboardDocumentIcon, 
  GiftIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import LoadingSpinner from '../common/LoadingSpinner';
import { discountService } from '../../services/discountService';

interface ReferralStats {
  totalReferrals: number;
  completedReferrals: number;
  pendingReferrals: number;
  totalRewards: number;
  referrals: Array<{
    id: string;
    referralCode: string;
    status: 'pending' | 'completed' | 'rewarded';
    referee?: {
      firstName: string;
      lastName: string;
      email: string;
    };
    order?: {
      total: number;
      createdAt: string;
    };
    rewardAmount: number;
    completedAt?: string;
  }>;
}

const ReferralSystem: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  
  const [referralCode, setReferralCode] = useState<string>('');
  const [shareUrl, setShareUrl] = useState<string>('');
  const [stats, setStats] = useState<ReferralStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (user) {
      fetchReferralData();
    }
  }, [user]);

  const fetchReferralData = async () => {
    try {
      setLoading(true);
      const [codeResponse, statsResponse] = await Promise.all([
        discountService.generateReferralCode(),
        discountService.getReferralStats()
      ]);

      if (codeResponse.success) {
        setReferralCode(codeResponse.data.referralCode);
        setShareUrl(codeResponse.data.shareUrl);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }
    } catch (error) {
      console.error('Error fetching referral data:', error);
      dispatch(addToast({
        type: 'error',
        message: 'Failed to load referral information'
      }));
    } finally {
      setLoading(false);
    }
  };

  const generateNewCode = async () => {
    try {
      setGenerating(true);
      const response = await discountService.generateReferralCode();
      
      if (response.success) {
        setReferralCode(response.data.referralCode);
        setShareUrl(response.data.shareUrl);
        dispatch(addToast({
          type: 'success',
          message: response.message
        }));
      } else {
        dispatch(addToast({
          type: 'error',
          message: response.message
        }));
      }
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        message: 'Failed to generate referral code'
      }));
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = async (text: string, type: 'code' | 'url') => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      dispatch(addToast({
        type: 'success',
        message: `${type === 'code' ? 'Referral code' : 'Share URL'} copied to clipboard!`
      }));
      
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        message: 'Failed to copy to clipboard'
      }));
    }
  };

  const shareOnSocialMedia = async (platform: string) => {
    const message = `Check out Nirvana Organics! Use my referral code ${referralCode} and get $5 off your first order. ${shareUrl}`;
    
    let shareUrl_platform = '';
    
    switch (platform) {
      case 'facebook':
        shareUrl_platform = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(message)}`;
        break;
      case 'twitter':
        shareUrl_platform = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
        break;
      case 'whatsapp':
        shareUrl_platform = `https://wa.me/?text=${encodeURIComponent(message)}`;
        break;
      case 'email':
        shareUrl_platform = `mailto:?subject=${encodeURIComponent('Get $5 off at Nirvana Organics!')}&body=${encodeURIComponent(message)}`;
        break;
      default:
        return;
    }

    // Record the share
    try {
      await discountService.recordSocialShare(platform, shareUrl_platform);
    } catch (error) {
      console.error('Error recording social share:', error);
    }

    // Open share URL
    window.open(shareUrl_platform, '_blank', 'width=600,height=400');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mx-auto mb-4">
          <GiftIcon className="h-8 w-8 text-primary-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Refer Friends & Earn Rewards</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Share Nirvana Organics with your friends and family. They get $5 off their first order, 
          and you earn rewards for every successful referral!
        </p>
      </div>

      {/* Referral Code Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Referral Code</h3>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Referral Code
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={referralCode}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-gray-900 font-mono text-lg"
                />
                <button
                  onClick={() => copyToClipboard(referralCode, 'code')}
                  className="px-4 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors flex items-center"
                >
                  {copied ? (
                    <CheckIcon className="h-5 w-5" />
                  ) : (
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Share URL
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-gray-900 text-sm"
                />
                <button
                  onClick={() => copyToClipboard(shareUrl, 'url')}
                  className="px-4 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors flex items-center"
                >
                  {copied ? (
                    <CheckIcon className="h-5 w-5" />
                  ) : (
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Social Share Buttons */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Share on Social Media</h4>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => shareOnSocialMedia('facebook')}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <span className="mr-2">📘</span>
              Facebook
            </button>
            <button
              onClick={() => shareOnSocialMedia('twitter')}
              className="flex items-center px-4 py-2 bg-sky-500 text-white rounded-md hover:bg-sky-600 transition-colors"
            >
              <span className="mr-2">🐦</span>
              Twitter
            </button>
            <button
              onClick={() => shareOnSocialMedia('whatsapp')}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <span className="mr-2">📱</span>
              WhatsApp
            </button>
            <button
              onClick={() => shareOnSocialMedia('email')}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              <span className="mr-2">📧</span>
              Email
            </button>
          </div>
        </div>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <UserGroupIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{stats.totalReferrals}</div>
            <div className="text-sm text-gray-600">Total Referrals</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <CheckIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{stats.completedReferrals}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <ShareIcon className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{stats.pendingReferrals}</div>
            <div className="text-sm text-gray-600">Pending</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <CurrencyDollarIcon className="h-8 w-8 text-primary-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">${stats.totalRewards.toFixed(2)}</div>
            <div className="text-sm text-gray-600">Total Rewards</div>
          </div>
        </div>
      )}

      {/* Recent Referrals */}
      {stats && stats.referrals.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Referrals</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {stats.referrals.slice(0, 5).map((referral) => (
              <div key={referral.id} className="p-6 flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      referral.status === 'completed' ? 'bg-green-500' :
                      referral.status === 'rewarded' ? 'bg-blue-500' :
                      'bg-yellow-500'
                    }`} />
                    <div>
                      {referral.referee ? (
                        <div className="font-medium text-gray-900">
                          {referral.referee.firstName} {referral.referee.lastName}
                        </div>
                      ) : (
                        <div className="font-medium text-gray-500">Pending signup</div>
                      )}
                      <div className="text-sm text-gray-600 capitalize">
                        Status: {referral.status}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {referral.rewardAmount > 0 && (
                    <div className="font-medium text-primary-600">
                      +${referral.rewardAmount.toFixed(2)}
                    </div>
                  )}
                  {referral.completedAt && (
                    <div className="text-sm text-gray-500">
                      {new Date(referral.completedAt).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ReferralSystem;
