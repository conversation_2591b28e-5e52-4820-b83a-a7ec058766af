{"use strict": true, "name": "nirvana-organics-ecommerce", "private": true, "version": "0.0.0", "scripts": {"start": "node server/index.js", "dev": "vite", "dev:admin": "vite --config vite.admin.config.ts --host", "build": "vite build --mode staging", "build:prod": "node scripts/build-production.js", "build:frontend": "vite build --mode production", "build:admin": "vite build --config vite.admin.config.ts --mode production", "build:check": "tsc -b && vite build --mode staging", "build:optimize": "node scripts/build-production.js && chmod +x scripts/optimize-assets.sh && ./scripts/optimize-assets.sh", "lint": "eslint .", "preview": "vite preview", "server": "cd server && npm run dev", "server:admin": "nodemon server/admin-server.js", "test": "vitest run tests/comprehensive.test.js", "test:watch": "vitest tests/comprehensive.test.js", "test:all": "vitest", "type-check": "tsc --noEmit", "test:gmail": "node scripts/test-gmail.js", "validate:email": "node scripts/validate-email-setup.js", "generate:favicons": "node scripts/generate-favicons.js", "test:favicons": "node scripts/test-favicons.js", "validate:env": "node scripts/validate-env.js", "generate:secrets": "node scripts/generate-production-secrets.js", "test:database": "node scripts/test-database-connection.js", "setup:database:production": "node scripts/setup-production-database.js", "setup:services": "node scripts/setup-third-party-services.js", "setup:ssl": "chmod +x scripts/setup-ssl-certificates.sh && sudo ./scripts/setup-ssl-certificates.sh", "verify:ssl": "node scripts/verify-ssl-setup.js", "verify:schema": "node scripts/verify-database-schema.js", "package:deployment": "node scripts/create-deployment-package.js", "deploy:update": "chmod +x scripts/update-deployment.sh && sudo ./scripts/update-deployment.sh", "deploy:rollback": "chmod +x scripts/rollback-deployment.sh && sudo ./scripts/rollback-deployment.sh", "setup:monitoring": "node scripts/setup-monitoring.js", "setup:backups": "chmod +x scripts/setup-backups.sh && sudo ./scripts/setup-backups.sh", "setup:cdn": "node scripts/setup-cdn.js", "optimize:assets": "chmod +x scripts/optimize-assets.sh && ./scripts/optimize-assets.sh", "deploy:fullstack": "scripts\\deploy-windows.bat", "deploy:fullstack:ps": "powershell -ExecutionPolicy Bypass -File scripts/deploy-fullstack-production.ps1", "deploy:fullstack:linux": "chmod +x scripts/deploy-fullstack-production.sh && ./scripts/deploy-fullstack-production.sh", "deploy:vps": "chmod +x scripts/deploy-to-vps.sh && ./scripts/deploy-to-vps.sh", "create:tables": "node scripts/create-database-tables.js", "seed:database": "node scripts/seed-database.js", "seed:comprehensive": "node scripts/seed-comprehensive-data.js", "add:products": "node scripts/add-more-products.js", "test:auth": "node scripts/test-authentication-system.js", "test:social-auth": "node scripts/test-social-auth.js", "fix:admin": "node scripts/fix-admin-password.js", "status": "node scripts/system-status.js", "setup:security": "node scripts/setup-security.js", "setup:database:dev": "node scripts/setup-database.js", "setup:rbac": "node scripts/setup-rbac.js", "test:rbac": "node scripts/test-rbac-system.js", "test:production-readiness": "node scripts/test-production-readiness.js", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "deploy:hostinger": "node scripts/deploy-to-hostinger.js", "build:admin-deployment": "node scripts/build-admin-deployment.js", "build:production-deployment": "node scripts/create-production-deployment.js", "test:production": "node scripts/test-production-sites.js", "docker:build": "chmod +x scripts/docker-build.sh && ./scripts/docker-build.sh", "docker:deploy": "chmod +x scripts/docker-deploy.sh && ./scripts/docker-deploy.sh", "docker:test": "docker run --rm --env-file .env.production nirvana-organics-test:latest", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "test:comprehensive": "chmod +x docker/test-runner.sh && ./docker/test-runner.sh", "start:prod": "NODE_ENV=production node server/index.js", "migrate:prod": "NODE_ENV=production node scripts/run-migrations.js", "seed:prod": "NODE_ENV=production node scripts/seed-production-data.js", "backup:db": "chmod +x scripts/backup-database.sh && ./scripts/backup-database.sh", "backup:setup": "node scripts/setup-backup-system.js", "test:integration": "node scripts/test-production-integration.js", "fix:dependencies": "node scripts/fix-dependencies.js", "test:google-oauth": "node scripts/test-google-oauth-integration.js", "test:quick": "node scripts/quick-deployment-test.js", "backup:files": "chmod +x scripts/backup-files.sh && ./scripts/backup-files.sh"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.5.0", "axios": "^1.7.9", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.2", "express-slow-down": "^2.1.0", "express-validator": "^7.2.0", "framer-motion": "^11.15.0", "google-auth-library": "^10.2.0", "handlebars": "^4.7.8", "helmet": "^8.0.0", "isomorphic-dompurify": "^2.26.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "nodemailer": "^6.9.18", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-redux": "^9.2.0", "react-router-dom": "^6.28.0", "recharts": "^3.1.0", "sequelize": "^6.37.5", "socket.io": "^4.8.1", "square": "^39.0.0", "stripe": "^17.5.0", "terser": "^5.43.1", "web-push": "^3.6.7", "xml2js": "^0.6.2", "xss": "^1.0.15", "yup": "^1.6.0"}, "devDependencies": {"@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "jsdom": "^26.1.0", "nodemon": "^3.1.10", "postcss": "^8.5.6", "postcss-nested": "^7.0.2", "sharp": "^0.34.3", "socket.io-client": "^4.8.1", "supertest": "^7.1.4", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.7", "vitest": "^2.1.8"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11", "Chrome >= 120", "Firefox >= 115", "Safari >= 16", "Edge >= 120"]}