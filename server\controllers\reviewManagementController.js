const { Review, User, Product, Order, AuditLog } = require('../models');
const { Op, sequelize } = require('sequelize');
const { validationResult } = require('express-validator');

// Get all reviews with advanced filtering and pagination
const getReviews = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      rating,
      verified,
      productId,
      userId,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      dateFrom,
      dateTo,
      includeProduct = true,
      includeUser = true
    } = req.query;

    // Build where clause
    const where = {};
    
    if (status && status !== 'all') {
      where.status = status;
    }
    
    if (rating && rating !== 'all') {
      where.rating = parseInt(rating);
    }
    
    if (verified === 'true') {
      where.verified = true;
    } else if (verified === 'false') {
      where.verified = false;
    }
    
    if (productId) {
      where.productId = parseInt(productId);
    }
    
    if (userId) {
      where.userId = parseInt(userId);
    }
    
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt[Op.gte] = new Date(dateFrom);
      if (dateTo) where.createdAt[Op.lte] = new Date(dateTo);
    }

    // Build include array
    const include = [];
    
    if (includeUser === 'true') {
      include.push({
        model: User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName', 'email']
      });
    }
    
    if (includeProduct === 'true') {
      include.push({
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'slug', 'images']
      });
    }

    // Handle search across multiple fields
    if (search) {
      const searchConditions = [
        { title: { [Op.iLike]: `%${search}%` } },
        { comment: { [Op.iLike]: `%${search}%` } }
      ];
      
      if (includeUser === 'true') {
        searchConditions.push(
          { '$user.firstName$': { [Op.iLike]: `%${search}%` } },
          { '$user.lastName$': { [Op.iLike]: `%${search}%` } },
          { '$user.email$': { [Op.iLike]: `%${search}%` } }
        );
      }
      
      if (includeProduct === 'true') {
        searchConditions.push(
          { '$product.name$': { [Op.iLike]: `%${search}%` } }
        );
      }
      
      where[Op.or] = searchConditions;
    }

    const { count, rows: reviews } = await Review.findAndCountAll({
      where,
      include,
      order: [[sortBy, sortOrder.toUpperCase()]],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit),
      distinct: true
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_REVIEWS', 'REVIEW', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { filters: req.query }
    });

    res.json({
      success: true,
      data: {
        reviews,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalReviews: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get reviews error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reviews',
      error: error.message
    });
  }
};

// Get single review by ID
const getReviewById = async (req, res) => {
  try {
    const { reviewId } = req.params;

    const review = await Review.findByPk(reviewId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'slug', 'images', 'price']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'orderNumber', 'createdAt']
        }
      ]
    });

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_REVIEW', 'REVIEW', reviewId, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: { review }
    });

  } catch (error) {
    console.error('Get review by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch review',
      error: error.message
    });
  }
};

// Update review status (approve/reject)
const updateReviewStatus = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { reviewId } = req.params;
    const { status, adminResponse } = req.body;

    // Find review
    const review = await Review.findByPk(reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Store old values for audit log
    const oldValues = review.toJSON();

    // Update review
    const updateData = { status };
    if (adminResponse) {
      updateData.adminResponse = adminResponse;
      updateData.adminResponseDate = new Date();
    }

    await review.update(updateData);

    // Get updated review
    const updatedReview = await Review.findByPk(reviewId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'slug']
        }
      ]
    });

    // Log admin action
    await AuditLog.logUpdate(req.user.id, 'REVIEW', reviewId, oldValues, updatedReview.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.json({
      success: true,
      message: `Review ${status} successfully`,
      data: { review: updatedReview }
    });

  } catch (error) {
    console.error('Update review status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update review status',
      error: error.message
    });
  }
};

// Add admin response to review
const addAdminResponse = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { reviewId } = req.params;
    const { adminResponse } = req.body;

    // Find review
    const review = await Review.findByPk(reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Store old values for audit log
    const oldValues = review.toJSON();

    // Update review with admin response
    await review.update({
      adminResponse,
      adminResponseDate: new Date()
    });

    // Get updated review
    const updatedReview = await Review.findByPk(reviewId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'slug']
        }
      ]
    });

    // Log admin action
    await AuditLog.logUpdate(req.user.id, 'REVIEW', reviewId, oldValues, updatedReview.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'low'
    });

    res.json({
      success: true,
      message: 'Admin response added successfully',
      data: { review: updatedReview }
    });

  } catch (error) {
    console.error('Add admin response error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add admin response',
      error: error.message
    });
  }
};

// Delete review
const deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;

    // Find review
    const review = await Review.findByPk(reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Store old values for audit log
    const oldValues = review.toJSON();

    // Delete review
    await review.destroy();

    // Log admin action
    await AuditLog.logDelete(req.user.id, 'REVIEW', reviewId, oldValues, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'high'
    });

    res.json({
      success: true,
      message: 'Review deleted successfully'
    });

  } catch (error) {
    console.error('Delete review error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete review',
      error: error.message
    });
  }
};

// Bulk operations on reviews
const bulkUpdateReviews = async (req, res) => {
  try {
    const { reviewIds, action, data } = req.body;

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Review IDs array is required'
      });
    }

    let updateData = {};
    let actionName = '';

    switch (action) {
      case 'approve':
        updateData = { status: 'approved' };
        actionName = 'BULK_APPROVE';
        break;
      case 'reject':
        updateData = { status: 'rejected' };
        actionName = 'BULK_REJECT';
        break;
      case 'pending':
        updateData = { status: 'pending' };
        actionName = 'BULK_PENDING';
        break;
      case 'delete':
        actionName = 'BULK_DELETE';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    let affectedCount = 0;

    if (action === 'delete') {
      // Delete reviews
      affectedCount = await Review.destroy({
        where: {
          id: { [Op.in]: reviewIds }
        }
      });
    } else {
      // Update reviews
      const [updatedCount] = await Review.update(updateData, {
        where: {
          id: { [Op.in]: reviewIds }
        }
      });
      affectedCount = updatedCount;
    }

    // Log admin action
    await AuditLog.logBulkAction(req.user.id, actionName, 'REVIEW', reviewIds, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { updateData },
      severity: 'high'
    });

    res.json({
      success: true,
      message: `Successfully ${action}d ${affectedCount} reviews`,
      data: { affectedCount }
    });

  } catch (error) {
    console.error('Bulk update reviews error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update reviews',
      error: error.message
    });
  }
};

// Get review statistics
const getReviewStatistics = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total reviews
      Review.count(),

      // Reviews by status
      Review.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status']
      }),

      // Reviews by rating
      Review.findAll({
        attributes: [
          'rating',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['rating'],
        order: [['rating', 'DESC']]
      }),

      // Verified vs unverified reviews
      Review.findAll({
        attributes: [
          'verified',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['verified']
      }),

      // Reviews this month
      Review.count({
        where: {
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),

      // Average rating
      Review.findOne({
        attributes: [
          [sequelize.fn('AVG', sequelize.col('rating')), 'averageRating']
        ],
        where: { status: 'approved' }
      }),

      // Reviews with admin responses
      Review.count({
        where: {
          adminResponse: { [Op.not]: null }
        }
      })
    ]);

    const [
      totalReviews,
      reviewsByStatus,
      reviewsByRating,
      reviewsByVerification,
      reviewsThisMonth,
      averageRatingResult,
      reviewsWithResponses
    ] = stats;

    const averageRating = averageRatingResult?.dataValues?.averageRating || 0;

    res.json({
      success: true,
      data: {
        totalReviews,
        reviewsByStatus,
        reviewsByRating,
        reviewsByVerification,
        reviewsThisMonth,
        averageRating: Math.round(averageRating * 10) / 10,
        reviewsWithResponses,
        reviewsWithoutResponses: totalReviews - reviewsWithResponses
      }
    });

  } catch (error) {
    console.error('Get review statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch review statistics',
      error: error.message
    });
  }
};

// Get reviews for a specific product
const getProductReviews = async (req, res) => {
  try {
    const { productId } = req.params;
    const { page = 1, limit = 20, status = 'all' } = req.query;

    const where = { productId: parseInt(productId) };
    if (status !== 'all') {
      where.status = status;
    }

    const { count, rows: reviews } = await Review.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order: [['createdAt', 'DESC']],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit)
    });

    // Get product rating statistics
    const ratingStats = await Review.getProductStats(productId);

    res.json({
      success: true,
      data: {
        reviews,
        ratingStats,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalReviews: count,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get product reviews error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product reviews',
      error: error.message
    });
  }
};

module.exports = {
  getReviews,
  getReviewById,
  updateReviewStatus,
  addAdminResponse,
  deleteReview,
  bulkUpdateReviews,
  getReviewStatistics,
  getProductReviews
};
