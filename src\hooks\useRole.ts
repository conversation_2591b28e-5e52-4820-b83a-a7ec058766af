import { useMemo } from 'react';
import { useAppSelector } from './redux';
import { 
  getUserPermissions, 
  hasRole, 
  hasPermission, 
  isAdmin, 
  isManager, 
  isCustomer, 
  isManagerOrAdmin,
  canAccessRoute,
  getDashboardRoute,
  getNavigationItems,
  getRoleDisplayName,
  getRoleBadgeColor,
  UserRole,
  Permission
} from '../utils/roleUtils';

/**
 * Custom hook for role-based access control
 */
export const useRole = () => {
  const { user } = useAppSelector((state) => state.auth);

  const permissions = useMemo(() => {
    return user ? getUserPermissions(user.role) : null;
  }, [user?.role]);

  const navigationItems = useMemo(() => {
    return getNavigationItems(user);
  }, [user]);

  const dashboardRoute = useMemo(() => {
    return getDashboardRoute(user);
  }, [user]);

  return {
    user,
    permissions,
    navigationItems,
    dashboardRoute,
    
    // Role checking functions
    hasRole: (role: UserRole | UserRole[]) => hasRole(user, role),
    isAdmin: () => isAdmin(user),
    isManager: () => isManager(user),
    isCustomer: () => isCustomer(user),
    isManagerOrAdmin: () => isManagerOrAdmin(user),
    
    // Permission checking
    hasPermission: (permission: keyof Permission) => hasPermission(user, permission),
    canAccessRoute: (route: string) => canAccessRoute(user, route),
    
    // Display utilities
    getRoleDisplayName: () => user ? getRoleDisplayName(user.role) : 'Guest',
    getRoleBadgeColor: () => user ? getRoleBadgeColor(user.role) : 'bg-gray-100 text-gray-800'
  };
};

/**
 * Hook for checking specific permissions
 */
export const usePermission = (permission: keyof Permission) => {
  const { user } = useAppSelector((state) => state.auth);
  
  return useMemo(() => {
    return hasPermission(user, permission);
  }, [user, permission]);
};

/**
 * Hook for checking specific roles
 */
export const useHasRole = (role: UserRole | UserRole[]) => {
  const { user } = useAppSelector((state) => state.auth);
  
  return useMemo(() => {
    return hasRole(user, role);
  }, [user, role]);
};

/**
 * Hook for admin-only functionality
 */
export const useIsAdmin = () => {
  const { user } = useAppSelector((state) => state.auth);
  
  return useMemo(() => {
    return isAdmin(user);
  }, [user]);
};

/**
 * Hook for manager-only functionality
 */
export const useIsManager = () => {
  const { user } = useAppSelector((state) => state.auth);
  
  return useMemo(() => {
    return isManager(user);
  }, [user]);
};

/**
 * Hook for manager or admin functionality
 */
export const useIsManagerOrAdmin = () => {
  const { user } = useAppSelector((state) => state.auth);
  
  return useMemo(() => {
    return isManagerOrAdmin(user);
  }, [user]);
};

/**
 * Hook for route access checking
 */
export const useCanAccessRoute = (route: string) => {
  const { user } = useAppSelector((state) => state.auth);
  
  return useMemo(() => {
    return canAccessRoute(user, route);
  }, [user, route]);
};
