import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AdminLayout from '../AdminLayout';
import authSlice from '../../../store/slices/authSlice';

// Mock the navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/admin/dashboard' })
}));

// Create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice
    },
    preloadedState: {
      auth: {
        user: {
          id: 1,
          firstName: 'Test',
          lastName: 'Admin',
          email: '<EMAIL>',
          role: 'admin'
        },
        token: 'test-token',
        isAuthenticated: true,
        loading: false,
        error: null,
        ...initialState.auth
      }
    }
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createTestStore(initialState);
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('AdminLayout', () => {
  const TestComponent = () => <div>Test Content</div>;

  beforeEach(() => {
    mockNavigate.mockClear();
  });

  test('renders admin layout with navigation', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Check if main navigation items are present
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('Orders')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Categories')).toBeInTheDocument();
    expect(screen.getByText('Reviews')).toBeInTheDocument();
    expect(screen.getByText('Coupons')).toBeInTheDocument();

    // Check if content is rendered
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('displays user information in header', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    expect(screen.getByText('Test Admin')).toBeInTheDocument();
    expect(screen.getByText('admin')).toBeInTheDocument();
  });

  test('toggles mobile sidebar', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Find and click the mobile menu button
    const mobileMenuButton = screen.getByRole('button', { name: /open sidebar/i });
    fireEvent.click(mobileMenuButton);

    // Mobile sidebar should be visible
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  test('expands and collapses navigation items', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Find Analytics navigation item (has children)
    const analyticsItem = screen.getByText('Analytics');
    fireEvent.click(analyticsItem);

    // Check if sub-items are visible
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Sales')).toBeInTheDocument();
    expect(screen.getByText('Customers')).toBeInTheDocument();

    // Click again to collapse
    fireEvent.click(analyticsItem);
  });

  test('navigates to different pages', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Click on Products navigation item
    const productsItem = screen.getByText('Products');
    fireEvent.click(productsItem);

    // Should show sub-items
    expect(screen.getByText('All Products')).toBeInTheDocument();
    expect(screen.getByText('Add Product')).toBeInTheDocument();

    // Click on "All Products"
    const allProductsItem = screen.getByText('All Products');
    fireEvent.click(allProductsItem);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/products');
  });

  test('handles logout', async () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Find and click logout button
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    fireEvent.click(logoutButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  test('shows notification badge', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Check if notification bell is present
    const notificationButton = screen.getByRole('button', { name: /notifications/i });
    expect(notificationButton).toBeInTheDocument();
  });

  test('displays badges for navigation items', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Orders should have a badge (5 pending orders)
    const ordersSection = screen.getByText('Orders').closest('div');
    expect(ordersSection).toHaveTextContent('5');

    // Reviews should have a badge (12 pending reviews)
    const reviewsSection = screen.getByText('Reviews').closest('div');
    expect(reviewsSection).toHaveTextContent('12');
  });

  test('handles responsive design', () => {
    // Mock window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });

    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // On mobile, sidebar should be hidden by default
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  test('auto-expands active parent items', () => {
    // Mock location to be in analytics section
    jest.doMock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useLocation: () => ({ pathname: '/admin/analytics/sales' })
    }));

    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Analytics should be expanded and Sales should be visible
    expect(screen.getByText('Sales')).toBeInTheDocument();
  });

  test('renders with different user roles', () => {
    const managerState = {
      auth: {
        user: {
          id: 2,
          firstName: 'Test',
          lastName: 'Manager',
          email: '<EMAIL>',
          role: 'manager'
        },
        token: 'test-token',
        isAuthenticated: true,
        loading: false,
        error: null
      }
    };

    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>,
      managerState
    );

    expect(screen.getByText('Test Manager')).toBeInTheDocument();
    expect(screen.getByText('manager')).toBeInTheDocument();
  });

  test('handles keyboard navigation', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    const dashboardItem = screen.getByText('Dashboard');
    
    // Focus on dashboard item
    dashboardItem.focus();
    expect(dashboardItem).toHaveFocus();

    // Press Enter to navigate
    fireEvent.keyDown(dashboardItem, { key: 'Enter', code: 'Enter' });
    expect(mockNavigate).toHaveBeenCalledWith('/admin/dashboard');
  });

  test('closes mobile sidebar when clicking outside', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Open mobile sidebar
    const mobileMenuButton = screen.getByRole('button', { name: /open sidebar/i });
    fireEvent.click(mobileMenuButton);

    // Click on overlay
    const overlay = screen.getByRole('dialog').previousSibling;
    fireEvent.click(overlay as Element);

    // Sidebar should be closed
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  test('displays correct logo and branding', () => {
    renderWithProviders(
      <AdminLayout>
        <TestComponent />
      </AdminLayout>
    );

    // Check for logo image
    const logo = screen.getByAltText('Nirvana Organics');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', '/Nirvana_logo.png');

    // Check for admin text
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });
});
