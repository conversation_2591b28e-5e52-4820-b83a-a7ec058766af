/**
 * XML Sitemap Generator for Nirvana Organics
 * Generates dynamic sitemap based on products and static pages
 */

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export class SitemapGenerator {
  private baseUrl: string;
  private urls: SitemapUrl[] = [];

  constructor(baseUrl: string = 'https://shopnirvanaorganics.com') {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  /**
   * Add static pages to sitemap
   */
  addStaticPages(): void {
    const staticPages: SitemapUrl[] = [
      {
        loc: `${this.baseUrl}/`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 1.0
      },
      {
        loc: `${this.baseUrl}/shop`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/about`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.7
      },
      {
        loc: `${this.baseUrl}/contact`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.7
      },
      {
        loc: `${this.baseUrl}/faq`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'weekly',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/privacy`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'yearly',
        priority: 0.3
      },
      {
        loc: `${this.baseUrl}/terms`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'yearly',
        priority: 0.3
      },
      {
        loc: `${this.baseUrl}/shipping`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.6
      },
      {
        loc: `${this.baseUrl}/returns`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: 0.6
      }
    ];

    this.urls.push(...staticPages);
  }

  /**
   * Add category pages to sitemap
   */
  addCategoryPages(): void {
    const categories = [
      'flowers',
      'edibles',
      'pre-rolls',
      'concentrates',
      'vapes',
      'chocolates'
    ];

    const cannabinoids = [
      'CBD',
      'THC-A',
      'Delta-8',
      'Delta-9'
    ];

    // Add category pages
    categories.forEach(category => {
      this.urls.push({
        loc: `${this.baseUrl}/shop?category=${category}`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.8
      });
    });

    // Add cannabinoid pages
    cannabinoids.forEach(cannabinoid => {
      this.urls.push({
        loc: `${this.baseUrl}/shop?cannabinoid=${cannabinoid}`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.8
      });
    });

    // Add special pages
    this.urls.push(
      {
        loc: `${this.baseUrl}/shop?featured=true`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/shop?bestSeller=true`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'daily',
        priority: 0.8
      }
    );
  }

  /**
   * Add product pages to sitemap
   */
  addProductPages(products: Array<{ slug: string; updatedAt?: string }>): void {
    products.forEach(product => {
      this.urls.push({
        loc: `${this.baseUrl}/product/${product.slug}`,
        lastmod: product.updatedAt 
          ? new Date(product.updatedAt).toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
        changefreq: 'weekly',
        priority: 0.9
      });
    });
  }

  /**
   * Generate XML sitemap string
   */
  generateXML(): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>\n';
    const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    const urlsetClose = '</urlset>';

    const urlElements = this.urls.map(url => {
      let urlXml = '  <url>\n';
      urlXml += `    <loc>${this.escapeXml(url.loc)}</loc>\n`;
      
      if (url.lastmod) {
        urlXml += `    <lastmod>${url.lastmod}</lastmod>\n`;
      }
      
      if (url.changefreq) {
        urlXml += `    <changefreq>${url.changefreq}</changefreq>\n`;
      }
      
      if (url.priority !== undefined) {
        urlXml += `    <priority>${url.priority.toFixed(1)}</priority>\n`;
      }
      
      urlXml += '  </url>\n';
      return urlXml;
    }).join('');

    return xmlHeader + urlsetOpen + urlElements + urlsetClose;
  }

  /**
   * Generate sitemap index for multiple sitemaps
   */
  generateSitemapIndex(sitemaps: Array<{ loc: string; lastmod?: string }>): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>\n';
    const sitemapIndexOpen = '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    const sitemapIndexClose = '</sitemapindex>';

    const sitemapElements = sitemaps.map(sitemap => {
      let sitemapXml = '  <sitemap>\n';
      sitemapXml += `    <loc>${this.escapeXml(sitemap.loc)}</loc>\n`;
      
      if (sitemap.lastmod) {
        sitemapXml += `    <lastmod>${sitemap.lastmod}</lastmod>\n`;
      }
      
      sitemapXml += '  </sitemap>\n';
      return sitemapXml;
    }).join('');

    return xmlHeader + sitemapIndexOpen + sitemapElements + sitemapIndexClose;
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(unsafe: string): string {
    return unsafe.replace(/[<>&'"]/g, (c) => {
      switch (c) {
        case '<': return '&lt;';
        case '>': return '&gt;';
        case '&': return '&amp;';
        case '\'': return '&apos;';
        case '"': return '&quot;';
        default: return c;
      }
    });
  }

  /**
   * Get all URLs in the sitemap
   */
  getUrls(): SitemapUrl[] {
    return [...this.urls];
  }

  /**
   * Clear all URLs
   */
  clear(): void {
    this.urls = [];
  }

  /**
   * Get sitemap statistics
   */
  getStats(): { totalUrls: number; byPriority: Record<string, number> } {
    const stats = {
      totalUrls: this.urls.length,
      byPriority: {} as Record<string, number>
    };

    this.urls.forEach(url => {
      const priority = url.priority?.toString() || 'undefined';
      stats.byPriority[priority] = (stats.byPriority[priority] || 0) + 1;
    });

    return stats;
  }
}

/**
 * Generate complete sitemap for the website
 */
export const generateCompleteSitemap = async (
  products: Array<{ slug: string; updatedAt?: string }> = []
): Promise<string> => {
  const generator = new SitemapGenerator();
  
  // Add all page types
  generator.addStaticPages();
  generator.addCategoryPages();
  generator.addProductPages(products);
  
  return generator.generateXML();
};

/**
 * Generate robots.txt content
 */
export const generateRobotsTxt = (baseUrl: string = 'https://shopnirvanaorganics.com'): string => {
  return `User-agent: *
Allow: /

# Disallow admin and private pages
Disallow: /admin/
Disallow: /api/
Disallow: /login
Disallow: /register
Disallow: /profile
Disallow: /orders
Disallow: /cart
Disallow: /checkout

# Allow important pages
Allow: /
Allow: /shop
Allow: /product/
Allow: /about
Allow: /contact
Allow: /faq
Allow: /privacy
Allow: /terms
Allow: /shipping
Allow: /returns

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;
};

export default SitemapGenerator;
