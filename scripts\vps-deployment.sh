#!/bin/bash

# ============================================================================
# VPS Deployment Script for Nirvana Organics Backend
# Complete automated deployment for Virtual Private Server
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${CYAN}🔧 $1${NC}"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Configuration
PROJECT_NAME="nirvana-organics-backend"
PROJECT_PATH="/var/www/$PROJECT_NAME"
BACKUP_PATH="/var/backups/$PROJECT_NAME"
LOG_FILE="/var/log/$PROJECT_NAME-deployment.log"
DOMAIN="shopnirvanaorganics.com"
WWW_DOMAIN="www.shopnirvanaorganics.com"
EMAIL="<EMAIL>"

# Create log file
touch "$LOG_FILE"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "============================================================================"
log_header "🚀 NIRVANA ORGANICS VPS DEPLOYMENT"
log_header "   Complete Backend Deployment for Production"
echo "============================================================================"
echo "Started: $(date)"
echo "Domain: $DOMAIN"
echo "Project Path: $PROJECT_PATH"
echo "============================================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: System Update and Prerequisites
log_step "Step 1: Updating system and installing prerequisites"

# Update system packages
log_info "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
log_info "Installing essential packages..."
apt install -y curl wget git nginx mysql-server certbot python3-certbot-nginx \
    ufw fail2ban htop unzip software-properties-common build-essential

log_success "System updated and prerequisites installed"

# Step 2: Install Node.js 18.x
log_step "Step 2: Installing Node.js 18.x"

if ! command -v node &> /dev/null; then
    log_info "Installing Node.js 18.x..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt install -y nodejs
    log_success "Node.js installed: $(node --version)"
else
    log_info "Node.js already installed: $(node --version)"
fi

# Install PM2 globally
if ! command -v pm2 &> /dev/null; then
    log_info "Installing PM2 process manager..."
    npm install -g pm2
    log_success "PM2 installed: $(pm2 --version)"
else
    log_info "PM2 already installed: $(pm2 --version)"
fi

# Step 3: Configure MySQL
log_step "Step 3: Configuring MySQL database"

# Secure MySQL installation
log_info "Securing MySQL installation..."
mysql_secure_installation

# Create database and user
log_info "Setting up database..."
read -p "Enter MySQL root password: " -s MYSQL_ROOT_PASSWORD
echo

# Database configuration
DB_NAME="u106832845_nirvana"
DB_USER="u106832845_root"
read -p "Enter database password for $DB_USER: " -s DB_PASSWORD
echo

# Create database if it doesn't exist
mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
"

log_success "Database configured"

# Step 4: Configure Firewall
log_step "Step 4: Configuring firewall"

# Configure UFW
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3306/tcp  # MySQL (restrict this in production)
ufw --force enable

log_success "Firewall configured"

# Step 5: Create Project Structure
log_step "Step 5: Creating project structure"

# Create project directories
mkdir -p "$PROJECT_PATH"/{current,shared/{logs,uploads,config},releases}
mkdir -p "$BACKUP_PATH"

# Create nirvana user for running the application
if ! id "nirvana" &>/dev/null; then
    useradd -r -s /bin/bash -d "$PROJECT_PATH" nirvana
    log_success "Created nirvana user"
fi

# Set permissions
chown -R nirvana:nirvana "$PROJECT_PATH"
chmod -R 755 "$PROJECT_PATH"

log_success "Project structure created"

# Step 6: Extract and Setup Application
log_step "Step 6: Setting up application"

# Check if deployment package exists
PACKAGE_FILE=$(find . -name "nirvana-organics-backend-production-*.tar.gz" | head -1)

if [ -z "$PACKAGE_FILE" ]; then
    log_error "Deployment package not found!"
    log_info "Please upload the deployment package (nirvana-organics-backend-production-*.tar.gz) to this directory"
    exit 1
fi

log_info "Found deployment package: $PACKAGE_FILE"

# Extract package
RELEASE_DIR="$PROJECT_PATH/releases/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RELEASE_DIR"

log_info "Extracting package to $RELEASE_DIR..."
tar -xzf "$PACKAGE_FILE" -C "$RELEASE_DIR" --strip-components=1

# Set ownership
chown -R nirvana:nirvana "$RELEASE_DIR"

# Create symlink to current
ln -sfn "$RELEASE_DIR" "$PROJECT_PATH/current"

log_success "Application extracted and linked"

# Step 7: Install Dependencies and Configure
log_step "Step 7: Installing dependencies and configuring application"

cd "$PROJECT_PATH/current"

# Install production dependencies
log_info "Installing Node.js dependencies..."
sudo -u nirvana npm ci --production

# Generate production secrets
log_info "Generating production secrets..."
sudo -u nirvana npm run generate:secrets

# Configure environment
log_info "Configuring production environment..."
if [ -f ".env.production" ]; then
    cp .env.production .env
    
    # Update database configuration in .env
    sed -i "s/DB_NAME=.*/DB_NAME=$DB_NAME/" .env
    sed -i "s/DB_USER=.*/DB_USER=$DB_USER/" .env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env
    sed -i "s/NODE_ENV=.*/NODE_ENV=production/" .env
    
    chown nirvana:nirvana .env
    chmod 600 .env
    
    log_success "Environment configured"
else
    log_error ".env.production not found in package"
    exit 1
fi

# Step 8: Database Setup
log_step "Step 8: Setting up database"

# Test database connection
log_info "Testing database connection..."
sudo -u nirvana npm run test:database

# Run migrations
log_info "Running database migrations..."
sudo -u nirvana npm run migrate:prod

# Seed production data if needed
read -p "Do you want to seed initial data? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Seeding production data..."
    sudo -u nirvana npm run seed:prod
fi

log_success "Database setup completed"

# Step 9: Configure Nginx
log_step "Step 9: Configuring Nginx"

# Create Nginx configuration
cat > /etc/nginx/sites-available/$PROJECT_NAME << EOF
# Nirvana Organics Backend - Nginx Configuration
server {
    listen 80;
    server_name $DOMAIN $WWW_DOMAIN;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    # Main API
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files and uploads
    location /uploads/ {
        alias $PROJECT_PATH/shared/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://localhost:5000/health;
    }
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
}
EOF

# Enable site
ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t
if [ $? -eq 0 ]; then
    systemctl reload nginx
    log_success "Nginx configured and reloaded"
else
    log_error "Nginx configuration error"
    exit 1
fi

# Step 10: Setup SSL Certificates
log_step "Step 10: Setting up SSL certificates"

# Obtain SSL certificates
log_info "Obtaining SSL certificates with Let's Encrypt..."
certbot --nginx -d $DOMAIN -d $WWW_DOMAIN \
    --non-interactive \
    --agree-tos \
    --email $EMAIL \
    --redirect

if [ $? -eq 0 ]; then
    log_success "SSL certificates obtained and configured"
    
    # Setup auto-renewal
    echo "0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx" | crontab -
    log_success "SSL auto-renewal configured"
else
    log_warning "SSL certificate setup failed - continuing with HTTP"
fi

# Step 11: Start Application with PM2
log_step "Step 11: Starting application with PM2"

# Start application
sudo -u nirvana pm2 start ecosystem.config.js --env production

# Save PM2 configuration
sudo -u nirvana pm2 save

# Setup PM2 startup
pm2 startup systemd -u nirvana --hp "$PROJECT_PATH"

log_success "Application started with PM2"

# Step 12: Configure Log Rotation
log_step "Step 12: Configuring log rotation"

cat > /etc/logrotate.d/$PROJECT_NAME << EOF
$PROJECT_PATH/shared/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nirvana nirvana
    postrotate
        sudo -u nirvana pm2 reloadLogs
    endscript
}

$LOG_FILE {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

log_success "Log rotation configured"

# Step 13: Final Verification
log_step "Step 13: Running final verification"

# Wait for application to start
sleep 10

# Check PM2 status
sudo -u nirvana pm2 status

# Test application health
if curl -f -s http://localhost:5000/health >/dev/null; then
    log_success "Application health check passed"
else
    log_warning "Application health check failed - check logs"
fi

# Test HTTPS if SSL was configured
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    if curl -f -s https://$DOMAIN/health >/dev/null; then
        log_success "HTTPS health check passed"
    else
        log_warning "HTTPS health check failed"
    fi
fi

# Final status
echo ""
echo "============================================================================"
log_success "🎉 VPS DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "============================================================================"
echo "Completed: $(date)"
echo ""

log_info "Deployment Summary:"
echo "• Project Path: $PROJECT_PATH"
echo "• Domain: $DOMAIN"
echo "• SSL: $([ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ] && echo "Enabled" || echo "Not configured")"
echo "• Application Status: $(sudo -u nirvana pm2 list | grep nirvana | awk '{print $10}' | head -1)"
echo "• Database: Configured and migrated"
echo "• Nginx: Configured and running"

echo ""
log_step "Next Steps:"
echo "1. Update .env file with production API keys and secrets"
echo "2. Test all application functionality"
echo "3. Configure monitoring and backups"
echo "4. Review security settings"

echo ""
log_warning "Important Files:"
echo "• Environment: $PROJECT_PATH/current/.env"
echo "• Logs: $PROJECT_PATH/shared/logs/"
echo "• Deployment Log: $LOG_FILE"
echo "• PM2 Status: sudo -u nirvana pm2 status"

echo ""
log_info "Useful Commands:"
echo "• View logs: sudo -u nirvana pm2 logs"
echo "• Restart app: sudo -u nirvana pm2 restart all"
echo "• Check status: sudo -u nirvana pm2 status"
echo "• Nginx reload: systemctl reload nginx"

echo ""
log_success "Deployment completed successfully! 🚀"
