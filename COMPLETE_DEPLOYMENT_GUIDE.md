# Complete Deployment Guide
## Nirvana Organics E-commerce Platform - Production Ready

### 🎯 Overview

This is the complete deployment guide for the Nirvana Organics e-commerce backend. This package contains everything needed for a production-ready deployment on a VPS, including automated scripts, monitoring, backups, and comprehensive documentation.

## 📦 Package Contents

### Core Application Files
- **`server/`** - Backend application code
- **`scripts/`** - Deployment, monitoring, and maintenance scripts
- **`public/`** - Static assets and uploads
- **`.env.production.final`** - Production environment template
- **`ecosystem.config.js`** - PM2 process management configuration
- **`package.json`** - Production dependencies and scripts

### Deployment Scripts
- **`scripts/vps-deployment.sh`** - Complete VPS setup and initial deployment
- **`scripts/update-deployment.sh`** - Update existing deployment with new release
- **`scripts/rollback-deployment.sh`** - Rollback to previous deployment
- **`scripts/create-deployment-package.js`** - Create deployment packages

### Configuration Scripts
- **`scripts/setup-ssl-certificates.sh`** - SSL certificate setup with Let's Encrypt
- **`scripts/setup-monitoring.sh`** - Production monitoring setup
- **`scripts/setup-backups.sh`** - Automated backup configuration
- **`scripts/setup-production-database.js`** - Database setup and validation
- **`scripts/setup-third-party-services.js`** - Third-party service validation

### Monitoring and Maintenance
- **`scripts/verify-ssl-setup.js`** - SSL certificate verification
- **`scripts/generate-production-secrets.js`** - Secure secret generation
- **Health check scripts** - Application and system monitoring
- **Backup scripts** - Database and file backup automation

### Documentation
- **`VPS_DEPLOYMENT_GUIDE.md`** - Step-by-step VPS deployment instructions
- **`PRODUCTION_CONFIGURATION_CHECKLIST.md`** - Configuration checklist
- **`SQUARE_PRODUCTION_SETUP.md`** - Square payment configuration
- **`SSL_SETUP_GUIDE.md`** - SSL certificate setup guide
- **`PRODUCTION_DEPLOYMENT_SUMMARY.md`** - Deployment summary

## 🚀 Quick Start Deployment

### Prerequisites
- Ubuntu 20.04+ VPS with root access
- Domain name pointing to VPS IP
- 2GB+ RAM, 20GB+ storage
- Ports 80, 443, 22 open

### 1. Upload and Extract Package
```bash
# Upload package to VPS
scp nirvana-organics-backend-production-*.tar.gz root@YOUR_VPS_IP:/root/

# SSH into VPS and extract
ssh root@YOUR_VPS_IP
tar -xzf nirvana-organics-backend-production-*.tar.gz
cd nirvana-organics-backend-production
```

### 2. Run Automated Deployment
```bash
# Make deployment script executable
chmod +x scripts/vps-deployment.sh

# Run complete deployment
sudo ./scripts/vps-deployment.sh
```

The script will automatically:
- Update system and install prerequisites (Node.js, MySQL, Nginx, PM2)
- Configure firewall and security
- Setup database and user
- Extract and configure application
- Install dependencies and run migrations
- Configure Nginx reverse proxy
- Setup SSL certificates with Let's Encrypt
- Start application with PM2
- Configure monitoring and logging

### 3. Post-Deployment Configuration

#### Update Production Credentials
Edit `/var/www/nirvana-organics-backend/current/.env`:

```bash
# CRITICAL: Update Square API to production credentials
SQUARE_APPLICATION_ID=sq0idp-YOUR_PRODUCTION_APP_ID
SQUARE_ACCESS_TOKEN=YOUR_PRODUCTION_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_PRODUCTION_LOCATION_ID
SQUARE_WEBHOOK_SIGNATURE_KEY=YOUR_PRODUCTION_WEBHOOK_KEY

# Update external API keys
SHIPPING_API_KEY=your_production_shipping_key
USPS_API_KEY=your_production_usps_key
WHATSAPP_ACCESS_TOKEN=your_production_whatsapp_token
```

#### Restart Application
```bash
sudo -u nirvana pm2 restart all
```

## 🔧 Manual Deployment Steps

If you prefer manual control or need to troubleshoot:

### 1. System Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install prerequisites
sudo apt install -y curl wget git nginx mysql-server certbot \
    python3-certbot-nginx ufw fail2ban htop unzip build-essential

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install PM2
sudo npm install -g pm2
```

### 2. Database Setup
```bash
# Secure MySQL
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE u106832845_nirvana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'u106832845_root'@'localhost' IDENTIFIED BY 'YOUR_SECURE_PASSWORD';
GRANT ALL PRIVILEGES ON u106832845_nirvana.* TO 'u106832845_root'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. Application Setup
```bash
# Create project structure
sudo mkdir -p /var/www/nirvana-organics-backend/{current,shared/{logs,uploads,config},releases}

# Create application user
sudo useradd -r -s /bin/bash -d /var/www/nirvana-organics-backend nirvana

# Extract and setup application
RELEASE_DIR="/var/www/nirvana-organics-backend/releases/$(date +%Y%m%d_%H%M%S)"
sudo mkdir -p "$RELEASE_DIR"
sudo tar -xzf nirvana-organics-backend-production-*.tar.gz -C "$RELEASE_DIR" --strip-components=1
sudo chown -R nirvana:nirvana /var/www/nirvana-organics-backend
sudo ln -sfn "$RELEASE_DIR" /var/www/nirvana-organics-backend/current

# Install dependencies and configure
cd /var/www/nirvana-organics-backend/current
sudo -u nirvana npm ci --production
sudo -u nirvana cp .env.production.final .env
# Edit .env with your production values
sudo -u nirvana npm run generate:secrets
sudo -u nirvana npm run setup:database
sudo -u nirvana npm run migrate:prod
```

### 4. Web Server Setup
```bash
# Configure Nginx (see VPS_DEPLOYMENT_GUIDE.md for full config)
sudo nano /etc/nginx/sites-available/nirvana-organics-backend
sudo ln -s /etc/nginx/sites-available/nirvana-organics-backend /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx
```

### 5. SSL Setup
```bash
# Obtain SSL certificates
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com \
    --non-interactive --agree-tos --email <EMAIL> --redirect

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx" | sudo crontab -
```

### 6. Start Application
```bash
# Start with PM2
sudo -u nirvana pm2 start ecosystem.config.js --env production
sudo -u nirvana pm2 save
sudo pm2 startup systemd -u nirvana --hp /var/www/nirvana-organics-backend
```

## 🔄 Deployment Updates

### Update Existing Deployment
```bash
# Upload new package and run update
sudo ./scripts/update-deployment.sh
```

### Rollback Deployment
```bash
# Rollback to previous version
sudo ./scripts/rollback-deployment.sh
```

## 📊 Monitoring and Maintenance

### Setup Monitoring
```bash
# Install monitoring system
sudo ./scripts/setup-monitoring.sh

# View monitoring dashboard
/opt/monitoring/scripts/dashboard.sh
```

### Setup Backups
```bash
# Install backup system
sudo ./scripts/setup-backups.sh

# View backup status
/var/backups/nirvana-organics-backend/scripts/backup-status.sh
```

### Regular Maintenance Commands
```bash
# Check application status
sudo -u nirvana pm2 status
sudo -u nirvana pm2 logs

# Check system resources
htop
df -h
free -h

# Check SSL certificates
sudo certbot certificates

# Manual backup
/var/backups/nirvana-organics-backend/scripts/backup-all.sh

# Health checks
/opt/monitoring/scripts/health-check.sh
```

## ✅ Post-Deployment Verification

### 1. Application Health
```bash
# Test local health endpoint
curl http://localhost:5000/health

# Test HTTPS endpoint
curl https://shopnirvanaorganics.com/health

# Check PM2 status
sudo -u nirvana pm2 status
```

### 2. SSL Verification
```bash
# Run SSL verification
sudo -u nirvana npm run verify:ssl

# Check SSL rating at: https://www.ssllabs.com/ssltest/
```

### 3. Service Validation
```bash
# Validate third-party services
sudo -u nirvana npm run setup:services

# Test database connection
sudo -u nirvana npm run test:database
```

### 4. Functional Testing
- [ ] User registration and login
- [ ] Product browsing and search
- [ ] Shopping cart functionality
- [ ] Checkout process
- [ ] Payment processing (Square)
- [ ] Email notifications
- [ ] Admin panel access
- [ ] File uploads
- [ ] API endpoints

## 🆘 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check PM2 logs
sudo -u nirvana pm2 logs

# Check environment variables
sudo -u nirvana cat /var/www/nirvana-organics-backend/current/.env

# Restart application
sudo -u nirvana pm2 restart all
```

#### Database Connection Issues
```bash
# Test database connection
sudo -u nirvana npm run test:database

# Check MySQL status
sudo systemctl status mysql

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log
```

#### SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew

# Check Nginx configuration
sudo nginx -t
```

#### High Resource Usage
```bash
# Check memory usage
free -h
sudo -u nirvana pm2 monit

# Check disk space
df -h

# Restart if needed
sudo -u nirvana pm2 restart all
```

### Log Locations
- **Application logs**: `sudo -u nirvana pm2 logs`
- **Nginx logs**: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- **MySQL logs**: `/var/log/mysql/error.log`
- **Deployment logs**: `/var/log/nirvana-organics-backend-deployment.log`
- **Monitoring logs**: `/opt/monitoring/logs/`
- **Backup logs**: `/var/backups/nirvana-organics-backend/logs/`

## 📞 Support and Resources

### Documentation Files
- **VPS_DEPLOYMENT_GUIDE.md** - Detailed VPS setup instructions
- **PRODUCTION_CONFIGURATION_CHECKLIST.md** - Configuration checklist
- **SQUARE_PRODUCTION_SETUP.md** - Square payment setup
- **SSL_SETUP_GUIDE.md** - SSL certificate guide

### Useful Commands Reference
```bash
# Application Management
sudo -u nirvana pm2 status          # Check PM2 status
sudo -u nirvana pm2 logs            # View application logs
sudo -u nirvana pm2 restart all     # Restart application
sudo -u nirvana pm2 reload all      # Reload application (zero downtime)

# System Management
sudo systemctl status nginx         # Check Nginx status
sudo systemctl reload nginx         # Reload Nginx configuration
sudo systemctl status mysql         # Check MySQL status

# Monitoring
/opt/monitoring/scripts/dashboard.sh                    # Monitoring dashboard
/opt/monitoring/scripts/health-check.sh                # Manual health check
/var/backups/nirvana-organics-backend/scripts/backup-status.sh  # Backup status

# SSL Management
sudo certbot certificates           # Check certificate status
sudo certbot renew                  # Renew certificates
sudo nginx -t                       # Test Nginx configuration
```

## 🎉 Success Checklist

- [ ] VPS accessible and secured
- [ ] Domain pointing to VPS IP
- [ ] Application responding on HTTPS
- [ ] SSL certificate valid and auto-renewing
- [ ] Database connected and migrated
- [ ] PM2 managing application processes
- [ ] Nginx reverse proxy configured
- [ ] Firewall configured and active
- [ ] Monitoring system operational
- [ ] Backup system configured
- [ ] All third-party services validated
- [ ] Square payment processing tested
- [ ] Email notifications working
- [ ] Admin panel accessible
- [ ] Performance monitoring active

**Congratulations! Your Nirvana Organics e-commerce platform is now fully deployed and production-ready! 🚀**
