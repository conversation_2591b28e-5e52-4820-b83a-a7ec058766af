const { body, param, query } = require('express-validator');

// Validation for category ID parameter
const validateCategoryId = [
  param('categoryId')
    .isInt({ min: 1 })
    .withMessage('Category ID must be a positive integer')
];

// Validation for creating a new category
const validateCreateCategory = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category name must be between 1 and 100 characters')
    .notEmpty()
    .withMessage('Category name is required'),
    
  body('slug')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category slug must be between 1 and 100 characters')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Category slug can only contain lowercase letters, numbers, and hyphens')
    .notEmpty()
    .withMessage('Category slug is required'),
    
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
    
  body('image')
    .optional()
    .trim()
    .isURL()
    .withMessage('Image must be a valid URL'),
    
  body('parentId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a positive integer'),
    
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value'),
    
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
    
  body('seoTitle')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('SEO title cannot exceed 200 characters'),
    
  body('seoDescription')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('SEO description cannot exceed 500 characters'),
    
  body('seoKeywords')
    .optional()
    .trim()
    .isLength({ max: 300 })
    .withMessage('SEO keywords cannot exceed 300 characters')
];

// Validation for updating a category
const validateUpdateCategory = [
  param('categoryId')
    .isInt({ min: 1 })
    .withMessage('Category ID must be a positive integer'),
    
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category name must be between 1 and 100 characters'),
    
  body('slug')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category slug must be between 1 and 100 characters')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Category slug can only contain lowercase letters, numbers, and hyphens'),
    
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
    
  body('image')
    .optional()
    .trim()
    .isURL()
    .withMessage('Image must be a valid URL'),
    
  body('parentId')
    .optional()
    .custom((value) => {
      if (value === null || value === '') return true;
      if (!Number.isInteger(value) || value < 1) {
        throw new Error('Parent ID must be a positive integer or null');
      }
      return true;
    }),
    
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value'),
    
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
    
  body('seoTitle')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('SEO title cannot exceed 200 characters'),
    
  body('seoDescription')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('SEO description cannot exceed 500 characters'),
    
  body('seoKeywords')
    .optional()
    .trim()
    .isLength({ max: 300 })
    .withMessage('SEO keywords cannot exceed 300 characters')
];

// Validation for bulk operations
const validateBulkCategoryOperation = [
  body('categoryIds')
    .isArray({ min: 1 })
    .withMessage('Category IDs must be a non-empty array')
    .custom((categoryIds) => {
      if (!categoryIds.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('All category IDs must be positive integers');
      }
      return true;
    }),
    
  body('action')
    .isIn(['activate', 'deactivate', 'move', 'delete'])
    .withMessage('Action must be activate, deactivate, move, or delete'),
    
  body('data.parentId')
    .if(body('action').equals('move'))
    .custom((value) => {
      if (value === null || value === '') return true;
      if (!Number.isInteger(value) || value < 1) {
        throw new Error('Parent ID must be a positive integer or null for move action');
      }
      return true;
    })
];

// Validation for category reordering
const validateCategoryReorder = [
  body('categoryOrders')
    .isArray({ min: 1 })
    .withMessage('Category orders must be a non-empty array')
    .custom((orders) => {
      if (!orders.every(order => 
        order.hasOwnProperty('id') && 
        order.hasOwnProperty('sortOrder') &&
        Number.isInteger(order.id) && order.id > 0 &&
        Number.isInteger(order.sortOrder) && order.sortOrder >= 0
      )) {
        throw new Error('Each order item must have valid id and sortOrder properties');
      }
      return true;
    })
];

// Validation for category listing query parameters
const validateCategoryQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
    
  query('parentId')
    .optional()
    .custom((value) => {
      if (value === 'null') return true;
      const id = parseInt(value);
      if (!Number.isInteger(id) || id < 1) {
        throw new Error('Parent ID must be a positive integer or "null"');
      }
      return true;
    }),
    
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive filter must be a boolean value'),
    
  query('sortBy')
    .optional()
    .isIn(['name', 'slug', 'sortOrder', 'createdAt', 'updatedAt'])
    .withMessage('Sort field must be name, slug, sortOrder, createdAt, or updatedAt'),
    
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
    
  query('includeProducts')
    .optional()
    .isBoolean()
    .withMessage('Include products must be a boolean value'),
    
  query('tree')
    .optional()
    .isBoolean()
    .withMessage('Tree must be a boolean value')
];

// Role-based access control for category management
const requireCategoryManagementAccess = (req, res, next) => {
  if (!['admin', 'manager', 'super_admin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Category management access required'
    });
  }
  next();
};

// Middleware to check if category exists
const checkCategoryExists = async (req, res, next) => {
  try {
    const { categoryId } = req.params;
    const { Category } = require('../models');
    
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }
    
    req.category = category;
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error checking category existence',
      error: error.message
    });
  }
};

// Middleware to validate slug uniqueness
const validateSlugUniqueness = async (req, res, next) => {
  try {
    const { slug } = req.body;
    const { categoryId } = req.params;
    
    if (!slug) return next();
    
    const { Category } = require('../models');
    const { Op } = require('sequelize');
    
    const where = { slug };
    if (categoryId) {
      where.id = { [Op.ne]: categoryId };
    }
    
    const existingCategory = await Category.findOne({ where });
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Category slug already exists'
      });
    }
    
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error validating slug uniqueness',
      error: error.message
    });
  }
};

// Middleware to prevent circular parent-child relationships
const preventCircularReference = async (req, res, next) => {
  try {
    const { parentId } = req.body;
    const { categoryId } = req.params;
    
    if (!parentId || !categoryId) return next();
    
    // Prevent category from being its own parent
    if (parseInt(parentId) === parseInt(categoryId)) {
      return res.status(400).json({
        success: false,
        message: 'Category cannot be its own parent'
      });
    }
    
    // Check for circular reference by traversing up the parent chain
    const { Category } = require('../models');
    let currentParentId = parentId;
    const visited = new Set();
    
    while (currentParentId && !visited.has(currentParentId)) {
      visited.add(currentParentId);
      
      if (parseInt(currentParentId) === parseInt(categoryId)) {
        return res.status(400).json({
          success: false,
          message: 'Circular parent-child relationship detected'
        });
      }
      
      const parent = await Category.findByPk(currentParentId);
      currentParentId = parent ? parent.parentId : null;
    }
    
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error checking circular reference',
      error: error.message
    });
  }
};

module.exports = {
  validateCategoryId,
  validateCreateCategory,
  validateUpdateCategory,
  validateBulkCategoryOperation,
  validateCategoryReorder,
  validateCategoryQuery,
  requireCategoryManagementAccess,
  checkCategoryExists,
  validateSlugUniqueness,
  preventCircularReference
};
