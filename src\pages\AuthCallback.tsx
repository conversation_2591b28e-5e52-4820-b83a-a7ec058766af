import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAppDispatch } from '../hooks/redux';
import { addToast } from '../store/slices/uiSlice';
import { setCredentials } from '../store/slices/authSlice';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { authService } from '../services/authService';

/**
 * OAuth Callback Page for Social Authentication
 * Handles the callback from Google and Facebook OAuth flows
 */
const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const token = searchParams.get('token');
        const refreshToken = searchParams.get('refreshToken');
        const provider = searchParams.get('provider');
        const error = searchParams.get('error');

        if (error) {
          let errorMessage = 'Social authentication failed';
          
          switch (error) {
            case 'google_auth_failed':
              errorMessage = 'Google authentication failed. Please try again.';
              break;
            case 'facebook_auth_failed':
              errorMessage = 'Facebook authentication failed. Please try again.';
              break;
            case 'email_required':
              errorMessage = 'Email address is required for registration. Please ensure your social account has a public email.';
              break;
            case 'account_exists':
              errorMessage = 'An account with this email already exists. Please sign in with your email and password.';
              break;
            default:
              errorMessage = `Authentication failed: ${error}`;
          }
          
          dispatch(addToast({
            type: 'error',
            title: 'Authentication Failed',
            message: errorMessage
          }));
          
          navigate('/login', { replace: true });
          return;
        }

        if (token && refreshToken) {
          // Store tokens in localStorage
          localStorage.setItem('token', token);
          localStorage.setItem('refreshToken', refreshToken);
          
          // Get user profile with the new token
          try {
            const userResponse = await authService.getProfile();
            if (userResponse.success && userResponse.data) {
              // Store user data
              localStorage.setItem('user', JSON.stringify(userResponse.data));
              
              // Update Redux store
              dispatch(setCredentials({
                user: userResponse.data,
                token: token
              }));

              dispatch(addToast({
                type: 'success',
                title: 'Welcome!',
                message: `Successfully signed in with ${provider || 'social account'}!`
              }));

              // Determine redirect path based on user role
              let redirectPath = '/';

              // Check if user is admin and redirect to dashboard
              if (userResponse.data.role === 'admin' || userResponse.data.role === 'super_admin') {
                redirectPath = '/dashboard';
              } else {
                // For non-admin users, use intended path or default to home
                redirectPath = localStorage.getItem('intendedPath') || '/';
              }

              localStorage.removeItem('intendedPath');
              navigate(redirectPath, { replace: true });
            } else {
              throw new Error('Failed to get user profile');
            }
          } catch (profileError) {
            console.error('Error getting user profile:', profileError);
            
            // Clear stored tokens if profile fetch fails
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
            
            dispatch(addToast({
              type: 'error',
              title: 'Authentication Error',
              message: 'Failed to complete authentication. Please try again.'
            }));
            
            navigate('/login', { replace: true });
          }
        } else {
          // No tokens received
          dispatch(addToast({
            type: 'error',
            title: 'Authentication Failed',
            message: 'No authentication tokens received. Please try again.'
          }));
          
          navigate('/login', { replace: true });
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        
        dispatch(addToast({
          type: 'error',
          title: 'Authentication Error',
          message: 'An unexpected error occurred during authentication.'
        }));
        
        navigate('/login', { replace: true });
      }
    };

    handleAuthCallback();
  }, [searchParams, navigate, dispatch]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Completing Authentication
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Please wait while we complete your social authentication...
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthCallback;
