@echo off
echo 🔍 Testing Server Connection
echo.

echo Please provide your server details:
echo.

set /p SERVER_IP="Enter server IP address (e.g., *************): "
set /p SERVER_USER="Enter username (default: root): "

if "%SERVER_USER%"=="" set SERVER_USER=root

echo.
echo Testing connection to %SERVER_USER%@%SERVER_IP%...
echo.

echo 1️⃣ Testing SSH connection...
ssh %SERVER_USER%@%SERVER_IP% "echo 'SSH connection successful!' && pwd && whoami"

if %errorlevel% neq 0 (
    echo ❌ SSH connection failed
    echo.
    echo 💡 Troubleshooting tips:
    echo 1. Check if the IP address is correct
    echo 2. Ensure SSH is enabled on the server
    echo 3. Verify your SSH key or password
    echo 4. Check if the server is running
    echo.
    pause
    exit /b 1
)

echo.
echo 2️⃣ Testing SCP file transfer...
echo test > test-file.txt
scp test-file.txt %SERVER_USER%@%SERVER_IP%:/tmp/
if %errorlevel% neq 0 (
    echo ❌ SCP file transfer failed
    del test-file.txt
    pause
    exit /b 1
) else (
    echo ✅ SCP file transfer successful
    ssh %SERVER_USER%@%SERVER_IP% "rm /tmp/test-file.txt"
    del test-file.txt
)

echo.
echo 3️⃣ Checking server environment...
ssh %SERVER_USER%@%SERVER_IP% "echo 'Node.js version:' && node --version 2>/dev/null || echo 'Node.js not installed'"
ssh %SERVER_USER%@%SERVER_IP% "echo 'NPM version:' && npm --version 2>/dev/null || echo 'NPM not installed'"
ssh %SERVER_USER%@%SERVER_IP% "echo 'Available disk space:' && df -h /"

echo.
echo ✅ Connection test completed!
echo.
echo Your server details:
echo Server: %SERVER_USER%@%SERVER_IP%
echo.
echo 📋 To deploy, update your deployment commands to use:
echo scp -r server %SERVER_USER%@%SERVER_IP%:/var/www/nirvana-backend/
echo.
pause
