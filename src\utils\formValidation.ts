import * as yup from 'yup';

/**
 * Common validation rules
 */
export const ValidationRules = {
  // Email validation
  email: yup
    .string()
    .email('Please enter a valid email address')
    .required('Email is required'),

  // Password validation (for cannabis e-commerce)
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('Password is required'),

  // Confirm password
  confirmPassword: (passwordField: string = 'password') => yup
    .string()
    .oneOf([yup.ref(passwordField)], 'Passwords must match')
    .required('Please confirm your password'),

  // Name validation
  firstName: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
    .required('First name is required'),

  lastName: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
    .required('Last name is required'),

  // Phone validation
  phone: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
    .required('Phone number is required'),

  // Age verification (21+ for cannabis)
  dateOfBirth: yup
    .date()
    .max(new Date(), 'Date of birth cannot be in the future')
    .test('age', 'You must be at least 21 years old', function(value) {
      if (!value) return false;
      const today = new Date();
      const birthDate = new Date(value);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= 21;
      }
      return age >= 21;
    })
    .required('Date of birth is required'),

  // Address validation
  streetAddress: yup
    .string()
    .min(5, 'Street address must be at least 5 characters')
    .max(100, 'Street address must be less than 100 characters')
    .required('Street address is required'),

  city: yup
    .string()
    .min(2, 'City must be at least 2 characters')
    .max(50, 'City must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'City can only contain letters, spaces, hyphens, and apostrophes')
    .required('City is required'),

  state: yup
    .string()
    .length(2, 'State must be a 2-letter code')
    .matches(/^[A-Z]{2}$/, 'State must be a valid 2-letter state code')
    .required('State is required'),

  postalCode: yup
    .string()
    .matches(/^\d{5}(-\d{4})?$/, 'Please enter a valid ZIP code')
    .required('ZIP code is required'),

  country: yup
    .string()
    .oneOf(['US'], 'Currently only shipping within the United States')
    .required('Country is required'),

  // Product validation
  productName: yup
    .string()
    .min(3, 'Product name must be at least 3 characters')
    .max(100, 'Product name must be less than 100 characters')
    .required('Product name is required'),

  productDescription: yup
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description must be less than 2000 characters')
    .required('Product description is required'),

  price: yup
    .number()
    .positive('Price must be greater than 0')
    .max(10000, 'Price cannot exceed $10,000')
    .required('Price is required'),

  quantity: yup
    .number()
    .integer('Quantity must be a whole number')
    .min(1, 'Quantity must be at least 1')
    .max(999, 'Quantity cannot exceed 999')
    .required('Quantity is required'),

  // Order validation
  orderNotes: yup
    .string()
    .max(500, 'Order notes must be less than 500 characters'),

  // Search validation
  searchQuery: yup
    .string()
    .min(2, 'Search query must be at least 2 characters')
    .max(100, 'Search query must be less than 100 characters'),

  // Coupon code validation
  couponCode: yup
    .string()
    .min(3, 'Coupon code must be at least 3 characters')
    .max(20, 'Coupon code must be less than 20 characters')
    .matches(/^[A-Z0-9]+$/, 'Coupon code can only contain uppercase letters and numbers'),

  // Review validation
  reviewRating: yup
    .number()
    .integer('Rating must be a whole number')
    .min(1, 'Rating must be at least 1 star')
    .max(5, 'Rating cannot exceed 5 stars')
    .required('Rating is required'),

  reviewComment: yup
    .string()
    .min(10, 'Review must be at least 10 characters')
    .max(1000, 'Review must be less than 1000 characters')
    .required('Review comment is required'),
};

/**
 * Common validation schemas
 */
export const ValidationSchemas = {
  // User registration
  register: yup.object({
    firstName: ValidationRules.firstName,
    lastName: ValidationRules.lastName,
    email: ValidationRules.email,
    password: ValidationRules.password,
    confirmPassword: ValidationRules.confirmPassword(),
    phone: ValidationRules.phone,
    dateOfBirth: ValidationRules.dateOfBirth,
    agreeToTerms: yup
      .boolean()
      .oneOf([true], 'You must agree to the terms and conditions')
      .required('You must agree to the terms and conditions'),
    agreeToPrivacy: yup
      .boolean()
      .oneOf([true], 'You must agree to the privacy policy')
      .required('You must agree to the privacy policy'),
  }),

  // User login
  login: yup.object({
    email: ValidationRules.email,
    password: yup.string().required('Password is required'),
  }),

  // Password reset request
  forgotPassword: yup.object({
    email: ValidationRules.email,
  }),

  // Password reset
  resetPassword: yup.object({
    password: ValidationRules.password,
    confirmPassword: ValidationRules.confirmPassword(),
  }),

  // Address form
  address: yup.object({
    firstName: ValidationRules.firstName,
    lastName: ValidationRules.lastName,
    streetAddress: ValidationRules.streetAddress,
    streetAddress2: yup.string().max(100, 'Address line 2 must be less than 100 characters'),
    city: ValidationRules.city,
    state: ValidationRules.state,
    postalCode: ValidationRules.postalCode,
    country: ValidationRules.country,
    phone: ValidationRules.phone.optional(),
    isDefault: yup.boolean(),
  }),

  // Checkout form
  checkout: yup.object({
    // Billing address
    billingAddress: yup.object({
      firstName: ValidationRules.firstName,
      lastName: ValidationRules.lastName,
      streetAddress: ValidationRules.streetAddress,
      streetAddress2: yup.string().max(100),
      city: ValidationRules.city,
      state: ValidationRules.state,
      postalCode: ValidationRules.postalCode,
      country: ValidationRules.country,
      phone: ValidationRules.phone,
    }),
    
    // Shipping address
    shippingAddress: yup.object({
      firstName: ValidationRules.firstName,
      lastName: ValidationRules.lastName,
      streetAddress: ValidationRules.streetAddress,
      streetAddress2: yup.string().max(100),
      city: ValidationRules.city,
      state: ValidationRules.state,
      postalCode: ValidationRules.postalCode,
      country: ValidationRules.country,
      phone: ValidationRules.phone,
    }),

    orderNotes: ValidationRules.orderNotes,
  }),

  // Guest checkout
  guestCheckout: yup.object({
    email: ValidationRules.email,
    firstName: ValidationRules.firstName,
    lastName: ValidationRules.lastName,
    phone: ValidationRules.phone,
    
    // Billing address
    billingAddress: yup.object({
      streetAddress: ValidationRules.streetAddress,
      streetAddress2: yup.string().max(100),
      city: ValidationRules.city,
      state: ValidationRules.state,
      postalCode: ValidationRules.postalCode,
      country: ValidationRules.country,
    }),
    
    // Shipping address
    shippingAddress: yup.object({
      streetAddress: ValidationRules.streetAddress,
      streetAddress2: yup.string().max(100),
      city: ValidationRules.city,
      state: ValidationRules.state,
      postalCode: ValidationRules.postalCode,
      country: ValidationRules.country,
    }),

    orderNotes: ValidationRules.orderNotes,
    agreeToTerms: yup.boolean().oneOf([true], 'You must agree to the terms and conditions'),
  }),

  // Product form (admin)
  product: yup.object({
    name: ValidationRules.productName,
    description: ValidationRules.productDescription,
    price: ValidationRules.price,
    compareAtPrice: yup.number().positive('Compare at price must be greater than 0').optional(),
    sku: yup.string().max(50, 'SKU must be less than 50 characters'),
    inventory: yup.number().integer().min(0, 'Inventory cannot be negative'),
    weight: yup.number().positive('Weight must be greater than 0').optional(),
    categoryId: yup.string().required('Category is required'),
    tags: yup.array().of(yup.string()),
    isActive: yup.boolean(),
    isFeatured: yup.boolean(),
  }),

  // Contact form
  contact: yup.object({
    name: yup.string().min(2, 'Name must be at least 2 characters').required('Name is required'),
    email: ValidationRules.email,
    subject: yup.string().min(5, 'Subject must be at least 5 characters').required('Subject is required'),
    message: yup.string().min(10, 'Message must be at least 10 characters').required('Message is required'),
  }),

  // Order tracking
  orderTracking: yup.object({
    email: ValidationRules.email,
    orderNumber: yup.string().required('Order number is required'),
  }),

  // Product review
  productReview: yup.object({
    rating: ValidationRules.reviewRating,
    title: yup.string().min(5, 'Review title must be at least 5 characters').required('Review title is required'),
    comment: ValidationRules.reviewComment,
    wouldRecommend: yup.boolean(),
  }),
};

/**
 * Validation utilities
 */
export const ValidationUtils = {
  /**
   * Validate a single field
   */
  validateField: async (schema: yup.AnySchema, value: any): Promise<string | null> => {
    try {
      await schema.validate(value);
      return null;
    } catch (error: any) {
      return error.message;
    }
  },

  /**
   * Validate multiple fields
   */
  validateFields: async (schema: yup.ObjectSchema<any>, values: any): Promise<Record<string, string>> => {
    try {
      await schema.validate(values, { abortEarly: false });
      return {};
    } catch (error: any) {
      const errors: Record<string, string> = {};
      if (error.inner) {
        error.inner.forEach((err: any) => {
          if (err.path) {
            errors[err.path] = err.message;
          }
        });
      }
      return errors;
    }
  },

  /**
   * Check if form is valid
   */
  isFormValid: async (schema: yup.ObjectSchema<any>, values: any): Promise<boolean> => {
    try {
      await schema.validate(values);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Get validation errors as array
   */
  getValidationErrors: (error: yup.ValidationError): Array<{ field: string; message: string }> => {
    return error.inner.map(err => ({
      field: err.path || 'unknown',
      message: err.message
    }));
  },

  /**
   * Format validation error for display
   */
  formatValidationError: (error: yup.ValidationError): string => {
    if (error.inner && error.inner.length > 0) {
      return error.inner[0].message;
    }
    return error.message;
  },
};

export default ValidationSchemas;
