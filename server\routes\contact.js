const express = require('express');
const router = express.Router();
const contactController = require('../controllers/contactController');
const { validateContact } = require('../middleware/validation');
const rateLimit = require('express-rate-limit');

// Contact form rate limiting - more restrictive to prevent spam
const contactFormLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit each IP to 3 contact form submissions per windowMs
  message: {
    success: false,
    message: 'Too many contact form submissions. Please try again in 15 minutes.',
    error: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting for development
  skip: (req) => process.env.NODE_ENV === 'development'
});

// General contact info rate limiting
const contactInfoLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // Allow more requests for contact info
  message: {
    success: false,
    message: 'Too many requests. Please try again in a minute.',
    error: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// @route   POST /api/contact
// @desc    Submit contact form
// @access  Public
router.post('/', 
  contactFormLimiter,
  validateContact,
  contactController.submitContactForm
);

// @route   GET /api/contact/info
// @desc    Get contact information
// @access  Public
router.get('/info', 
  contactInfoLimiter,
  contactController.getContactInfo
);

module.exports = router;
