#!/usr/bin/env node

/**
 * Third-Party Services Configuration Script
 * Validates and configures all third-party integrations for production
 */

require('dotenv').config();
const nodemailer = require('nodemailer');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔧${colors.reset} ${msg}`)
};

/**
 * Validate Square Payment Configuration
 */
async function validateSquareConfiguration() {
  log.step('Validating Square Payment Configuration...');

  const requiredVars = [
    'SQUARE_APPLICATION_ID',
    'SQUARE_ACCESS_TOKEN',
    'SQUARE_LOCATION_ID',
    'SQUARE_ENVIRONMENT'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    log.error(`Missing Square environment variables: ${missingVars.join(', ')}`);
    return false;
  }

  // Check for sandbox values in production
  if (process.env.NODE_ENV === 'production') {
    const sandboxIndicators = [
      { var: 'SQUARE_APPLICATION_ID', indicator: 'sandbox' },
      { var: 'SQUARE_ACCESS_TOKEN', indicator: 'sandbox' },
      { var: 'SQUARE_ENVIRONMENT', indicator: 'sandbox' }
    ];

    const sandboxVars = sandboxIndicators.filter(({ var: varName, indicator }) => 
      process.env[varName] && process.env[varName].toLowerCase().includes(indicator)
    );

    if (sandboxVars.length > 0) {
      log.error('❌ CRITICAL: Sandbox Square credentials detected in production!');
      sandboxVars.forEach(({ var: varName }) => {
        log.error(`  ${varName} contains sandbox values`);
      });
      log.error('Please update to production Square credentials');
      return false;
    }

    if (process.env.SQUARE_ENVIRONMENT !== 'production') {
      log.error(`SQUARE_ENVIRONMENT is '${process.env.SQUARE_ENVIRONMENT}', should be 'production'`);
      return false;
    }
  }

  // Test Square API connection (basic validation)
  try {
    const { Client, Environment } = require('squareup');
    
    const client = new Client({
      accessToken: process.env.SQUARE_ACCESS_TOKEN,
      environment: process.env.SQUARE_ENVIRONMENT === 'production' ? Environment.Production : Environment.Sandbox
    });

    // Test locations API
    const locationsApi = client.locationsApi;
    const response = await locationsApi.listLocations();

    if (response.result && response.result.locations) {
      const location = response.result.locations.find(loc => loc.id === process.env.SQUARE_LOCATION_ID);
      
      if (location) {
        log.success(`Square API connection successful`);
        log.info(`Location: ${location.name} (${location.id})`);
        log.info(`Environment: ${process.env.SQUARE_ENVIRONMENT}`);
        return true;
      } else {
        log.error(`Location ID ${process.env.SQUARE_LOCATION_ID} not found`);
        return false;
      }
    } else {
      log.error('Failed to retrieve Square locations');
      return false;
    }

  } catch (error) {
    log.error(`Square API test failed: ${error.message}`);
    
    if (error.message.includes('Unauthorized')) {
      log.error('Check SQUARE_ACCESS_TOKEN - it may be invalid or expired');
    } else if (error.message.includes('ENOTFOUND')) {
      log.error('Network connectivity issue - check internet connection');
    }
    
    return false;
  }
}

/**
 * Validate Email Configuration
 */
async function validateEmailConfiguration() {
  log.step('Validating Email Configuration...');

  const requiredVars = [
    'EMAIL_HOST',
    'EMAIL_PORT',
    'EMAIL_USER',
    'EMAIL_PASS',
    'EMAIL_FROM'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    log.error(`Missing email environment variables: ${missingVars.join(', ')}`);
    return false;
  }

  // Check for placeholder values
  const placeholderVars = requiredVars.filter(varName => 
    process.env[varName] && (
      process.env[varName].includes('your-') ||
      process.env[varName].includes('example.com') ||
      process.env[varName].includes('placeholder')
    )
  );

  if (placeholderVars.length > 0) {
    log.warning(`Found placeholder values in: ${placeholderVars.join(', ')}`);
    log.warning('Please update these with actual email credentials');
  }

  // Test email connection
  try {
    const transporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT),
      secure: parseInt(process.env.EMAIL_PORT) === 465,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    await transporter.verify();
    log.success('Email SMTP connection successful');

    // Test sending a test email (optional)
    if (process.env.TEST_EMAIL_RECIPIENT) {
      log.info('Sending test email...');
      
      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: process.env.TEST_EMAIL_RECIPIENT,
        subject: 'Nirvana Organics - Production Email Test',
        text: 'This is a test email from your production environment setup.',
        html: '<p>This is a test email from your <strong>production environment</strong> setup.</p>'
      });
      
      log.success('Test email sent successfully');
    }

    return true;

  } catch (error) {
    log.error(`Email configuration test failed: ${error.message}`);
    
    if (error.message.includes('Invalid login')) {
      log.error('Check EMAIL_USER and EMAIL_PASS - authentication failed');
    } else if (error.message.includes('ENOTFOUND')) {
      log.error('Check EMAIL_HOST - hostname not found');
    } else if (error.message.includes('ECONNREFUSED')) {
      log.error('Check EMAIL_PORT - connection refused');
    }
    
    return false;
  }
}

/**
 * Validate Google OAuth Configuration
 */
async function validateGoogleOAuthConfiguration() {
  log.step('Validating Google OAuth Configuration...');

  const requiredVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'GOOGLE_OAUTH_CALLBACK_URL'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    log.error(`Missing Google OAuth environment variables: ${missingVars.join(', ')}`);
    return false;
  }

  // Validate callback URL for production
  if (process.env.NODE_ENV === 'production') {
    const callbackUrl = process.env.GOOGLE_OAUTH_CALLBACK_URL;
    
    if (callbackUrl.includes('localhost') || callbackUrl.includes('127.0.0.1')) {
      log.error('Google OAuth callback URL contains localhost - update for production');
      return false;
    }

    if (!callbackUrl.startsWith('https://')) {
      log.error('Google OAuth callback URL must use HTTPS in production');
      return false;
    }
  }

  log.success('Google OAuth configuration appears valid');
  log.info(`Callback URL: ${process.env.GOOGLE_OAUTH_CALLBACK_URL}`);
  
  return true;
}

/**
 * Validate Web Push Notifications (VAPID)
 */
function validateWebPushConfiguration() {
  log.step('Validating Web Push Notifications Configuration...');

  const requiredVars = [
    'VAPID_PUBLIC_KEY',
    'VAPID_PRIVATE_KEY',
    'VAPID_EMAIL'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    log.warning(`Missing VAPID environment variables: ${missingVars.join(', ')}`);
    log.info('Web push notifications will not work without VAPID keys');
    return false;
  }

  // Basic validation of VAPID keys format
  const publicKey = process.env.VAPID_PUBLIC_KEY;
  const privateKey = process.env.VAPID_PRIVATE_KEY;

  if (publicKey.length < 80 || privateKey.length < 40) {
    log.warning('VAPID keys appear to be too short - they may be invalid');
    log.info('Generate new VAPID keys with: npx web-push generate-vapid-keys');
    return false;
  }

  log.success('VAPID configuration appears valid');
  return true;
}

/**
 * Validate External APIs
 */
function validateExternalAPIs() {
  log.step('Validating External API Configuration...');

  const apiConfigs = [
    { name: 'Shipping API', key: 'SHIPPING_API_KEY' },
    { name: 'Analytics API', key: 'ANALYTICS_API_KEY' },
    { name: 'USPS API', key: 'USPS_API_KEY' },
    { name: 'WhatsApp Access Token', key: 'WHATSAPP_ACCESS_TOKEN' }
  ];

  let allValid = true;

  apiConfigs.forEach(({ name, key }) => {
    if (!process.env[key]) {
      log.warning(`${name} not configured (${key} missing)`);
      allValid = false;
    } else if (process.env[key].includes('CHANGE_TO_') || process.env[key].includes('your_')) {
      log.warning(`${name} has placeholder value - update with actual credentials`);
      allValid = false;
    } else {
      log.success(`${name} configured`);
    }
  });

  return allValid;
}

/**
 * Main setup function
 */
async function setupThirdPartyServices() {
  console.log(`${colors.bright}🔌 Third-Party Services Configuration${colors.reset}`);
  console.log('='.repeat(50));

  let allServicesValid = true;

  try {
    // Validate Square Payment Service
    if (!await validateSquareConfiguration()) {
      allServicesValid = false;
    }

    console.log('');

    // Validate Email Service
    if (!await validateEmailConfiguration()) {
      allServicesValid = false;
    }

    console.log('');

    // Validate Google OAuth
    if (!validateGoogleOAuthConfiguration()) {
      allServicesValid = false;
    }

    console.log('');

    // Validate Web Push Notifications
    validateWebPushConfiguration();

    console.log('');

    // Validate External APIs
    validateExternalAPIs();

    console.log('');

    if (allServicesValid) {
      log.success('🎉 All critical third-party services are properly configured!');
    } else {
      log.error('❌ Some critical services need attention before production deployment');
      process.exit(1);
    }

    console.log('');
    log.step('Production Readiness Checklist:');
    console.log('□ Square API credentials are for PRODUCTION environment');
    console.log('□ Email service is configured and tested');
    console.log('□ Google OAuth callback URL uses production domain');
    console.log('□ All API keys are production-ready (not sandbox/test)');
    console.log('□ VAPID keys are generated for web push notifications');
    console.log('□ External API credentials are updated');

  } catch (error) {
    log.error(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  setupThirdPartyServices();
}

module.exports = {
  setupThirdPartyServices,
  validateSquareConfiguration,
  validateEmailConfiguration,
  validateGoogleOAuthConfiguration,
  validateWebPushConfiguration,
  validateExternalAPIs
};
