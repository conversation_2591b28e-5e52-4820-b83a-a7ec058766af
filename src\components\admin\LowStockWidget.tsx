import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchProducts } from '../../store/slices/productSlice';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowRightIcon,
  CubeIcon
} from '@heroicons/react/24/outline';

interface LowStockProduct {
  id: number;
  name: string;
  sku: string;
  quantity: number;
  lowStockThreshold: number;
  images: string[];
  category: string;
}

interface LowStockWidgetProps {
  lowStockThreshold?: number;
  maxItems?: number;
  className?: string;
}

const LowStockWidget: React.FC<LowStockWidgetProps> = ({
  lowStockThreshold = 10,
  maxItems = 5,
  className = ''
}) => {
  const dispatch = useAppDispatch();
  const { products, loading } = useAppSelector((state) => state.products);
  const [lowStockProducts, setLowStockProducts] = useState<LowStockProduct[]>([]);
  const [outOfStockProducts, setOutOfStockProducts] = useState<LowStockProduct[]>([]);

  useEffect(() => {
    // Fetch products with low stock filter
    dispatch(fetchProducts({ 
      status: 'active',
      limit: 50 // Get more products to analyze stock levels
    }));
  }, [dispatch]);

  useEffect(() => {
    if (products.length > 0) {
      const lowStock = products.filter(product => 
        product.quantity > 0 && product.quantity <= lowStockThreshold
      ).slice(0, maxItems);

      const outOfStock = products.filter(product => 
        product.quantity === 0
      ).slice(0, maxItems);

      setLowStockProducts(lowStock);
      setOutOfStockProducts(outOfStock);
    }
  }, [products, lowStockThreshold, maxItems]);

  const getStockStatusColor = (quantity: number) => {
    if (quantity === 0) return 'text-red-600 bg-red-50';
    if (quantity <= lowStockThreshold) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  };

  const getStockStatusIcon = (quantity: number) => {
    if (quantity === 0) return XCircleIcon;
    if (quantity <= lowStockThreshold) return ExclamationTriangleIcon;
    return CubeIcon;
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-gray-200 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const totalLowStockItems = lowStockProducts.length + outOfStockProducts.length;

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Inventory Alerts</h3>
          {totalLowStockItems > 0 && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              {totalLowStockItems} alerts
            </span>
          )}
        </div>

        {totalLowStockItems === 0 ? (
          <div className="text-center py-8">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">All Good!</h3>
            <p className="mt-1 text-sm text-gray-500">
              No low stock or out of stock items found.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Out of Stock Items */}
            {outOfStockProducts.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center">
                  <XCircleIcon className="h-4 w-4 mr-1" />
                  Out of Stock ({outOfStockProducts.length})
                </h4>
                <div className="space-y-2">
                  {outOfStockProducts.map((product) => {
                    const StatusIcon = getStockStatusIcon(product.quantity);
                    return (
                      <div key={product.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            {product.images.length > 0 ? (
                              <img
                                className="h-10 w-10 rounded object-cover"
                                src={product.images[0]}
                                alt={product.name}
                                onError={(e) => {
                                  e.currentTarget.src = '/placeholder-product.png';
                                }}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded bg-gray-200 flex items-center justify-center">
                                <CubeIcon className="h-5 w-5 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {product.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              SKU: {product.sku} • {product.category}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStockStatusColor(product.quantity)}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {product.quantity} units
                          </span>
                          <Link
                            to={`/admin/products/${product.id}/edit`}
                            className="text-red-600 hover:text-red-800"
                          >
                            <ArrowRightIcon className="h-4 w-4" />
                          </Link>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Low Stock Items */}
            {lowStockProducts.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-yellow-800 mb-2 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  Low Stock ({lowStockProducts.length})
                </h4>
                <div className="space-y-2">
                  {lowStockProducts.map((product) => {
                    const StatusIcon = getStockStatusIcon(product.quantity);
                    return (
                      <div key={product.id} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            {product.images.length > 0 ? (
                              <img
                                className="h-10 w-10 rounded object-cover"
                                src={product.images[0]}
                                alt={product.name}
                                onError={(e) => {
                                  e.currentTarget.src = '/placeholder-product.png';
                                }}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded bg-gray-200 flex items-center justify-center">
                                <CubeIcon className="h-5 w-5 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {product.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              SKU: {product.sku} • {product.category}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStockStatusColor(product.quantity)}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {product.quantity} units
                          </span>
                          <Link
                            to={`/admin/products/${product.id}/edit`}
                            className="text-yellow-600 hover:text-yellow-800"
                          >
                            <ArrowRightIcon className="h-4 w-4" />
                          </Link>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* View All Link */}
            <div className="pt-4 border-t border-gray-200">
              <Link
                to="/admin/products?filter=low-stock"
                className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
              >
                View All Inventory Issues
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LowStockWidget;
