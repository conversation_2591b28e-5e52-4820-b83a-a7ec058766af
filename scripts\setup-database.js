#!/usr/bin/env node

/**
 * Database Setup Script for Nirvana Organics E-commerce
 * This script creates and initializes the MySQL database with all required tables
 */

import { Sequelize } from 'sequelize';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.join(__dirname, '../.env') });

// Import all models - we'll need to update this to dynamic import
// const models = require('../server/models');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'nirvana_organics',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql',
  logging: console.log,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`)
};

/**
 * Test database connection
 */
async function testConnection(sequelize) {
  try {
    await sequelize.authenticate();
    log.success('Database connection established successfully');
    return true;
  } catch (error) {
    log.error(`Unable to connect to database: ${error.message}`);
    return false;
  }
}

/**
 * Create database tables
 */
async function createTables(sequelize) {
  try {
    log.step('Creating database tables...');
    
    // Sync all models (create tables)
    await sequelize.sync({ force: false, alter: true });
    
    log.success('All database tables created successfully');
    return true;
  } catch (error) {
    log.error(`Failed to create tables: ${error.message}`);
    return false;
  }
}

/**
 * Seed initial data
 */
async function seedInitialData(sequelize) {
  try {
    log.step('Seeding initial data...');
    
    const { User, Category, Product } = models;
    
    // Create admin user if not exists
    const [adminUser, adminCreated] = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        email: '<EMAIL>',
        password: 'Admin123!', // This will be hashed by the model
        firstName: 'Admin',
        lastName: 'User',
        role: 'super_admin',
        isVerified: true,
        isActive: true
      }
    });
    
    if (adminCreated) {
      log.success('Admin user created');
    } else {
      log.info('Admin user already exists');
    }
    
    // Create default categories
    const defaultCategories = [
      {
        name: 'Flower',
        slug: 'flower',
        description: 'Premium cannabis flower products',
        isActive: true,
        sortOrder: 1
      },
      {
        name: 'Edibles',
        slug: 'edibles',
        description: 'Cannabis-infused edible products',
        isActive: true,
        sortOrder: 2
      },
      {
        name: 'Concentrates',
        slug: 'concentrates',
        description: 'High-quality cannabis concentrates',
        isActive: true,
        sortOrder: 3
      },
      {
        name: 'Accessories',
        slug: 'accessories',
        description: 'Cannabis accessories and tools',
        isActive: true,
        sortOrder: 4
      }
    ];
    
    for (const categoryData of defaultCategories) {
      const [category, categoryCreated] = await Category.findOrCreate({
        where: { slug: categoryData.slug },
        defaults: categoryData
      });
      
      if (categoryCreated) {
        log.success(`Category '${categoryData.name}' created`);
      } else {
        log.info(`Category '${categoryData.name}' already exists`);
      }
    }
    
    log.success('Initial data seeded successfully');
    return true;
  } catch (error) {
    log.error(`Failed to seed initial data: ${error.message}`);
    return false;
  }
}

/**
 * Create database indexes for performance
 */
async function createIndexes(sequelize) {
  try {
    log.step('Creating database indexes...');
    
    const queries = [
      // Product indexes
      'CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id)',
      'CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_products_featured ON products(is_featured)',
      'CREATE INDEX IF NOT EXISTS idx_products_price ON products(price)',
      'CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at)',
      
      // Order indexes
      'CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)',
      
      // User indexes
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)',
      
      // Review indexes
      'CREATE INDEX IF NOT EXISTS idx_reviews_product_id ON reviews(product_id)',
      'CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews(user_id)',
      
      // Address indexes
      'CREATE INDEX IF NOT EXISTS idx_addresses_user_id ON addresses(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_addresses_type ON addresses(type)'
    ];
    
    for (const query of queries) {
      await sequelize.query(query);
    }
    
    log.success('Database indexes created successfully');
    return true;
  } catch (error) {
    log.error(`Failed to create indexes: ${error.message}`);
    return false;
  }
}

/**
 * Main setup function
 */
async function setupDatabase() {
  log.info('Starting Nirvana Organics Database Setup...');
  log.info('=====================================');
  
  // Validate environment variables
  if (!process.env.DB_HOST || !process.env.DB_NAME || !process.env.DB_USER) {
    log.error('Missing required database environment variables');
    log.error('Please ensure DB_HOST, DB_NAME, and DB_USER are set in your .env file');
    process.exit(1);
  }
  
  log.info(`Database Host: ${dbConfig.host}`);
  log.info(`Database Name: ${dbConfig.database}`);
  log.info(`Database User: ${dbConfig.username}`);
  log.info('=====================================');
  
  // Create Sequelize instance
  const sequelize = new Sequelize(
    dbConfig.database,
    dbConfig.username,
    dbConfig.password,
    {
      host: dbConfig.host,
      port: dbConfig.port,
      dialect: dbConfig.dialect,
      logging: dbConfig.logging,
      pool: dbConfig.pool,
      define: dbConfig.define
    }
  );
  
  try {
    // Step 1: Test connection
    const connectionSuccess = await testConnection(sequelize);
    if (!connectionSuccess) {
      process.exit(1);
    }
    
    // Step 2: Create tables
    const tablesSuccess = await createTables(sequelize);
    if (!tablesSuccess) {
      process.exit(1);
    }
    
    // Step 3: Create indexes
    const indexesSuccess = await createIndexes(sequelize);
    if (!indexesSuccess) {
      log.warning('Some indexes may not have been created, but continuing...');
    }
    
    // Step 4: Seed initial data
    const seedSuccess = await seedInitialData(sequelize);
    if (!seedSuccess) {
      log.warning('Initial data seeding failed, but database structure is ready');
    }
    
    log.success('Database setup completed successfully!');
    log.info('=====================================');
    log.info('Next steps:');
    log.info('1. Update your .env file with the correct database credentials');
    log.info('2. Test your application connection');
    log.info('3. Configure SSL certificates');
    log.info('=====================================');
    
  } catch (error) {
    log.error(`Database setup failed: ${error.message}`);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run setup if called directly
if (require.main === module) {
  setupDatabase().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = { setupDatabase };
