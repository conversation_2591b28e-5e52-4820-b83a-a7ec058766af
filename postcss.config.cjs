/** @type {import('postcss-load-config').Config} */
module.exports = {
  plugins: [
    // CSS nesting support - using postcss-nested instead of @tailwindcss/nesting
    require('postcss-nested'),

    // Tailwind CSS - main utility framework
    require('tailwindcss'),

    // Autoprefixer - add vendor prefixes for browser compatibility
    require('autoprefixer')({
      overrideBrowserslist: [
        '> 1%',
        'last 2 versions',
        'not dead',
        'not ie 11'
      ],
      grid: 'autoplace',
      remove: true,
      cascade: false
    })
  ]
}
