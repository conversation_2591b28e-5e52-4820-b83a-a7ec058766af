# Production Configuration Checklist
## Nirvana Organics E-commerce Platform

### 🔧 Environment Variables Setup

#### ✅ Security Configuration (CRITICAL - MUST CHANGE)
- [ ] **JWT_SECRET** - Generate a secure 256-bit secret for JWT tokens
- [ ] **JWT_REFRESH_SECRET** - Generate a different 256-bit secret for refresh tokens
- [ ] **SESSION_SECRET** - Generate a secure session secret
- [ ] **ENCRYPTION_KEY** - Generate a 256-bit encryption key
- [ ] **FORCE_HTTPS** - Set to `true` for production

#### ✅ Database Configuration
- [ ] **DB_HOST** - Currently: `srv1753.hstgr.io` (Verify this is correct)
- [ ] **DB_PORT** - Currently: `3306` (Standard MySQL port)
- [ ] **DB_NAME** - Currently: `u106832845_nirvana` (Verify database exists)
- [ ] **DB_USER** - Currently: `u106832845_root` (Verify user has proper permissions)
- [ ] **DB_PASSWORD** - Currently set (Verify password is correct)

#### ✅ Frontend & API URLs
- [ ] **FRONTEND_URL** - Update to: `https://shopnirvanaorganics.com`
- [ ] **BACKEND_URL** - Update to: `https://shopnirvanaorganics.com`
- [ ] **API_BASE_URL** - Update to: `https://shopnirvanaorganics.com/api`
- [ ] **CORS_ORIGIN** - Update to production domains

#### ✅ Email Configuration
- [ ] **EMAIL_HOST** - Currently: `smtp.gmail.com` (Verify SMTP server)
- [ ] **EMAIL_USER** - Currently: `<EMAIL>`
- [ ] **EMAIL_PASS** - App password for Gmail (Verify it's valid)
- [ ] **EMAIL_ORDERS** - Currently: `<EMAIL>`
- [ ] **EMAIL_SUPPORT** - Currently: `<EMAIL>`

### 💳 Third-Party Service Integration

#### ✅ Square Payment Service (CRITICAL - MUST CHANGE)
- [ ] **SQUARE_APPLICATION_ID** - Change from sandbox to production ID
- [ ] **SQUARE_ACCESS_TOKEN** - Change from sandbox to production token
- [ ] **SQUARE_LOCATION_ID** - Update to production location ID
- [ ] **SQUARE_ENVIRONMENT** - Set to `production`
- [ ] **SQUARE_WEBHOOK_SIGNATURE_KEY** - Update to production webhook key

#### ✅ Google OAuth Configuration
- [ ] **GOOGLE_CLIENT_ID** - Verify for production domain
- [ ] **GOOGLE_CLIENT_SECRET** - Verify for production domain
- [ ] **GOOGLE_OAUTH_CALLBACK_URL** - Update to production callback URL

#### ✅ External APIs (UPDATE REQUIRED)
- [ ] **SHIPPING_API_KEY** - Replace placeholder with actual API key
- [ ] **ANALYTICS_API_KEY** - Replace placeholder with actual API key
- [ ] **USPS_USER_ID** - Replace placeholder with actual USPS user ID
- [ ] **USPS_API_KEY** - Replace placeholder with actual USPS API key
- [ ] **WHATSAPP_PHONE_NUMBER** - Replace placeholder with actual phone number
- [ ] **WHATSAPP_ACCESS_TOKEN** - Replace placeholder with actual access token
- [ ] **WHATSAPP_PHONE_NUMBER_ID** - Replace placeholder with actual phone number ID
- [ ] **WHATSAPP_WEBHOOK_VERIFY_TOKEN** - Replace placeholder with actual verify token
- [ ] **WHATSAPP_BUSINESS_ACCOUNT_ID** - Replace placeholder with actual account ID

### 🗄️ Database Configuration

#### ✅ Database Connection
- [ ] Test database connectivity from production server
- [ ] Verify database user permissions (CREATE, ALTER, DROP, INSERT, UPDATE, DELETE, SELECT)
- [ ] Ensure database charset is `utf8mb4` with `utf8mb4_unicode_ci` collation
- [ ] Configure SSL connection if required by hosting provider

#### ✅ Database Migrations
- [ ] Run database migrations: `npm run migrate:prod`
- [ ] Verify all tables are created correctly
- [ ] Check database schema: `npm run verify:schema`
- [ ] Seed initial data if needed: `npm run seed:prod`

### 🔒 SSL Certificate Setup

#### ✅ SSL Certificate Installation
- [ ] Install SSL certificate for `shopnirvanaorganics.com`
- [ ] Install SSL certificate for `www.shopnirvanaorganics.com`
- [ ] Verify certificate chain is complete
- [ ] Check certificate expiration date
- [ ] Set up automatic renewal (Let's Encrypt or hosting provider)

#### ✅ HTTPS Configuration
- [ ] Configure web server (Nginx/Apache) to use SSL certificates
- [ ] Set up HTTP to HTTPS redirects (301 redirects)
- [ ] Update `FORCE_HTTPS=true` in environment variables
- [ ] Test HTTPS enforcement on all routes
- [ ] Verify security headers are properly set

### 🌐 Web Server Configuration

#### ✅ Nginx Configuration (if using Nginx)
- [ ] Update server names in Nginx config
- [ ] Configure SSL certificate paths
- [ ] Set up proper proxy headers
- [ ] Configure rate limiting
- [ ] Set up static file serving for uploads
- [ ] Configure security headers

#### ✅ PM2 Process Management
- [ ] Update `ecosystem.config.js` with production settings
- [ ] Configure PM2 startup script
- [ ] Set up log rotation
- [ ] Configure memory limits and restart policies

### 🔐 Security Configuration

#### ✅ Security Headers
- [ ] Verify Content Security Policy (CSP) headers
- [ ] Check X-Frame-Options, X-XSS-Protection headers
- [ ] Ensure HSTS (HTTP Strict Transport Security) is enabled
- [ ] Configure proper CORS settings

#### ✅ Rate Limiting
- [ ] Configure API rate limiting
- [ ] Set up authentication rate limiting
- [ ] Configure payment endpoint rate limiting

### 📁 Files That Need Production Values

#### ✅ Configuration Files
- [ ] `.env.production` - Main environment configuration
- [ ] `deployment.config.js` - Update server host, repository URL
- [ ] `ecosystem.config.js` - Update deployment hosts and repository
- [ ] `scripts/setup-server.sh` - Update domain names and SSL configuration

#### ✅ Frontend Configuration
- [ ] Update API endpoints in frontend build
- [ ] Configure production build settings
- [ ] Update CSP headers for production domains

### 🧪 Testing & Validation

#### ✅ Environment Validation
- [ ] Run environment validation: `npm run validate:env`
- [ ] Test database connection: `npm run test:database`
- [ ] Test email service: `npm run test:email`
- [ ] Validate authentication system: `npm run test:auth`

#### ✅ Production Testing
- [ ] Test SSL certificate installation
- [ ] Verify HTTPS redirects work correctly
- [ ] Test payment processing with Square production API
- [ ] Test email sending functionality
- [ ] Verify Google OAuth works with production callback URL

### 🚀 Deployment Commands

```bash
# 1. Copy production environment
cp .env.production .env

# 2. Install production dependencies
npm ci --production

# 3. Run database migrations
npm run migrate:prod

# 4. Start production server
npm run start:prod

# 5. Or use PM2
pm2 start ecosystem.config.js --env production
```

### ⚠️ Critical Security Notes

1. **Never commit `.env.production` to version control**
2. **Generate unique secrets for JWT, session, and encryption keys**
3. **Use production Square API credentials, not sandbox**
4. **Ensure all placeholder values are replaced with actual credentials**
5. **Test all third-party integrations in production environment**
6. **Set up monitoring and logging for production**
7. **Configure backup strategies for database and uploaded files**
