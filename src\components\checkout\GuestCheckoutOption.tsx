import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  UserIcon, 
  UserPlusIcon, 
  ShoppingBagIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface GuestCheckoutOptionProps {
  onGuestCheckout: () => void;
  onLoginRedirect: () => void;
  onRegisterRedirect: () => void;
}

const GuestCheckoutOption: React.FC<GuestCheckoutOptionProps> = ({
  onGuestCheckout,
  onLoginRedirect,
  onRegisterRedirect
}) => {
  const [selectedOption, setSelectedOption] = useState<'guest' | 'login' | 'register' | null>(null);

  const handleContinue = () => {
    switch (selectedOption) {
      case 'guest':
        onGuestCheckout();
        break;
      case 'login':
        onLoginRedirect();
        break;
      case 'register':
        onRegisterRedirect();
        break;
      default:
        break;
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <ShoppingBagIcon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            How would you like to checkout?
          </h2>
          <p className="text-gray-600">
            Choose your preferred checkout method to continue with your order
          </p>
        </div>

        <div className="space-y-4 mb-8">
          {/* Guest Checkout Option */}
          <div
            className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${
              selectedOption === 'guest'
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedOption('guest')}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className={`w-4 h-4 rounded-full border-2 mt-1 ${
                  selectedOption === 'guest'
                    ? 'border-primary-500 bg-primary-500'
                    : 'border-gray-300'
                }`}>
                  {selectedOption === 'guest' && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
              </div>
              <div className="ml-4 flex-1">
                <div className="flex items-center mb-2">
                  <ShoppingBagIcon className="h-5 w-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Continue as Guest
                  </h3>
                  <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    Fastest
                  </span>
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  Complete your purchase quickly without creating an account. You'll receive order confirmation and tracking via email.
                </p>
                <div className="flex items-center text-sm text-gray-500">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>No account required</span>
                  <span className="mx-2">•</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Email order tracking</span>
                  <span className="mx-2">•</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Secure checkout</span>
                </div>
              </div>
            </div>
          </div>

          {/* Login Option */}
          <div
            className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${
              selectedOption === 'login'
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedOption('login')}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className={`w-4 h-4 rounded-full border-2 mt-1 ${
                  selectedOption === 'login'
                    ? 'border-primary-500 bg-primary-500'
                    : 'border-gray-300'
                }`}>
                  {selectedOption === 'login' && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
              </div>
              <div className="ml-4 flex-1">
                <div className="flex items-center mb-2">
                  <UserIcon className="h-5 w-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Sign In to Your Account
                  </h3>
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  Access your saved addresses, order history, and account preferences for a personalized experience.
                </p>
                <div className="flex items-center text-sm text-gray-500">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Saved addresses</span>
                  <span className="mx-2">•</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Order history</span>
                  <span className="mx-2">•</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Faster checkout</span>
                </div>
              </div>
            </div>
          </div>

          {/* Register Option */}
          <div
            className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${
              selectedOption === 'register'
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedOption('register')}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className={`w-4 h-4 rounded-full border-2 mt-1 ${
                  selectedOption === 'register'
                    ? 'border-primary-500 bg-primary-500'
                    : 'border-gray-300'
                }`}>
                  {selectedOption === 'register' && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
              </div>
              <div className="ml-4 flex-1">
                <div className="flex items-center mb-2">
                  <UserPlusIcon className="h-5 w-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Create New Account
                  </h3>
                  <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                    Recommended
                  </span>
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  Create an account to enjoy exclusive benefits, track orders, and get personalized recommendations.
                </p>
                <div className="flex items-center text-sm text-gray-500">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Exclusive offers</span>
                  <span className="mx-2">•</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Loyalty rewards</span>
                  <span className="mx-2">•</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span>Easy reordering</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Age Verification Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-semibold text-yellow-800 mb-1">
                Age Verification Required
              </h4>
              <p className="text-sm text-yellow-700">
                You must be 21 years or older to purchase cannabis products. Age verification will be required during checkout regardless of your chosen method.
              </p>
            </div>
          </div>
        </div>

        {/* Continue Button */}
        <div className="flex justify-center">
          <button
            onClick={handleContinue}
            disabled={!selectedOption}
            className={`px-8 py-3 rounded-lg font-semibold text-white transition-colors ${
              selectedOption
                ? 'bg-primary-600 hover:bg-primary-700'
                : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            {selectedOption === 'guest' && 'Continue as Guest'}
            {selectedOption === 'login' && 'Sign In'}
            {selectedOption === 'register' && 'Create Account'}
            {!selectedOption && 'Select an Option'}
          </button>
        </div>

        {/* Security Notice */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            🔒 Your information is protected with 256-bit SSL encryption
          </p>
        </div>
      </div>
    </div>
  );
};

export default GuestCheckoutOption;
