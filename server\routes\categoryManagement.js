const express = require('express');
const router = express.Router();
const categoryManagementController = require('../controllers/categoryManagementController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const {
  validateCategoryId,
  validateCreateCategory,
  validateUpdateCategory,
  validateBulkCategoryOperation,
  validateCategoryReorder,
  validateCategoryQuery,
  requireCategoryManagementAccess,
  checkCategoryExists,
  validateSlugUniqueness,
  preventCircularReference
} = require('../middleware/categoryValidation');

// All routes require authentication and admin privileges
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/categories
// @desc    Get all categories with filtering and pagination
// @access  Private (Admin/Manager)
router.get('/', 
  requireCategoryManagementAccess,
  validateCategoryQuery,
  categoryManagementController.getCategories
);

// @route   GET /api/admin/categories/statistics
// @desc    Get category statistics
// @access  Private (Admin/Manager)
router.get('/statistics',
  requireCategoryManagementAccess,
  categoryManagementController.getCategoryStatistics
);

// @route   GET /api/admin/categories/:categoryId
// @desc    Get single category by ID
// @access  Private (Admin/Manager)
router.get('/:categoryId',
  requireCategoryManagementAccess,
  validateCategoryId,
  checkCategoryExists,
  categoryManagementController.getCategoryById
);

// @route   POST /api/admin/categories
// @desc    Create new category
// @access  Private (Admin/Manager)
router.post('/',
  requireCategoryManagementAccess,
  validateCreateCategory,
  validateSlugUniqueness,
  categoryManagementController.createCategory
);

// @route   PUT /api/admin/categories/:categoryId
// @desc    Update category
// @access  Private (Admin/Manager)
router.put('/:categoryId',
  requireCategoryManagementAccess,
  validateUpdateCategory,
  checkCategoryExists,
  validateSlugUniqueness,
  preventCircularReference,
  categoryManagementController.updateCategory
);

// @route   DELETE /api/admin/categories/:categoryId
// @desc    Delete category
// @access  Private (Admin/Manager)
router.delete('/:categoryId',
  requireCategoryManagementAccess,
  validateCategoryId,
  checkCategoryExists,
  categoryManagementController.deleteCategory
);

// @route   POST /api/admin/categories/bulk
// @desc    Bulk operations on categories (activate/deactivate/move/delete)
// @access  Private (Admin/Manager)
router.post('/bulk',
  requireCategoryManagementAccess,
  validateBulkCategoryOperation,
  categoryManagementController.bulkUpdateCategories
);

// @route   POST /api/admin/categories/reorder
// @desc    Reorder categories
// @access  Private (Admin/Manager)
router.post('/reorder',
  requireCategoryManagementAccess,
  validateCategoryReorder,
  categoryManagementController.reorderCategories
);

module.exports = router;
