#!/bin/bash

# ============================================================================
# Deployment Update Script
# Updates existing deployment with new release
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${CYAN}🔧 $1${NC}"; }

# Configuration
PROJECT_NAME="nirvana-organics-backend"
PROJECT_PATH="/var/www/$PROJECT_NAME"
BACKUP_PATH="/var/backups/$PROJECT_NAME"
LOG_FILE="/var/log/$PROJECT_NAME-update.log"

# Create log file
touch "$LOG_FILE"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "============================================================================"
echo "🚀 DEPLOYMENT UPDATE - NIRVANA ORGANICS"
echo "============================================================================"
echo "Started: $(date)"
echo "============================================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: Pre-update checks
log_step "Step 1: Pre-update checks"

# Check if deployment package exists
PACKAGE_FILE=$(find . -name "nirvana-organics-backend-production-*.tar.gz" | head -1)

if [ -z "$PACKAGE_FILE" ]; then
    log_error "Deployment package not found!"
    log_info "Please upload the deployment package (nirvana-organics-backend-production-*.tar.gz) to this directory"
    exit 1
fi

log_info "Found deployment package: $PACKAGE_FILE"

# Check current deployment
if [ ! -d "$PROJECT_PATH/current" ]; then
    log_error "No current deployment found. Use vps-deployment.sh for initial deployment."
    exit 1
fi

CURRENT_RELEASE=$(readlink "$PROJECT_PATH/current" | xargs basename)
log_info "Current release: $CURRENT_RELEASE"

# Step 2: Create pre-update backup
log_step "Step 2: Creating pre-update backup"

UPDATE_BACKUP_DIR="$BACKUP_PATH/update_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$UPDATE_BACKUP_DIR"

# Backup current database
log_info "Backing up current database..."
if [ -f "$PROJECT_PATH/current/.env" ]; then
    DB_PASSWORD=$(grep "^DB_PASSWORD=" "$PROJECT_PATH/current/.env" | cut -d'=' -f2)
    DB_NAME=$(grep "^DB_NAME=" "$PROJECT_PATH/current/.env" | cut -d'=' -f2)
    DB_USER=$(grep "^DB_USER=" "$PROJECT_PATH/current/.env" | cut -d'=' -f2)
    
    mysqldump -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" | gzip > "$UPDATE_BACKUP_DIR/database_before_update.sql.gz"
    log_success "Database backup created"
else
    log_warning "Could not backup database - .env file not found"
fi

# Backup current application
log_info "Backing up current application..."
tar -czf "$UPDATE_BACKUP_DIR/application_before_update.tar.gz" -C "$PROJECT_PATH" current
log_success "Application backup created"

# Step 3: Extract new release
log_step "Step 3: Extracting new release"

RELEASE_DIR="$PROJECT_PATH/releases/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RELEASE_DIR"

log_info "Extracting package to $RELEASE_DIR..."
tar -xzf "$PACKAGE_FILE" -C "$RELEASE_DIR" --strip-components=1

# Set ownership
chown -R nirvana:nirvana "$RELEASE_DIR"

log_success "New release extracted"

# Step 4: Preserve configuration
log_step "Step 4: Preserving configuration"

# Copy environment configuration from current deployment
if [ -f "$PROJECT_PATH/current/.env" ]; then
    cp "$PROJECT_PATH/current/.env" "$RELEASE_DIR/.env"
    chown nirvana:nirvana "$RELEASE_DIR/.env"
    log_success "Environment configuration preserved"
else
    log_warning "No .env file found in current deployment"
    
    # Use production template if available
    if [ -f "$RELEASE_DIR/.env.production.final" ]; then
        cp "$RELEASE_DIR/.env.production.final" "$RELEASE_DIR/.env"
        chown nirvana:nirvana "$RELEASE_DIR/.env"
        log_warning "Using production template - please review configuration"
    fi
fi

# Copy uploads directory
if [ -d "$PROJECT_PATH/shared/uploads" ]; then
    ln -sfn "$PROJECT_PATH/shared/uploads" "$RELEASE_DIR/uploads"
    log_success "Uploads directory linked"
fi

# Step 5: Install dependencies
log_step "Step 5: Installing dependencies"

cd "$RELEASE_DIR"

log_info "Installing Node.js dependencies..."
sudo -u nirvana npm ci --production
log_success "Dependencies installed"

# Step 6: Run database migrations
log_step "Step 6: Running database migrations"

log_info "Running database migrations..."
if sudo -u nirvana npm run migrate:prod; then
    log_success "Database migrations completed"
else
    log_warning "Database migrations failed - continuing with deployment"
fi

# Step 7: Run pre-deployment tests
log_step "Step 7: Running pre-deployment tests"

# Test database connection
log_info "Testing database connection..."
if sudo -u nirvana npm run test:database; then
    log_success "Database connection test passed"
else
    log_error "Database connection test failed"
    exit 1
fi

# Validate environment
log_info "Validating environment configuration..."
if sudo -u nirvana npm run validate:env; then
    log_success "Environment validation passed"
else
    log_warning "Environment validation failed - continuing with deployment"
fi

# Step 8: Switch to new release
log_step "Step 8: Switching to new release"

# Stop current application
log_info "Stopping current application..."
sudo -u nirvana pm2 stop all

# Update symlink
log_info "Updating symlink to new release..."
ln -sfn "$RELEASE_DIR" "$PROJECT_PATH/current"

# Start application with new release
log_info "Starting application with new release..."
sudo -u nirvana pm2 start ecosystem.config.js --env production

# Save PM2 configuration
sudo -u nirvana pm2 save

log_success "Application switched to new release"

# Step 9: Health checks
log_step "Step 9: Running health checks"

# Wait for application to start
log_info "Waiting for application to start..."
sleep 15

# Check PM2 status
if sudo -u nirvana pm2 list | grep -q "online"; then
    log_success "PM2 processes are running"
else
    log_error "PM2 processes failed to start"
    log_info "Rolling back to previous release..."
    ./scripts/rollback-deployment.sh
    exit 1
fi

# Check application health
HEALTH_CHECK_ATTEMPTS=5
HEALTH_CHECK_DELAY=10

for i in $(seq 1 $HEALTH_CHECK_ATTEMPTS); do
    log_info "Health check attempt $i/$HEALTH_CHECK_ATTEMPTS..."
    
    if curl -f -s http://localhost:5000/health >/dev/null 2>&1; then
        log_success "Application health check passed"
        break
    else
        if [ $i -eq $HEALTH_CHECK_ATTEMPTS ]; then
            log_error "Application health check failed after $HEALTH_CHECK_ATTEMPTS attempts"
            log_info "Rolling back to previous release..."
            ./scripts/rollback-deployment.sh
            exit 1
        else
            log_warning "Health check failed, retrying in ${HEALTH_CHECK_DELAY}s..."
            sleep $HEALTH_CHECK_DELAY
        fi
    fi
done

# Check HTTPS health
if curl -f -s https://shopnirvanaorganics.com/health >/dev/null 2>&1; then
    log_success "HTTPS health check passed"
else
    log_warning "HTTPS health check failed - check SSL configuration"
fi

# Step 10: Post-deployment tasks
log_step "Step 10: Post-deployment tasks"

# Validate third-party services
log_info "Validating third-party services..."
if sudo -u nirvana npm run setup:services; then
    log_success "Third-party services validation passed"
else
    log_warning "Third-party services validation failed - check configuration"
fi

# Update monitoring
if [ -f "/opt/monitoring/scripts/health-check.sh" ]; then
    log_info "Running monitoring health check..."
    /opt/monitoring/scripts/health-check.sh
    log_success "Monitoring health check completed"
fi

# Step 11: Cleanup old releases
log_step "Step 11: Cleanup old releases"

# Keep only last 5 releases
RELEASES_TO_KEEP=5
RELEASE_COUNT=$(ls -1 "$PROJECT_PATH/releases/" | wc -l)

if [ "$RELEASE_COUNT" -gt "$RELEASES_TO_KEEP" ]; then
    log_info "Cleaning up old releases (keeping last $RELEASES_TO_KEEP)..."
    ls -1t "$PROJECT_PATH/releases/" | tail -n +$((RELEASES_TO_KEEP + 1)) | while read old_release; do
        rm -rf "$PROJECT_PATH/releases/$old_release"
        log_info "Removed old release: $old_release"
    done
    log_success "Old releases cleaned up"
else
    log_info "No old releases to clean up"
fi

# Step 12: Final verification
log_step "Step 12: Final verification"

NEW_RELEASE=$(readlink "$PROJECT_PATH/current" | xargs basename)
log_info "Deployment completed - now running: $NEW_RELEASE"

# Run comprehensive health check
log_info "Running comprehensive health check..."
if [ -f "/opt/monitoring/scripts/health-check.sh" ]; then
    /opt/monitoring/scripts/health-check.sh
fi

# Final status
echo ""
echo "============================================================================"
log_success "🎉 DEPLOYMENT UPDATE COMPLETED!"
echo "============================================================================"
echo "Completed: $(date)"
echo ""

log_info "Update Summary:"
echo "• Updated from: $CURRENT_RELEASE"
echo "• Updated to: $NEW_RELEASE"
echo "• Backup location: $UPDATE_BACKUP_DIR"
echo "• Application status: $(sudo -u nirvana pm2 list | grep nirvana | awk '{print $10}' | head -1)"

echo ""
log_info "Post-Update Checklist:"
echo "□ Test all critical application functionality"
echo "□ Verify payment processing works correctly"
echo "□ Check email notifications"
echo "□ Test user authentication and registration"
echo "□ Verify SSL certificate is working"
echo "□ Monitor application logs for errors"
echo "□ Check application performance"

echo ""
log_warning "Important Notes:"
echo "• Backup created at: $UPDATE_BACKUP_DIR"
echo "• Monitor application closely after update"
echo "• Rollback available: ./scripts/rollback-deployment.sh"
echo "• Review logs: sudo -u nirvana pm2 logs"

echo ""
log_info "Useful Commands:"
echo "• View logs: sudo -u nirvana pm2 logs"
echo "• Check status: sudo -u nirvana pm2 status"
echo "• Restart app: sudo -u nirvana pm2 restart all"
echo "• Rollback: ./scripts/rollback-deployment.sh"
echo "• View update log: tail -f $LOG_FILE"

log_success "Deployment update completed successfully! 🚀"
