const express = require('express');
const router = express.Router();
const {
  authenticate,
  authorize,
  requireAdmin,
  requireManagerOrAdmin,
  requireSystemAccess,
  requireAuditAccess,
  auditAdminAction
} = require('../middleware/auth');
const performanceMonitor = require('../utils/performanceMonitor');
const emailService = require('../services/emailService');
const adminController = require('../controllers/adminController');
const customerController = require('../controllers/customerController');
const notificationController = require('../controllers/notificationController');
const emailController = require('../controllers/emailController');
const bannerController = require('../controllers/bannerController');

// Import sub-routes
const userManagementRoutes = require('./userManagement');
const reviewManagementRoutes = require('./reviewManagement');
const couponManagementRoutes = require('./couponManagement');
const categoryManagementRoutes = require('./categoryManagement');
const analyticsRoutes = require('./analytics');

// Middleware to ensure admin access (legacy)
const adminOnly = [authenticate, authorize(['admin'])];

// Dashboard routes - Managers can view stats, only admins can sync inventory
router.get('/dashboard', authenticate, requireManagerOrAdmin, adminController.getDashboardStats);
router.get('/dashboard/stats', authenticate, requireManagerOrAdmin, adminController.getDashboardStats);
router.post('/inventory/sync', authenticate, requireAdmin, auditAdminAction('SYNC_INVENTORY', 'SYSTEM'), adminController.syncInventoryWithSquare);
router.get('/analytics/customers', authenticate, requireManagerOrAdmin, adminController.getCustomerAnalytics);

// Order management routes - Both managers and admins can manage orders
router.get('/orders', authenticate, requireManagerOrAdmin, adminController.getAllOrders);
router.put('/orders/:orderId/status', authenticate, requireManagerOrAdmin, auditAdminAction('UPDATE_ORDER_STATUS', 'ORDER'), adminController.updateOrderStatusAdmin);

// Product management routes - Managers can view, only admins can bulk update
router.get('/products', authenticate, requireManagerOrAdmin, adminController.getAllProducts);
router.put('/products/bulk-update', authenticate, requireAdmin, auditAdminAction('BULK_UPDATE_PRODUCTS', 'PRODUCT'), adminController.bulkUpdateProducts);

// User management routes (comprehensive CRUD)
router.use('/users', userManagementRoutes);

// Review management routes (comprehensive CRUD)
router.use('/reviews', reviewManagementRoutes);

// Coupon management routes (comprehensive CRUD)
router.use('/coupons', couponManagementRoutes);

// Category management routes (comprehensive CRUD)
router.use('/categories', categoryManagementRoutes);

// Analytics routes (comprehensive reporting and metrics)
router.use('/analytics', analyticsRoutes);

// Legacy user management route (for backward compatibility)
router.get('/users-legacy', adminOnly, adminController.getUserManagement);

// Customer management routes - Both managers and admins can view/manage customers
router.get('/customers', authenticate, requireManagerOrAdmin, customerController.getCustomers);
router.get('/customers/:customerId', authenticate, requireManagerOrAdmin, customerController.getCustomerProfile);
router.put('/customers/:customerId', authenticate, requireManagerOrAdmin, auditAdminAction('UPDATE_CUSTOMER', 'USER'), customerController.updateCustomer);
router.get('/customers/segments/all', authenticate, requireManagerOrAdmin, customerController.getCustomerSegments);

// Push notification routes - Both managers and admins can send notifications
router.post('/notifications/push/send', authenticate, requireManagerOrAdmin, auditAdminAction('SEND_PUSH_NOTIFICATION', 'NOTIFICATION'), notificationController.sendPushNotification);
router.get('/notifications/history', authenticate, requireManagerOrAdmin, notificationController.getNotificationHistory);
router.get('/notifications/push/stats', authenticate, requireManagerOrAdmin, notificationController.getPushStats);

// Email marketing routes - Only admins can send promotional emails
router.post('/email/send', authenticate, requireAdmin, auditAdminAction('SEND_PROMOTIONAL_EMAIL', 'EMAIL'), emailController.sendPromotionalEmail);
router.get('/email/campaigns', authenticate, requireManagerOrAdmin, emailController.getEmailCampaigns);
router.get('/email/subscribers', authenticate, requireManagerOrAdmin, emailController.getNewsletterSubscribers);
router.get('/email/stats', authenticate, requireManagerOrAdmin, emailController.getEmailStats);

// Banner management routes - Only admins can manage banners
router.get('/banners', authenticate, requireManagerOrAdmin, bannerController.getBanners);
router.post('/banners', authenticate, requireAdmin, auditAdminAction('CREATE_BANNER', 'BANNER'), bannerController.createBanner);
router.put('/banners/:bannerId', authenticate, requireAdmin, auditAdminAction('UPDATE_BANNER', 'BANNER'), bannerController.updateBanner);
router.patch('/banners/:bannerId/toggle', authenticate, requireAdmin, auditAdminAction('TOGGLE_BANNER', 'BANNER'), bannerController.toggleBannerStatus);
router.delete('/banners/:bannerId', authenticate, requireAdmin, auditAdminAction('DELETE_BANNER', 'BANNER'), bannerController.deleteBanner);
router.post('/banners/preview', authenticate, requireAdmin, bannerController.previewBanner);
router.get('/banners/:bannerId/analytics', authenticate, requireManagerOrAdmin, bannerController.getBannerAnalytics);

// System metrics and monitoring routes - Only admins can access system metrics
router.get('/metrics', authenticate, requireSystemAccess, (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    res.json({
      ...metrics,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      emailProvider: 'Gmail'
    });
  } catch (error) {
    console.error('Error getting metrics:', error);
    res.status(500).json({ error: 'Failed to get system metrics' });
  }
});

// Gmail email service monitoring - Only admins can check email service status
router.get('/email/service-status', authenticate, requireSystemAccess, async (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    const emailMetrics = metrics.email || {};

    // Test Gmail connection
    let connectionStatus = 'unknown';
    try {
      const isConnected = await emailService.verifyGmailTransporter();
      connectionStatus = isConnected ? 'connected' : 'disconnected';
    } catch (error) {
      connectionStatus = 'error';
    }

    const emailStatus = {
      provider: 'Gmail',
      connectionStatus,
      configuration: {
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        user: process.env.EMAIL_USER,
        from: process.env.EMAIL_FROM,
        authType: process.env.EMAIL_OAUTH2_CLIENT_ID ? 'OAuth2' : 'App Password'
      },
      metrics: {
        totalSent: emailMetrics.totalSent || 0,
        totalErrors: emailMetrics.totalErrors || 0,
        errorRate: emailMetrics.errorRate || 0,
        avgSendTime: emailMetrics.avgSendTime || 0
      },
      limits: {
        dailyLimit: process.env.EMAIL_OAUTH2_CLIENT_ID ? '2000+ (Google Workspace)' : '500 (Free Gmail)',
        currentUsage: 'Not tracked'
      },
      lastChecked: new Date().toISOString()
    };

    res.json(emailStatus);
  } catch (error) {
    console.error('Error getting email status:', error);
    res.status(500).json({ error: 'Failed to get email status' });
  }
});

// Test Gmail email sending - Only admins can test email sending
router.post('/email/test-send', authenticate, requireSystemAccess, auditAdminAction('TEST_EMAIL_SEND', 'EMAIL'), async (req, res) => {
  try {
    const { email, type = 'basic' } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email address is required' });
    }

    let result;
    const startTime = Date.now();

    switch (type) {
      case 'basic':
        result = await emailService.sendTestEmail(email);
        break;
      case 'welcome':
        result = await emailService.sendWelcomeEmail(email);
        break;
      case 'password-reset':
        result = await emailService.sendPasswordResetEmail(email, 'test-token-123');
        break;
      default:
        return res.status(400).json({ error: 'Invalid email type' });
    }

    const sendTime = Date.now() - startTime;

    res.json({
      success: true,
      messageId: result.messageId,
      sendTime: `${sendTime}ms`,
      type,
      recipient: email,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Email test failed:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
