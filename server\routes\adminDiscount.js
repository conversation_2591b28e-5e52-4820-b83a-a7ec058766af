const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const adminDiscountController = require('../controllers/adminDiscountController');

// All routes require admin authentication
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/coupons
// @desc    Get all coupons with pagination and filtering
// @access  Private (Admin)
router.get('/coupons', adminDiscountController.getAllCoupons);

// @route   POST /api/admin/coupons
// @desc    Create new coupon
// @access  Private (Admin)
router.post('/coupons', [
  body('code')
    .notEmpty()
    .withMessage('Coupon code is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters')
    .matches(/^[A-Z0-9_-]+$/)
    .withMessage('Coupon code can only contain uppercase letters, numbers, underscores, and hyphens'),
  body('name')
    .notEmpty()
    .withMessage('Coupon name is required')
    .isLength({ min: 3, max: 100 })
    .withMessage('Coupon name must be between 3 and 100 characters'),
  body('type')
    .isIn(['percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y'])
    .withMessage('Invalid coupon type'),
  body('value')
    .isNumeric()
    .withMessage('Value must be a number')
    .custom((value, { req }) => {
      if (req.body.type === 'percentage' && (value < 0 || value > 100)) {
        throw new Error('Percentage value must be between 0 and 100');
      }
      if (req.body.type === 'fixed_amount' && value < 0) {
        throw new Error('Fixed amount must be non-negative');
      }
      return true;
    }),
  body('minimumOrderAmount')
    .optional()
    .isNumeric()
    .withMessage('Minimum order amount must be a number')
    .custom(value => value >= 0)
    .withMessage('Minimum order amount must be non-negative'),
  body('maximumDiscountAmount')
    .optional()
    .isNumeric()
    .withMessage('Maximum discount amount must be a number')
    .custom(value => value >= 0)
    .withMessage('Maximum discount amount must be non-negative'),
  body('usageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Usage limit must be a positive integer'),
  body('userUsageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User usage limit must be a positive integer'),
  body('validFrom')
    .isISO8601()
    .withMessage('Valid from date must be a valid ISO 8601 date'),
  body('validUntil')
    .isISO8601()
    .withMessage('Valid until date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.validFrom)) {
        throw new Error('Valid until date must be after valid from date');
      }
      return true;
    }),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  body('isReferralCoupon')
    .optional()
    .isBoolean()
    .withMessage('Is referral coupon must be a boolean'),
  body('referrerReward')
    .optional()
    .isNumeric()
    .withMessage('Referrer reward must be a number')
    .custom(value => value >= 0)
    .withMessage('Referrer reward must be non-negative'),
  body('referrerRewardType')
    .optional()
    .isIn(['percentage', 'fixed_amount', 'points'])
    .withMessage('Invalid referrer reward type'),
  body('socialShareBonus')
    .optional()
    .isNumeric()
    .withMessage('Social share bonus must be a number')
    .custom(value => value >= 0)
    .withMessage('Social share bonus must be non-negative'),
  body('socialPlatforms')
    .optional()
    .isArray()
    .withMessage('Social platforms must be an array'),
  body('applicableProducts')
    .optional()
    .isArray()
    .withMessage('Applicable products must be an array'),
  body('applicableCategories')
    .optional()
    .isArray()
    .withMessage('Applicable categories must be an array'),
  body('excludedProducts')
    .optional()
    .isArray()
    .withMessage('Excluded products must be an array'),
  body('buyQuantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Buy quantity must be a positive integer'),
  body('getQuantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Get quantity must be a positive integer'),
  body('getProductIds')
    .optional()
    .isArray()
    .withMessage('Get product IDs must be an array')
], adminDiscountController.createCoupon);

// @route   PUT /api/admin/coupons/:id
// @desc    Update coupon
// @access  Private (Admin)
router.put('/coupons/:id', [
  body('code')
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters')
    .matches(/^[A-Z0-9_-]+$/)
    .withMessage('Coupon code can only contain uppercase letters, numbers, underscores, and hyphens'),
  body('name')
    .optional()
    .isLength({ min: 3, max: 100 })
    .withMessage('Coupon name must be between 3 and 100 characters'),
  body('type')
    .optional()
    .isIn(['percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y'])
    .withMessage('Invalid coupon type'),
  body('value')
    .optional()
    .isNumeric()
    .withMessage('Value must be a number'),
  body('minimumOrderAmount')
    .optional()
    .isNumeric()
    .withMessage('Minimum order amount must be a number')
    .custom(value => value >= 0)
    .withMessage('Minimum order amount must be non-negative'),
  body('maximumDiscountAmount')
    .optional()
    .isNumeric()
    .withMessage('Maximum discount amount must be a number')
    .custom(value => value >= 0)
    .withMessage('Maximum discount amount must be non-negative'),
  body('usageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Usage limit must be a positive integer'),
  body('userUsageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User usage limit must be a positive integer'),
  body('validFrom')
    .optional()
    .isISO8601()
    .withMessage('Valid from date must be a valid ISO 8601 date'),
  body('validUntil')
    .optional()
    .isISO8601()
    .withMessage('Valid until date must be a valid ISO 8601 date'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean')
], adminDiscountController.updateCoupon);

// @route   DELETE /api/admin/coupons/:id
// @desc    Delete coupon
// @access  Private (Admin)
router.delete('/coupons/:id', adminDiscountController.deleteCoupon);

// @route   GET /api/admin/coupons/analytics
// @desc    Get coupon usage analytics
// @access  Private (Admin)
router.get('/coupons/analytics', adminDiscountController.getCouponAnalytics);

// @route   GET /api/admin/referrals/analytics
// @desc    Get referral system analytics
// @access  Private (Admin)
router.get('/referrals/analytics', adminDiscountController.getReferralAnalytics);

// @route   PUT /api/admin/social-shares/:id/verify
// @desc    Verify social media share
// @access  Private (Admin)
router.put('/social-shares/:id/verify', [
  body('bonusAmount')
    .optional()
    .isNumeric()
    .withMessage('Bonus amount must be a number')
    .custom(value => value >= 0)
    .withMessage('Bonus amount must be non-negative')
], adminDiscountController.verifySocialShare);

module.exports = router;
