import React from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/cartSlice';
import { addToast } from '../../store/slices/uiSlice';
import { Product } from '../../types';
import LazyImage from '../common/LazyImage';
import { StarIcon, ShoppingCartIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const dispatch = useAppDispatch();

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!product.isInStock?.()) {
      dispatch(addToast({
        type: 'error',
        title: 'Out of Stock',
        message: 'This product is currently out of stock'
      }));
      return;
    }

    try {
      await dispatch(addToCart({
        productId: product.id.toString(),
        quantity: 1,
        variant: undefined
      })).unwrap();

      dispatch(addToast({
        type: 'success',
        title: 'Added to Cart',
        message: `${product.name} has been added to your cart`
      }));
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to add item to cart'
      }));
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarOutlineIcon className="h-4 w-4 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <StarIcon className="h-4 w-4 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarOutlineIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return stars;
  };

  const discountPercentage = product.comparePrice && product.comparePrice > product.price
    ? Math.round(((product.comparePrice - product.price) / product.comparePrice) * 100)
    : 0;

  const isOutOfStock = product.trackQuantity && product.quantity <= 0;
  const isLowStock = product.trackQuantity && product.quantity <= product.lowStockThreshold && product.quantity > 0;

  return (
    <div className="group relative bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      <Link to={`/product/${product.slug}`} className="block">
        {/* Product Image */}
        <div className="relative aspect-w-1 aspect-h-1 bg-gray-200 overflow-hidden">
          <LazyImage
            src={product.images[0]?.url || '/images/placeholder-product.jpg'}
            alt={product.name}
            className="w-full h-64 object-cover group-hover:scale-105 smooth-transition image-stable"
            placeholder="/images/placeholder-product.jpg"
          />
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.featured && (
              <span className="bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">
                Featured
              </span>
            )}
            {product.bestSeller && (
              <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                Best Seller
              </span>
            )}
            {discountPercentage > 0 && (
              <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
                -{discountPercentage}%
              </span>
            )}
          </div>

          {/* Stock Status */}
          {isOutOfStock && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <span className="bg-red-600 text-white font-bold px-3 py-1 rounded">
                Out of Stock
              </span>
            </div>
          )}

          {/* Quick Add to Cart Button */}
          {!isOutOfStock && (
            <button
              onClick={handleAddToCart}
              className="absolute bottom-2 right-2 bg-primary-600 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-primary-700"
              title="Add to Cart"
            >
              <ShoppingCartIcon className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4">
          {/* Category and Cannabinoid */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-500 uppercase tracking-wide">
              {product.cannabinoid}
            </span>
            {product.strain && (
              <span className="text-xs text-gray-500">
                {product.strain}
              </span>
            )}
          </div>

          {/* Product Name */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 smooth-transition text-stable">
            {product.name}
          </h3>

          {/* Short Description */}
          {product.shortDescription && (
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
              {product.shortDescription}
            </p>
          )}

          {/* Rating */}
          {product.averageRating > 0 && (
            <div className="flex items-center mb-3">
              <div className="flex items-center">
                {renderStars(product.averageRating)}
              </div>
              <span className="ml-2 text-sm text-gray-600">
                ({product.reviewCount})
              </span>
            </div>
          )}

          {/* Price */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-xl font-bold text-gray-900 text-stable">
                ${product.price.toFixed(2)}
              </span>
              {product.comparePrice && product.comparePrice > product.price && (
                <span className="text-sm text-gray-500 line-through">
                  ${product.comparePrice.toFixed(2)}
                </span>
              )}
            </div>

            {/* Stock Indicator */}
            {isLowStock && (
              <span className="text-xs text-orange-600 font-medium">
                Only {product.quantity} left
              </span>
            )}
          </div>

          {/* Effects */}
          {product.effects && product.effects.length > 0 && (
            <div className="mt-3">
              <div className="flex flex-wrap gap-1">
                {product.effects.slice(0, 3).map((effect, index) => (
                  <span
                    key={index}
                    className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
                  >
                    {effect}
                  </span>
                ))}
                {product.effects.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{product.effects.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </Link>
    </div>
  );
};

export default ProductCard;
