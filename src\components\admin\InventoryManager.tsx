import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { updateProduct, fetchProducts } from '../../store/slices/productSlice';
import { addToast } from '../../store/slices/uiSlice';
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  MinusIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface InventoryManagerProps {
  productId: number;
  currentStock: number;
  lowStockThreshold?: number;
  onStockUpdate?: (newStock: number) => void;
}

const InventoryManager: React.FC<InventoryManagerProps> = ({
  productId,
  currentStock,
  lowStockThreshold = 10,
  onStockUpdate
}) => {
  const dispatch = useAppDispatch();
  const { loading } = useAppSelector((state) => state.products);
  const [stockInput, setStockInput] = useState(currentStock.toString());
  const [isEditing, setIsEditing] = useState(false);
  const [stockHistory, setStockHistory] = useState<Array<{
    date: string;
    change: number;
    reason: string;
    newStock: number;
  }>>([]);

  useEffect(() => {
    setStockInput(currentStock.toString());
  }, [currentStock]);

  const getStockStatus = (stock: number) => {
    if (stock === 0) {
      return {
        status: 'out-of-stock',
        color: 'text-red-600 bg-red-50 border-red-200',
        icon: XCircleIcon,
        label: 'Out of Stock'
      };
    } else if (stock <= lowStockThreshold) {
      return {
        status: 'low-stock',
        color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
        icon: ExclamationTriangleIcon,
        label: 'Low Stock'
      };
    } else {
      return {
        status: 'in-stock',
        color: 'text-green-600 bg-green-50 border-green-200',
        icon: CheckCircleIcon,
        label: 'In Stock'
      };
    }
  };

  const handleStockUpdate = async (newStock: number, reason: string = 'Manual adjustment') => {
    try {
      await dispatch(updateProduct({
        id: productId,
        productData: { quantity: newStock }
      })).unwrap();

      // Add to stock history
      const historyEntry = {
        date: new Date().toISOString(),
        change: newStock - currentStock,
        reason,
        newStock
      };
      setStockHistory(prev => [historyEntry, ...prev.slice(0, 9)]); // Keep last 10 entries

      dispatch(addToast({
        type: 'success',
        title: 'Stock Updated',
        message: `Stock level updated to ${newStock} units`
      }));

      if (onStockUpdate) {
        onStockUpdate(newStock);
      }

      setIsEditing(false);
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Update Failed',
        message: error.message || 'Failed to update stock level'
      }));
    }
  };

  const handleQuickAdjustment = (adjustment: number) => {
    const newStock = Math.max(0, currentStock + adjustment);
    const reason = adjustment > 0 ? `Added ${adjustment} units` : `Removed ${Math.abs(adjustment)} units`;
    handleStockUpdate(newStock, reason);
  };

  const handleInputSubmit = () => {
    const newStock = parseInt(stockInput);
    if (isNaN(newStock) || newStock < 0) {
      dispatch(addToast({
        type: 'error',
        title: 'Invalid Input',
        message: 'Please enter a valid stock quantity'
      }));
      setStockInput(currentStock.toString());
      return;
    }

    if (newStock !== currentStock) {
      handleStockUpdate(newStock, 'Manual adjustment');
    } else {
      setIsEditing(false);
    }
  };

  const stockStatus = getStockStatus(currentStock);
  const StatusIcon = stockStatus.icon;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Inventory Management</h3>
        <button
          onClick={() => dispatch(fetchProducts({ id: productId }))}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          title="Refresh stock data"
        >
          <ArrowPathIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Current Stock Display */}
      <div className={`border rounded-lg p-4 mb-4 ${stockStatus.color}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <StatusIcon className="h-6 w-6 mr-2" />
            <div>
              <div className="font-medium">{stockStatus.label}</div>
              <div className="text-sm opacity-75">
                {currentStock} units available
              </div>
            </div>
          </div>
          
          {/* Stock Input/Display */}
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <input
                  type="number"
                  value={stockInput}
                  onChange={(e) => setStockInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleInputSubmit()}
                  className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
                  min="0"
                  autoFocus
                />
                <button
                  onClick={handleInputSubmit}
                  disabled={loading}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                >
                  Save
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setStockInput(currentStock.toString());
                  }}
                  className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                >
                  Cancel
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200"
              >
                Edit Stock
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Quick Adjustments */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Quick Adjustments
        </label>
        <div className="flex space-x-2">
          <button
            onClick={() => handleQuickAdjustment(-1)}
            disabled={loading || currentStock === 0}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <MinusIcon className="h-4 w-4 mr-1" />
            -1
          </button>
          <button
            onClick={() => handleQuickAdjustment(-5)}
            disabled={loading || currentStock < 5}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <MinusIcon className="h-4 w-4 mr-1" />
            -5
          </button>
          <button
            onClick={() => handleQuickAdjustment(1)}
            disabled={loading}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            +1
          </button>
          <button
            onClick={() => handleQuickAdjustment(5)}
            disabled={loading}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            +5
          </button>
          <button
            onClick={() => handleQuickAdjustment(10)}
            disabled={loading}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            +10
          </button>
        </div>
      </div>

      {/* Stock History */}
      {stockHistory.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Recent Changes
          </label>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {stockHistory.map((entry, index) => (
              <div key={index} className="flex items-center justify-between text-sm bg-gray-50 rounded p-2">
                <div>
                  <span className="font-medium">{entry.reason}</span>
                  <span className="text-gray-500 ml-2">
                    {new Date(entry.date).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className={`font-medium ${
                    entry.change > 0 ? 'text-green-600' : entry.change < 0 ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {entry.change > 0 ? '+' : ''}{entry.change}
                  </span>
                  <span className="text-gray-500 ml-2">
                    → {entry.newStock}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Low Stock Warning */}
      {currentStock > 0 && currentStock <= lowStockThreshold && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Low Stock Alert
              </h3>
              <div className="mt-1 text-sm text-yellow-700">
                This product is running low. Consider restocking soon to avoid stockouts.
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryManager;
