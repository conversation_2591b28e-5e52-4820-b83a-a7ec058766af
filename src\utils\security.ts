/**
 * Security utilities for the Nirvana Organics e-commerce application
 */

/**
 * Input sanitization utilities
 */
export const sanitize = {
  /**
   * Sanitize HTML input to prevent XSS attacks
   */
  html: (input: string): string => {
    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
  },

  /**
   * Sanitize email input
   */
  email: (email: string): string => {
    return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
  },

  /**
   * Sanitize phone number input
   */
  phone: (phone: string): string => {
    return phone.replace(/[^\d+()-\s]/g, '');
  },

  /**
   * Sanitize search query
   */
  searchQuery: (query: string): string => {
    return query.trim().replace(/[<>]/g, '').substring(0, 100);
  },

  /**
   * Sanitize URL parameters
   */
  urlParam: (param: string): string => {
    return encodeURIComponent(param.trim());
  }
};

/**
 * Input validation utilities
 */
export const validate = {
  /**
   * Validate email format
   */
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  },

  /**
   * Validate password strength
   */
  password: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  /**
   * Validate phone number
   */
  phone: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s()-]{10,15}$/;
    return phoneRegex.test(phone);
  },

  /**
   * Validate credit card number (basic Luhn algorithm)
   */
  creditCard: (cardNumber: string): boolean => {
    const num = cardNumber.replace(/\D/g, '');
    if (num.length < 13 || num.length > 19) return false;

    let sum = 0;
    let isEven = false;

    for (let i = num.length - 1; i >= 0; i--) {
      let digit = parseInt(num[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  },

  /**
   * Validate ZIP code
   */
  zipCode: (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  }
};

/**
 * Security headers utilities
 */
export const securityHeaders = {
  /**
   * Generate Content Security Policy
   */
  generateCSP: (): string => {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://js.square-web-payments-sdk.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https://api.nirvanaorganics.com",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'"
    ].join('; ');
  },

  /**
   * Check if running on HTTPS
   */
  isSecure: (): boolean => {
    return window.location.protocol === 'https:' || window.location.hostname === 'localhost';
  }
};

/**
 * Token management utilities
 */
export const tokenManager = {
  /**
   * Store JWT token securely
   */
  store: (token: string): void => {
    if (typeof Storage !== 'undefined') {
      localStorage.setItem('token', token);
    }
  },

  /**
   * Retrieve JWT token
   */
  get: (): string | null => {
    if (typeof Storage !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  },

  /**
   * Remove JWT token
   */
  remove: (): void => {
    if (typeof Storage !== 'undefined') {
      localStorage.removeItem('token');
    }
  },

  /**
   * Check if token is expired
   */
  isExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  },

  /**
   * Decode JWT token payload
   */
  decode: (token: string): any => {
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch {
      return null;
    }
  }
};

/**
 * Rate limiting utilities (client-side)
 */
export const rateLimiter = {
  requests: new Map<string, number[]>(),

  /**
   * Check if request is allowed based on rate limit
   */
  isAllowed: (key: string, maxRequests: number = 10, windowMs: number = 60000): boolean => {
    const now = Date.now();
    const requests = rateLimiter.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }

    // Add current request
    validRequests.push(now);
    rateLimiter.requests.set(key, validRequests);
    
    return true;
  },

  /**
   * Clear rate limit data for a key
   */
  clear: (key: string): void => {
    rateLimiter.requests.delete(key);
  }
};

/**
 * CSRF protection utilities
 */
export const csrfProtection = {
  /**
   * Generate CSRF token
   */
  generateToken: (): string => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  },

  /**
   * Store CSRF token
   */
  storeToken: (token: string): void => {
    if (typeof Storage !== 'undefined') {
      sessionStorage.setItem('csrf-token', token);
    }
  },

  /**
   * Get CSRF token
   */
  getToken: (): string | null => {
    if (typeof Storage !== 'undefined') {
      return sessionStorage.getItem('csrf-token');
    }
    return null;
  }
};

/**
 * Secure random utilities
 */
export const secureRandom = {
  /**
   * Generate secure random string
   */
  string: (length: number = 32): string => {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  },

  /**
   * Generate secure random number
   */
  number: (min: number = 0, max: number = 1000000): number => {
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    return min + (array[0] % (max - min + 1));
  }
};

/**
 * Privacy utilities
 */
export const privacy = {
  /**
   * Mask sensitive data for logging
   */
  maskEmail: (email: string): string => {
    const [username, domain] = email.split('@');
    if (username.length <= 2) return email;
    return `${username[0]}${'*'.repeat(username.length - 2)}${username[username.length - 1]}@${domain}`;
  },

  /**
   * Mask credit card number
   */
  maskCreditCard: (cardNumber: string): string => {
    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.length < 4) return cardNumber;
    return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4);
  },

  /**
   * Mask phone number
   */
  maskPhone: (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length < 4) return phone;
    return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4);
  }
};

export default {
  sanitize,
  validate,
  securityHeaders,
  tokenManager,
  rateLimiter,
  csrfProtection,
  secureRandom,
  privacy
};
