/**
 * Mobile Testing Utilities
 * Provides utilities for testing mobile responsiveness and debugging mobile issues
 */

export interface MobileTestResult {
  screenSize: string;
  viewport: { width: number; height: number };
  hasHorizontalScroll: boolean;
  whatsappWidgetVisible: boolean;
  whatsappWidgetPosition: { bottom: number; right: number } | null;
  touchTargetsValid: boolean;
  fontSizeAppropriate: boolean;
  issues: string[];
}

/**
 * Test mobile responsiveness at different screen sizes
 */
export const testMobileResponsiveness = (): MobileTestResult[] => {
  const testSizes = [
    { name: '320px (iPhone SE)', width: 320, height: 568 },
    { name: '375px (iPhone 6/7/8)', width: 375, height: 667 },
    { name: '414px (iPhone 6/7/8 Plus)', width: 414, height: 736 },
    { name: '768px (iPad)', width: 768, height: 1024 }
  ];

  const results: MobileTestResult[] = [];

  testSizes.forEach(size => {
    // Simulate viewport size
    const originalWidth = window.innerWidth;
    const originalHeight = window.innerHeight;

    // Note: In a real test environment, you'd use tools like Puppeteer or Playwright
    // This is a simplified version for runtime testing
    
    const result: MobileTestResult = {
      screenSize: size.name,
      viewport: { width: size.width, height: size.height },
      hasHorizontalScroll: checkHorizontalScroll(),
      whatsappWidgetVisible: checkWhatsAppWidget(),
      whatsappWidgetPosition: getWhatsAppWidgetPosition(),
      touchTargetsValid: checkTouchTargets(),
      fontSizeAppropriate: checkFontSizes(size.width),
      issues: []
    };

    // Collect issues
    if (result.hasHorizontalScroll) {
      result.issues.push('Horizontal scrolling detected');
    }
    if (!result.whatsappWidgetVisible) {
      result.issues.push('WhatsApp widget not visible');
    }
    if (!result.touchTargetsValid) {
      result.issues.push('Touch targets too small');
    }
    if (!result.fontSizeAppropriate) {
      result.issues.push('Font sizes not appropriate for screen size');
    }

    results.push(result);
  });

  return results;
};

/**
 * Check if there's horizontal scrolling
 */
export const checkHorizontalScroll = (): boolean => {
  return document.documentElement.scrollWidth > window.innerWidth;
};

/**
 * Check if WhatsApp widget is visible and properly positioned
 */
export const checkWhatsAppWidget = (): boolean => {
  const widget = document.querySelector('.whatsapp-widget-container');
  if (!widget) return false;

  const rect = widget.getBoundingClientRect();
  const isVisible = rect.width > 0 && rect.height > 0;
  const isInViewport = rect.bottom <= window.innerHeight && rect.right <= window.innerWidth;

  return isVisible && isInViewport;
};

/**
 * Get WhatsApp widget position
 */
export const getWhatsAppWidgetPosition = (): { bottom: number; right: number } | null => {
  const widget = document.querySelector('.whatsapp-widget-container') as HTMLElement;
  if (!widget) return null;

  const styles = window.getComputedStyle(widget);
  return {
    bottom: parseInt(styles.bottom) || 0,
    right: parseInt(styles.right) || 0
  };
};

/**
 * Check if touch targets are appropriately sized (minimum 44px)
 */
export const checkTouchTargets = (): boolean => {
  const touchElements = document.querySelectorAll('button, a, input, [role="button"]');
  const minSize = 44;

  for (const element of touchElements) {
    const rect = element.getBoundingClientRect();
    if (rect.width < minSize || rect.height < minSize) {
      // Check if element has touch-target class or appropriate padding
      if (!element.classList.contains('touch-target')) {
        return false;
      }
    }
  }

  return true;
};

/**
 * Check if font sizes are appropriate for the screen size
 */
export const checkFontSizes = (screenWidth: number): boolean => {
  const body = document.body;
  const computedStyle = window.getComputedStyle(body);
  const fontSize = parseInt(computedStyle.fontSize);

  // Minimum font size recommendations
  if (screenWidth <= 320 && fontSize < 14) return false;
  if (screenWidth <= 375 && fontSize < 14) return false;
  if (screenWidth <= 414 && fontSize < 15) return false;
  if (screenWidth <= 768 && fontSize < 16) return false;

  return true;
};

/**
 * Generate a mobile testing report
 */
export const generateMobileTestReport = (): string => {
  const results = testMobileResponsiveness();
  let report = '=== MOBILE RESPONSIVENESS TEST REPORT ===\n\n';

  results.forEach(result => {
    report += `Screen Size: ${result.screenSize}\n`;
    report += `Viewport: ${result.viewport.width}x${result.viewport.height}\n`;
    report += `Horizontal Scroll: ${result.hasHorizontalScroll ? 'FAIL' : 'PASS'}\n`;
    report += `WhatsApp Widget: ${result.whatsappWidgetVisible ? 'PASS' : 'FAIL'}\n`;
    report += `Touch Targets: ${result.touchTargetsValid ? 'PASS' : 'FAIL'}\n`;
    report += `Font Sizes: ${result.fontSizeAppropriate ? 'PASS' : 'FAIL'}\n`;
    
    if (result.whatsappWidgetPosition) {
      report += `Widget Position: bottom=${result.whatsappWidgetPosition.bottom}px, right=${result.whatsappWidgetPosition.right}px\n`;
    }

    if (result.issues.length > 0) {
      report += `Issues: ${result.issues.join(', ')}\n`;
    }

    report += '\n';
  });

  return report;
};

/**
 * Debug mobile layout issues
 */
export const debugMobileLayout = (): void => {
  console.group('Mobile Layout Debug');
  
  // Check viewport
  console.log('Viewport:', {
    width: window.innerWidth,
    height: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio
  });

  // Check for horizontal scroll
  console.log('Horizontal Scroll:', checkHorizontalScroll());

  // Check WhatsApp widget
  console.log('WhatsApp Widget:', {
    visible: checkWhatsAppWidget(),
    position: getWhatsAppWidgetPosition()
  });

  // Check for elements causing overflow
  const elements = document.querySelectorAll('*');
  const overflowElements: Element[] = [];

  elements.forEach(element => {
    const rect = element.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      overflowElements.push(element);
    }
  });

  if (overflowElements.length > 0) {
    console.warn('Elements causing horizontal overflow:', overflowElements);
  }

  // Check touch targets
  console.log('Touch Targets Valid:', checkTouchTargets());

  console.groupEnd();
};

/**
 * Auto-fix common mobile issues
 */
export const autoFixMobileIssues = (): void => {
  // Fix horizontal scroll by adding overflow-x: hidden to problematic elements
  const body = document.body;
  const html = document.documentElement;
  
  body.style.overflowX = 'hidden';
  html.style.overflowX = 'hidden';

  // Ensure all containers have proper box-sizing
  const containers = document.querySelectorAll('.container, .mx-auto');
  containers.forEach(container => {
    (container as HTMLElement).style.boxSizing = 'border-box';
    (container as HTMLElement).style.maxWidth = '100vw';
  });

  // Fix small touch targets
  const smallTargets = document.querySelectorAll('button, a, input');
  smallTargets.forEach(target => {
    const rect = target.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      (target as HTMLElement).style.minHeight = '44px';
      (target as HTMLElement).style.minWidth = '44px';
      (target as HTMLElement).style.padding = '12px';
    }
  });

  console.log('Mobile issues auto-fixed');
};

// Export for global access in development
if (typeof window !== 'undefined') {
  (window as any).mobileTestingUtils = {
    testMobileResponsiveness,
    generateMobileTestReport,
    debugMobileLayout,
    autoFixMobileIssues,
    checkHorizontalScroll,
    checkWhatsAppWidget
  };
}
