import React from 'react';
import { ShieldCheckIcon, EyeIcon, LockClosedIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

const Privacy: React.FC = () => {
  const lastUpdated = 'December 15, 2024';

  const sections = [
    {
      icon: DocumentTextIcon,
      title: 'Information We Collect',
      content: [
        'Personal information you provide (name, email, phone, address)',
        'Payment information (processed securely through our payment partners)',
        'Order history and preferences',
        'Website usage data and analytics',
        'Device information and IP addresses',
        'Cookies and similar tracking technologies'
      ]
    },
    {
      icon: EyeIcon,
      title: 'How We Use Your Information',
      content: [
        'Process and fulfill your orders',
        'Provide customer support and respond to inquiries',
        'Send order confirmations and shipping updates',
        'Improve our products and services',
        'Comply with legal obligations',
        'Prevent fraud and ensure security'
      ]
    },
    {
      icon: LockClosedIcon,
      title: 'Information Sharing',
      content: [
        'We never sell your personal information to third parties',
        'Shipping partners (only delivery information)',
        'Payment processors (only transaction data)',
        'Legal compliance when required by law',
        'Service providers who help operate our business',
        'Business transfers (mergers, acquisitions)'
      ]
    },
    {
      icon: ShieldCheckIcon,
      title: 'Data Security',
      content: [
        'SSL encryption for all data transmission',
        'Secure payment processing (PCI DSS compliant)',
        'Regular security audits and updates',
        'Limited access to personal information',
        'Data backup and recovery procedures',
        'Employee training on data protection'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Privacy Policy
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              Your privacy is important to us. Learn how we collect, use, and protect your information.
            </p>
            <p className="text-primary-200 mt-4">
              Last updated: {lastUpdated}
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Introduction */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Introduction</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              At Nirvana Organics, we are committed to protecting your privacy and ensuring the security 
              of your personal information. This Privacy Policy explains how we collect, use, disclose, 
              and safeguard your information when you visit our website or make a purchase.
            </p>
            <p className="text-gray-600 leading-relaxed">
              By using our website and services, you agree to the collection and use of information 
              in accordance with this policy. If you do not agree with our policies and practices, 
              please do not use our services.
            </p>
          </div>

          {/* Main Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {sections.map((section, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-primary-100 text-primary-600 rounded-lg flex items-center justify-center mr-3">
                    <section.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{section.title}</h3>
                </div>
                <ul className="space-y-2">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-gray-600 text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Additional Sections */}
          <div className="space-y-8">
            {/* Your Rights */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Rights</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Access & Control</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Access your personal information</li>
                    <li>• Update or correct your data</li>
                    <li>• Delete your account and data</li>
                    <li>• Export your data</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Communication</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Opt-out of marketing emails</li>
                    <li>• Manage cookie preferences</li>
                    <li>• Request information about data use</li>
                    <li>• File privacy complaints</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Cookies */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Cookies and Tracking</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                We use cookies and similar technologies to enhance your browsing experience, 
                analyze website traffic, and personalize content. You can control cookie 
                settings through your browser preferences.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Essential Cookies</h4>
                  <p className="text-sm text-gray-600">Required for website functionality</p>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Analytics Cookies</h4>
                  <p className="text-sm text-gray-600">Help us improve our website</p>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Marketing Cookies</h4>
                  <p className="text-sm text-gray-600">Personalize your experience</p>
                </div>
              </div>
            </div>

            {/* Data Retention */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Data Retention</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                We retain your personal information only as long as necessary to fulfill the 
                purposes outlined in this privacy policy, comply with legal obligations, 
                resolve disputes, and enforce our agreements.
              </p>
              <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
                <p className="text-primary-800 text-sm">
                  <strong>Typical retention periods:</strong> Account data (until deletion requested), 
                  Order history (7 years for tax purposes), Marketing data (until opt-out), 
                  Website analytics (26 months).
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Us</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                If you have questions about this Privacy Policy or our data practices, 
                please contact us:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Privacy Officer</h4>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-gray-600">+1 (555) 123-4567</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Mailing Address</h4>
                  <p className="text-gray-600">
                    Nirvana Organics<br />
                    123 Wellness Way<br />
                    Denver, CO 80202
                  </p>
                </div>
              </div>
            </div>

            {/* Updates */}
            <div className="bg-primary-50 border border-primary-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-primary-900 mb-2">Policy Updates</h3>
              <p className="text-primary-800 text-sm">
                We may update this Privacy Policy from time to time. We will notify you of any 
                material changes by posting the new policy on this page and updating the 
                "Last updated" date. Your continued use of our services after any changes 
                constitutes acceptance of the updated policy.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Privacy;
