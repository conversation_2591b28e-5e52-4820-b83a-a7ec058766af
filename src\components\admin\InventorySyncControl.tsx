import React, { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import LoadingSpinner from '../common/LoadingSpinner';

interface SyncStatus {
  status: 'synced' | 'syncing' | 'error' | 'never';
  lastSync: string | null;
  nextSync: string | null;
  syncedProducts: number;
  totalProducts: number;
  errors: string[];
  autoSyncEnabled: boolean;
  syncInterval: number; // in minutes
}

interface InventorySyncControlProps {
  onSyncComplete?: () => void;
}

const InventorySyncControl: React.FC<InventorySyncControlProps> = ({ onSyncComplete }) => {
  const dispatch = useAppDispatch();
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    status: 'synced',
    lastSync: new Date().toISOString(),
    nextSync: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
    syncedProducts: 156,
    totalProducts: 156,
    errors: [],
    autoSyncEnabled: true,
    syncInterval: 5
  });
  const [isManualSyncing, setIsManualSyncing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    // Set up auto-sync interval if enabled
    if (syncStatus.autoSyncEnabled && syncStatus.syncInterval > 0) {
      const interval = setInterval(() => {
        performSync(true);
      }, syncStatus.syncInterval * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [syncStatus.autoSyncEnabled, syncStatus.syncInterval]);

  const performSync = async (isAutoSync = false) => {
    try {
      if (!isAutoSync) {
        setIsManualSyncing(true);
      }

      setSyncStatus(prev => ({
        ...prev,
        status: 'syncing',
        errors: []
      }));

      // Simulate API call to sync with Square
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Mock successful sync
      const now = new Date().toISOString();
      const nextSync = new Date(Date.now() + syncStatus.syncInterval * 60 * 1000).toISOString();

      setSyncStatus(prev => ({
        ...prev,
        status: 'synced',
        lastSync: now,
        nextSync: syncStatus.autoSyncEnabled ? nextSync : null,
        syncedProducts: prev.totalProducts,
        errors: []
      }));

      dispatch(addToast({
        type: 'success',
        message: `Inventory sync completed successfully. ${syncStatus.totalProducts} products updated.`
      }));

      onSyncComplete?.();

    } catch (error: any) {
      setSyncStatus(prev => ({
        ...prev,
        status: 'error',
        errors: [error.message || 'Sync failed']
      }));

      dispatch(addToast({
        type: 'error',
        message: 'Inventory sync failed. Please try again.'
      }));
    } finally {
      if (!isAutoSync) {
        setIsManualSyncing(false);
      }
    }
  };

  const updateSyncSettings = (settings: Partial<Pick<SyncStatus, 'autoSyncEnabled' | 'syncInterval'>>) => {
    setSyncStatus(prev => ({
      ...prev,
      ...settings,
      nextSync: settings.autoSyncEnabled !== false && (settings.syncInterval || prev.syncInterval) > 0
        ? new Date(Date.now() + (settings.syncInterval || prev.syncInterval) * 60 * 1000).toISOString()
        : null
    }));

    dispatch(addToast({
      type: 'success',
      message: 'Sync settings updated successfully'
    }));
  };

  const getStatusIcon = () => {
    switch (syncStatus.status) {
      case 'synced':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'syncing':
        return <ArrowPathIcon className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (syncStatus.status) {
      case 'synced':
        return 'text-green-600';
      case 'syncing':
        return 'text-blue-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  const formatTime = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <h3 className="text-lg font-semibold text-gray-900">Square Inventory Sync</h3>
            </div>
            <span className={`text-sm font-medium capitalize ${getStatusColor()}`}>
              {syncStatus.status}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              title="Sync Settings"
            >
              <Cog6ToothIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => performSync(false)}
              disabled={isManualSyncing || syncStatus.status === 'syncing'}
              className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isManualSyncing ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  Syncing...
                </>
              ) : (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2" />
                  Sync Now
                </>
              )}
            </button>
          </div>
        </div>

        {/* Sync Status Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">Last Sync</div>
            <div className="text-lg font-semibold text-gray-900">
              {formatTime(syncStatus.lastSync)}
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">Products Synced</div>
            <div className="text-lg font-semibold text-gray-900">
              {syncStatus.syncedProducts} / {syncStatus.totalProducts}
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">Next Auto Sync</div>
            <div className="text-lg font-semibold text-gray-900">
              {syncStatus.autoSyncEnabled && syncStatus.nextSync 
                ? formatTime(syncStatus.nextSync)
                : 'Disabled'
              }
            </div>
          </div>
        </div>

        {/* Sync Progress */}
        {syncStatus.status === 'syncing' && (
          <div className="mb-6">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Syncing products...</span>
              <span>{Math.round((syncStatus.syncedProducts / syncStatus.totalProducts) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(syncStatus.syncedProducts / syncStatus.totalProducts) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Errors */}
        {syncStatus.errors.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center mb-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
              <h4 className="text-sm font-medium text-red-800">Sync Errors</h4>
            </div>
            <ul className="text-sm text-red-700 space-y-1">
              {syncStatus.errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Settings Panel */}
        {showSettings && (
          <div className="border-t border-gray-200 pt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Sync Settings</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Auto Sync</label>
                  <p className="text-xs text-gray-500">Automatically sync inventory at regular intervals</p>
                </div>
                <button
                  onClick={() => updateSyncSettings({ autoSyncEnabled: !syncStatus.autoSyncEnabled })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    syncStatus.autoSyncEnabled ? 'bg-primary-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      syncStatus.autoSyncEnabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {syncStatus.autoSyncEnabled && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sync Interval (minutes)
                  </label>
                  <select
                    value={syncStatus.syncInterval}
                    onChange={(e) => updateSyncSettings({ syncInterval: parseInt(e.target.value) })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value={1}>1 minute</option>
                    <option value={5}>5 minutes</option>
                    <option value={15}>15 minutes</option>
                    <option value={30}>30 minutes</option>
                    <option value={60}>1 hour</option>
                  </select>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InventorySyncControl;
