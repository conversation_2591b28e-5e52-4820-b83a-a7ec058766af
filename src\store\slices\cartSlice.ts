import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Cart, CartItem } from '../../types';
import { cartAPI } from '../../services/api';

interface CartState {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
  itemCount: number;
  total: number;
}

const initialState: CartState = {
  cart: null,
  loading: false,
  error: null,
  itemCount: 0,
  total: 0,
};

// Async thunks
export const fetchCart = createAsyncThunk(
  'cart/fetchCart',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartAPI.getCart();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch cart');
    }
  }
);

export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async ({ productId, variant, quantity }: { productId: string; variant?: any; quantity?: number }, { rejectWithValue }) => {
    try {
      const response = await cartAPI.addToCart(productId, variant, quantity);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add item to cart');
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateCartItem',
  async ({ itemId, quantity }: { itemId: string; quantity: number }, { rejectWithValue }) => {
    try {
      const response = await cartAPI.updateCartItem(itemId, quantity);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update cart item');
    }
  }
);

export const removeFromCart = createAsyncThunk(
  'cart/removeFromCart',
  async (itemId: string, { rejectWithValue }) => {
    try {
      const response = await cartAPI.removeFromCart(itemId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to remove item from cart');
    }
  }
);

export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartAPI.clearCart();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to clear cart');
    }
  }
);

export const applyCoupon = createAsyncThunk(
  'cart/applyCoupon',
  async (couponCode: string, { rejectWithValue }) => {
    try {
      const response = await cartAPI.applyCoupon(couponCode);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to apply coupon');
    }
  }
);

export const removeCoupon = createAsyncThunk(
  'cart/removeCoupon',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartAPI.removeCoupon();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to remove coupon');
    }
  }
);

// Cart slice
const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateCartSummary: (state) => {
      if (state.cart) {
        state.itemCount = state.cart.items.reduce((count, item) => count + item.quantity, 0);
        state.total = state.cart.total;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch Cart
    builder
      .addCase(fetchCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.loading = false;
        state.cart = action.payload.data?.cart || null;
        state.itemCount = action.payload.data?.cart?.items?.reduce((count: number, item: CartItem) => count + item.quantity, 0) || 0;
        state.total = action.payload.data?.cart?.total || 0;
        state.error = null;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Add to Cart
    builder
      .addCase(addToCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.loading = false;
        state.cart = action.payload.data?.cart || null;
        state.itemCount = action.payload.data?.cart?.items?.reduce((count: number, item: CartItem) => count + item.quantity, 0) || 0;
        state.total = action.payload.data?.cart?.total || 0;
        state.error = null;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Update Cart Item
    builder
      .addCase(updateCartItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCartItem.fulfilled, (state, action) => {
        state.loading = false;
        state.cart = action.payload.data?.cart || null;
        state.itemCount = action.payload.data?.cart?.items?.reduce((count: number, item: CartItem) => count + item.quantity, 0) || 0;
        state.total = action.payload.data?.cart?.total || 0;
        state.error = null;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Remove from Cart
    builder
      .addCase(removeFromCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.loading = false;
        state.cart = action.payload.data?.cart || null;
        state.itemCount = action.payload.data?.cart?.items?.reduce((count: number, item: CartItem) => count + item.quantity, 0) || 0;
        state.total = action.payload.data?.cart?.total || 0;
        state.error = null;
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Clear Cart
    builder
      .addCase(clearCart.fulfilled, (state, action) => {
        state.cart = action.payload.data?.cart || null;
        state.itemCount = 0;
        state.total = 0;
        state.error = null;
      });

    // Apply Coupon
    builder
      .addCase(applyCoupon.fulfilled, (state, action) => {
        state.cart = action.payload.data?.cart || null;
        state.total = action.payload.data?.cart?.total || 0;
        state.error = null;
      })
      .addCase(applyCoupon.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Remove Coupon
    builder
      .addCase(removeCoupon.fulfilled, (state, action) => {
        state.cart = action.payload.data?.cart || null;
        state.total = action.payload.data?.cart?.total || 0;
        state.error = null;
      });
  },
});

export const { clearError, updateCartSummary } = cartSlice.actions;
export default cartSlice.reducer;
