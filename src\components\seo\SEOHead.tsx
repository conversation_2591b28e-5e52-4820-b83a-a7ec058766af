import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  twitterCard?: 'summary' | 'summary_large_image';
  structuredData?: object;
  noIndex?: boolean;
  noFollow?: boolean;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'Nirvana Organics - Premium Hemp-Derived Cannabis Products',
  description = 'Discover premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide.',
  keywords = [
    'hemp products',
    'cannabis',
    'CBD',
    'THC-A',
    'Delta-8',
    'Delta-9',
    'hemp flowers',
    'cannabis chocolates',
    'pre-rolls',
    'diamond sauce',
    'vapes',
    'legal cannabis',
    'lab tested',
    'organic hemp'
  ],
  canonicalUrl,
  ogImage = '/images/og-image.jpg',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  structuredData,
  noIndex = false,
  noFollow = false
}) => {
  const siteUrl = 'https://shopnirvanaorganics.com';
  const fullTitle = title.includes('Nirvana Organics') ? title : `${title} | Nirvana Organics`;
  const fullCanonicalUrl = canonicalUrl ? `${siteUrl}${canonicalUrl}` : siteUrl;
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${siteUrl}${ogImage}`;

  const robotsContent = [
    noIndex ? 'noindex' : 'index',
    noFollow ? 'nofollow' : 'follow'
  ].join(', ');

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="robots" content={robotsContent} />
      <link rel="canonical" href={fullCanonicalUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Nirvana Organics" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      <meta name="twitter:site" content="@nirvanaorganics" />
      <meta name="twitter:creator" content="@nirvanaorganics" />

      {/* Additional SEO Meta Tags */}
      <meta name="author" content="Nirvana Organics" />
      <meta name="publisher" content="Nirvana Organics" />
      <meta name="copyright" content="© 2024 Nirvana Organics. All rights reserved." />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />

      {/* Mobile & Responsive */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />

      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="48x48" href="/favicon-48x48.png" />

      {/* Apple Touch Icons */}
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon-180x180.png" />

      {/* Android/PWA Icons */}
      <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
      <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />

      {/* Microsoft Tiles */}
      <meta name="msapplication-TileImage" content="/mstile-144x144.png" />
      <meta name="msapplication-TileColor" content="#16a34a" />
      <meta name="msapplication-config" content="/browserconfig.xml" />

      {/* Theme Colors */}
      <meta name="theme-color" content="#16a34a" />
      <meta name="theme-color" media="(prefers-color-scheme: light)" content="#16a34a" />
      <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#15803d" />

      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />

      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}

      {/* Business Schema */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Nirvana Organics",
          "url": siteUrl,
          "logo": `${siteUrl}/Nirvana_logo.png`,
          "description": "Premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide.",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "123 Wellness Way",
            "addressLocality": "Denver",
            "addressRegion": "CO",
            "postalCode": "80202",
            "addressCountry": "US"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "******-123-4567",
            "contactType": "customer service",
            "email": "<EMAIL>",
            "availableLanguage": "English"
          },
          "sameAs": [
            "https://facebook.com/nirvanaorganics",
            "https://instagram.com/nirvanaorganics",
            "https://twitter.com/nirvanaorganics"
          ]
        })}
      </script>

      {/* Website Schema */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "Nirvana Organics",
          "url": siteUrl,
          "description": "Premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes.",
          "publisher": {
            "@type": "Organization",
            "name": "Nirvana Organics"
          },
          "potentialAction": {
            "@type": "SearchAction",
            "target": `${siteUrl}/shop?search={search_term_string}`,
            "query-input": "required name=search_term_string"
          }
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
