#!/usr/bin/env node

/**
 * Dependency Fix Script
 * Fixes React version conflicts and ensures all dependencies are compatible
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔧 Fixing Dependency Conflicts...\n');

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

function execCommand(command, description) {
  log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed`, 'success');
    return true;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'error');
    return false;
  }
}

// Step 1: Check current package.json
function checkPackageJson() {
  log('🔍 Checking package.json for conflicts...', 'info');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Check for old react-google-login
    if (packageJson.dependencies['react-google-login']) {
      log('⚠️  Found react-google-login (incompatible with React 18)', 'warning');
      return false;
    } else {
      log('✅ react-google-login properly removed', 'success');
    }
    
    // Check for modern @react-oauth/google
    if (packageJson.dependencies['@react-oauth/google']) {
      log(`✅ @react-oauth/google found: ${packageJson.dependencies['@react-oauth/google']}`, 'success');
    } else {
      log('❌ @react-oauth/google not found', 'error');
      return false;
    }
    
    // Check React version
    const reactVersion = packageJson.dependencies.react;
    if (reactVersion && reactVersion.includes('18')) {
      log(`✅ React version: ${reactVersion}`, 'success');
    } else {
      log(`⚠️  React version: ${reactVersion}`, 'warning');
    }
    
    return true;
  } catch (error) {
    log(`❌ Error reading package.json: ${error.message}`, 'error');
    return false;
  }
}

// Step 2: Clean node_modules and package-lock.json
function cleanDependencies() {
  log('🧹 Cleaning existing dependencies...', 'info');
  
  try {
    // Remove node_modules
    if (fs.existsSync('node_modules')) {
      log('   Removing node_modules...');
      fs.rmSync('node_modules', { recursive: true, force: true });
    }
    
    // Remove package-lock.json
    if (fs.existsSync('package-lock.json')) {
      log('   Removing package-lock.json...');
      fs.unlinkSync('package-lock.json');
    }
    
    // Clear npm cache
    log('   Clearing npm cache...');
    execSync('npm cache clean --force', { stdio: 'pipe' });
    
    log('✅ Dependencies cleaned', 'success');
    return true;
  } catch (error) {
    log(`❌ Failed to clean dependencies: ${error.message}`, 'error');
    return false;
  }
}

// Step 3: Install dependencies with proper resolution
function installDependencies() {
  log('📦 Installing dependencies...', 'info');
  
  // Try different installation strategies
  const strategies = [
    { command: 'npm install', description: 'Standard npm install' },
    { command: 'npm install --legacy-peer-deps', description: 'npm install with legacy peer deps' },
    { command: 'npm install --force', description: 'npm install with force flag' }
  ];
  
  for (const strategy of strategies) {
    log(`   Trying: ${strategy.description}...`);
    try {
      execSync(strategy.command, { stdio: 'inherit' });
      log(`✅ ${strategy.description} succeeded`, 'success');
      return true;
    } catch (error) {
      log(`⚠️  ${strategy.description} failed, trying next strategy...`, 'warning');
    }
  }
  
  log('❌ All installation strategies failed', 'error');
  return false;
}

// Step 4: Verify Google OAuth dependencies
function verifyGoogleOAuth() {
  log('🔍 Verifying Google OAuth setup...', 'info');
  
  try {
    // Check if @react-oauth/google is properly installed
    const googleOAuthPath = 'node_modules/@react-oauth/google/package.json';
    if (fs.existsSync(googleOAuthPath)) {
      const googleOAuthPackage = JSON.parse(fs.readFileSync(googleOAuthPath, 'utf8'));
      log(`✅ @react-oauth/google installed: ${googleOAuthPackage.version}`, 'success');
    } else {
      log('❌ @react-oauth/google not found in node_modules', 'error');
      return false;
    }
    
    // Check if google-auth-library is available (backend dependency)
    try {
      require.resolve('google-auth-library');
      log('✅ google-auth-library available for backend', 'success');
    } catch (error) {
      log('⚠️  google-auth-library not found (backend dependency)', 'warning');
    }
    
    // Check if passport-google-oauth20 is available
    try {
      require.resolve('passport-google-oauth20');
      log('✅ passport-google-oauth20 available', 'success');
    } catch (error) {
      log('⚠️  passport-google-oauth20 not found', 'warning');
    }
    
    return true;
  } catch (error) {
    log(`❌ Error verifying Google OAuth: ${error.message}`, 'error');
    return false;
  }
}

// Step 5: Test build process
function testBuild() {
  log('🏗️  Testing build process...', 'info');

  try {
    // Test TypeScript compilation (if tsconfig exists)
    if (fs.existsSync('tsconfig.json')) {
      log('   Testing TypeScript compilation...');
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      log('✅ TypeScript compilation successful', 'success');
    } else {
      log('   ⚠️  No tsconfig.json found, skipping TypeScript test', 'warning');
    }

    // Test Vite availability
    log('   Testing Vite availability...');
    const viteVersion = execSync('npx vite --version', { encoding: 'utf8', stdio: 'pipe' }).trim();
    log(`✅ Vite available: ${viteVersion}`, 'success');

    // Test basic package.json validity
    log('   Testing package.json validity...');
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (packageJson.scripts && packageJson.scripts['build:frontend']) {
      log('✅ Build scripts configured', 'success');
    } else {
      log('⚠️  Build scripts not found', 'warning');
    }

    return true;
  } catch (error) {
    log(`⚠️  Build test failed: ${error.message}`, 'warning');
    log('   This may be normal if environment variables are not set', 'info');
    return true; // Don't fail the entire process for build test
  }
}

// Step 6: Create dependency report
function createDependencyReport() {
  log('📋 Creating dependency report...', 'info');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const report = {
      timestamp: new Date().toISOString(),
      nodeVersion: process.version,
      npmVersion: execSync('npm --version', { encoding: 'utf8' }).trim(),
      reactVersion: packageJson.dependencies.react,
      googleOAuthPackage: packageJson.dependencies['@react-oauth/google'],
      conflictResolved: !packageJson.dependencies['react-google-login'],
      dependencies: {
        react: packageJson.dependencies.react,
        'react-dom': packageJson.dependencies['react-dom'],
        '@react-oauth/google': packageJson.dependencies['@react-oauth/google'],
        'google-auth-library': packageJson.dependencies['google-auth-library'],
        'passport-google-oauth20': packageJson.dependencies['passport-google-oauth20']
      }
    };
    
    const reportPath = 'dependency-fix-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    log(`✅ Dependency report saved: ${reportPath}`, 'success');
    return true;
  } catch (error) {
    log(`⚠️  Failed to create dependency report: ${error.message}`, 'warning');
    return true; // Don't fail for this
  }
}

// Main fix function
async function fixDependencies() {
  log('🚀 Starting Dependency Fix Process', 'info');
  log('==================================\n');
  
  const steps = [
    { name: 'Check package.json', fn: checkPackageJson },
    { name: 'Clean dependencies', fn: cleanDependencies },
    { name: 'Install dependencies', fn: installDependencies },
    { name: 'Verify Google OAuth', fn: verifyGoogleOAuth },
    { name: 'Test build process', fn: testBuild },
    { name: 'Create dependency report', fn: createDependencyReport }
  ];
  
  let allPassed = true;
  
  for (const step of steps) {
    const result = step.fn();
    if (!result) {
      allPassed = false;
      if (step.name === 'Install dependencies') {
        log('❌ Critical step failed, stopping process', 'error');
        break;
      }
    }
    console.log(''); // Add spacing between steps
  }
  
  // Final summary
  log('📊 Dependency Fix Summary', 'info');
  log('=========================');
  
  if (allPassed) {
    log('🎉 All dependency conflicts resolved!', 'success');
    log('✅ React 18 compatibility ensured', 'success');
    log('✅ Google OAuth using modern @react-oauth/google', 'success');
    log('✅ No peer dependency conflicts', 'success');
    
    log('\n🚀 Next Steps:', 'info');
    log('1. Run: npm run deploy:fullstack');
    log('2. Test Google OAuth functionality');
    log('3. Verify Square payment integration');
    
  } else {
    log('⚠️  Some issues remain. Check the logs above.', 'warning');
    
    log('\n🔧 Manual Steps if Issues Persist:', 'info');
    log('1. Delete node_modules and package-lock.json manually');
    log('2. Run: npm install --legacy-peer-deps');
    log('3. Check for any remaining peer dependency warnings');
  }
  
  log('\n✅ Dependency fix process completed!', 'success');
  return allPassed;
}

// Run the fix
fixDependencies().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log(`❌ Dependency fix failed: ${error.message}`, 'error');
  process.exit(1);
});
