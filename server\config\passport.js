const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;

const LocalStrategy = require('passport-local').Strategy;
const bcrypt = require('bcryptjs');
const { User, Role } = require('../models');

/**
 * Passport Configuration for Nirvana Organics
 * Supports Google OAuth and Local authentication
 */

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id, {
      include: [{ model: Role, as: 'Role' }]
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Local Strategy for email/password authentication
passport.use(new LocalStrategy({
  usernameField: 'email',
  passwordField: 'password'
}, async (email, password, done) => {
  try {
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [{ model: Role, as: 'Role' }]
    });

    if (!user) {
      return done(null, false, { message: 'Invalid email or password' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return done(null, false, { message: 'Invalid email or password' });
    }

    if (!user.isVerified) {
      return done(null, false, { message: 'Please verify your email address before logging in' });
    }

    return done(null, user);
  } catch (error) {
    return done(error);
  }
}));

// Google OAuth Strategy
const getCallbackURL = () => {
  // Use environment-specific callback URL
  if (process.env.GOOGLE_OAUTH_CALLBACK_URL) {
    return process.env.GOOGLE_OAUTH_CALLBACK_URL;
  }

  // Default based on environment
  const baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
  return `${baseUrl}/api/auth/google/callback`;
};

passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: getCallbackURL()
}, async (accessToken, refreshToken, profile, done) => {
  try {
    // Check if user already exists with this Google ID
    let user = await User.findOne({
      where: { googleId: profile.id },
      include: [{ model: Role, as: 'Role' }]
    });

    if (user) {
      return done(null, user);
    }

    // Check if user exists with the same email
    const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
    if (email) {
      user = await User.findOne({
        where: { email: email.toLowerCase() },
        include: [{ model: Role, as: 'Role' }]
      });

      if (user) {
        // Check if this should be an admin user
        const adminEmails = [
          '<EMAIL>',
          '<EMAIL>',
          process.env.ADMIN_EMAIL
        ].filter(Boolean).map(email => email.toLowerCase());

        const isAdminEmail = adminEmails.includes(email.toLowerCase());

        // If this is an admin email but user doesn't have admin role, upgrade them
        if (isAdminEmail && user.Role && user.Role.name !== 'admin') {
          let adminRole = await Role.findOne({ where: { name: 'admin' } });
          if (!adminRole) {
            // Create admin role if it doesn't exist
            adminRole = await Role.create({
              name: 'admin',
              displayName: 'Administrator',
              description: 'System administrator with full access',
              permissions: JSON.stringify(['*']),
              priority: 100,
              isSystemRole: true
            });
          }
          user.roleId = adminRole.id;
        }

        // Link Google account to existing user
        user.googleId = profile.id;
        user.isVerified = true; // Google accounts are pre-verified
        await user.save();

        // Reload user with updated role information
        const updatedUser = await User.findByPk(user.id, {
          include: [{ model: Role, as: 'Role' }]
        });

        return done(null, updatedUser);
      }
    }

    // Create new user with Google account
    if (!email) {
      return done(new Error('No email provided by Google'), null);
    }

    // Check if this is an admin email
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      process.env.ADMIN_EMAIL
    ].filter(Boolean).map(email => email.toLowerCase());

    const isAdminEmail = adminEmails.includes(email.toLowerCase());

    // Get appropriate role based on email
    let userRole;
    if (isAdminEmail) {
      userRole = await Role.findOne({ where: { name: 'admin' } });
      if (!userRole) {
        // Create admin role if it doesn't exist
        userRole = await Role.create({
          name: 'admin',
          displayName: 'Administrator',
          description: 'System administrator with full access',
          permissions: JSON.stringify(['*']),
          priority: 100,
          isSystemRole: true
        });
      }
    } else {
      userRole = await Role.findOne({ where: { name: 'customer' } });
      if (!userRole) {
        return done(new Error('Customer role not found'), null);
      }
    }

    const newUser = await User.create({
      firstName: profile.name.givenName || (isAdminEmail ? 'Admin' : 'Google'),
      lastName: profile.name.familyName || (isAdminEmail ? 'User' : 'User'),
      email: email.toLowerCase(),
      googleId: profile.id,
      isVerified: true, // Google accounts are pre-verified
      roleId: userRole.id,
      profilePicture: profile.photos && profile.photos[0] ? profile.photos[0].value : null
    });

    // Fetch the user with role information
    const userWithRole = await User.findByPk(newUser.id, {
      include: [{ model: Role, as: 'Role' }]
    });

    return done(null, userWithRole);
  } catch (error) {
    console.error('Google OAuth error:', error);
    return done(error, null);
  }
}));



module.exports = passport;
