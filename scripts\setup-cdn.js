#!/usr/bin/env node

/**
 * CDN and Static Asset Optimization Setup
 * Configures CDN, compression, and caching for production
 */

const fs = require('fs');
const path = require('path');

console.log('🌐 Setting up CDN and Static Asset Optimization...\n');

// Configuration
const config = {
  cdnUrl: process.env.CDN_URL || 'https://cdn.shopnirvanaorganics.com',
  staticAssetsPath: '/var/www/nirvana-organics/static',
  nginxConfigPath: '/etc/nginx/sites-available/shopnirvanaorganics.com',
  compressionLevel: 6,
  cacheMaxAge: {
    images: '1y',
    css: '1y',
    js: '1y',
    fonts: '1y',
    html: '1h',
    api: '0'
  }
};

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

// Create Nginx configuration for static assets and CDN
function createNginxConfig() {
  log('⚙️  Creating Nginx configuration...');
  
  const nginxConfig = `
# Nirvana Organics - Production Configuration with CDN
# Static Asset Optimization and Caching

# Gzip Compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level ${config.compressionLevel};
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json
    image/svg+xml;

# Brotli Compression (if available)
# brotli on;
# brotli_comp_level 6;
# brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    return 301 https://shopnirvanaorganics.com$request_uri;
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/shopnirvanaorganics.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/shopnirvanaorganics.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://sandbox.web.squarecdn.com https://web.squarecdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://connect.squareup.com https://connect.squareupsandbox.com;" always;
    
    # Root directory
    root /var/www/nirvana-organics/current/dist;
    index index.html;
    
    # Enable efficient file serving
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    
    # Static Assets with Long-term Caching
    location ~* \\.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires ${config.cacheMaxAge.images};
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Optional: Serve from CDN
        # try_files $uri @cdn;
    }
    
    location ~* \\.(css|js)$ {
        expires ${config.cacheMaxAge.css};
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Optional: Serve from CDN
        # try_files $uri @cdn;
    }
    
    location ~* \\.(woff|woff2|ttf|eot)$ {
        expires ${config.cacheMaxAge.fonts};
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # API Routes - No caching
    location /api/ {
        expires ${config.cacheMaxAge.api};
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Admin Panel
    location /admin {
        try_files $uri $uri/ /admin.html;
        expires ${config.cacheMaxAge.html};
        add_header Cache-Control "no-cache, must-revalidate";
    }
    
    # Uploads and Public Assets
    location /uploads/ {
        alias /var/www/nirvana-organics/current/public/uploads/;
        expires ${config.cacheMaxAge.images};
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
    }
    
    location /assets/ {
        alias /var/www/nirvana-organics/current/public/;
        expires ${config.cacheMaxAge.images};
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
    }
    
    # Health Check
    location /health {
        proxy_pass http://localhost:5000/health;
        access_log off;
    }
    
    # Main SPA Route
    location / {
        try_files $uri $uri/ /index.html;
        expires ${config.cacheMaxAge.html};
        add_header Cache-Control "no-cache, must-revalidate";
    }
    
    # Optional: CDN Fallback
    # location @cdn {
    #     proxy_pass ${config.cdnUrl};
    #     proxy_set_header Host $host;
    #     proxy_cache_valid 200 1y;
    # }
    
    # Error Pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # Deny access to sensitive files
    location ~ /\\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(package\\.json|package-lock\\.json|\\.env.*|ecosystem\\.config\\.js)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# CDN Server (Optional)
# server {
#     listen 443 ssl http2;
#     server_name cdn.shopnirvanaorganics.com;
#     
#     ssl_certificate /etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/shopnirvanaorganics.com/privkey.pem;
#     
#     root ${config.staticAssetsPath};
#     
#     location / {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#         add_header Access-Control-Allow-Origin "*";
#     }
# }
`;

  const configPath = path.join(__dirname, '../nginx-production.conf');
  fs.writeFileSync(configPath, nginxConfig);
  log(`✅ Nginx configuration created: ${configPath}`, 'success');
  
  return configPath;
}

// Create asset optimization script
function createAssetOptimization() {
  log('🎨 Creating asset optimization script...');
  
  const optimizationScript = `#!/bin/bash

# Asset Optimization Script for Nirvana Organics
# Optimizes images, CSS, and JS for production

DIST_DIR="/var/www/nirvana-organics/current/dist"
UPLOADS_DIR="/var/www/nirvana-organics/current/public/uploads"

echo "🎨 Starting asset optimization..."

# Optimize images in dist directory
if command -v optipng &> /dev/null; then
    echo "Optimizing PNG images..."
    find "$DIST_DIR" -name "*.png" -exec optipng -o2 {} \\;
fi

if command -v jpegoptim &> /dev/null; then
    echo "Optimizing JPEG images..."
    find "$DIST_DIR" -name "*.jpg" -o -name "*.jpeg" | xargs jpegoptim --strip-all --max=85
fi

# Optimize SVG images
if command -v svgo &> /dev/null; then
    echo "Optimizing SVG images..."
    find "$DIST_DIR" -name "*.svg" -exec svgo {} \\;
fi

# Generate WebP versions of images
if command -v cwebp &> /dev/null; then
    echo "Generating WebP versions..."
    find "$DIST_DIR" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | while read img; do
        cwebp -q 85 "$img" -o "\${img%.*}.webp"
    done
fi

# Optimize uploaded images
echo "Optimizing uploaded images..."
find "$UPLOADS_DIR" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -mtime -1 | while read img; do
    if command -v jpegoptim &> /dev/null && [[ "$img" =~ \\.(jpg|jpeg)$ ]]; then
        jpegoptim --strip-all --max=85 "$img"
    fi
    if command -v optipng &> /dev/null && [[ "$img" =~ \\.png$ ]]; then
        optipng -o2 "$img"
    fi
    if command -v cwebp &> /dev/null; then
        cwebp -q 85 "$img" -o "\${img%.*}.webp"
    fi
done

echo "✅ Asset optimization completed"
`;

  const scriptPath = path.join(__dirname, '../scripts/optimize-assets.sh');
  fs.writeFileSync(scriptPath, optimizationScript);
  fs.chmodSync(scriptPath, '755');
  log(`✅ Asset optimization script created: ${scriptPath}`, 'success');
}

// Create performance monitoring script
function createPerformanceMonitoring() {
  log('📊 Creating performance monitoring...');
  
  const performanceScript = `#!/usr/bin/env node

const fs = require('fs');
const https = require('https');

const config = {
  url: 'https://shopnirvanaorganics.com',
  endpoints: [
    '/',
    '/api/health',
    '/api/products',
    '/admin'
  ],
  thresholds: {
    responseTime: 2000,
    availability: 99.9
  }
};

async function checkPerformance() {
  const results = {
    timestamp: new Date().toISOString(),
    checks: []
  };
  
  for (const endpoint of config.endpoints) {
    const start = Date.now();
    
    try {
      await new Promise((resolve, reject) => {
        const req = https.get(config.url + endpoint, (res) => {
          const responseTime = Date.now() - start;
          
          results.checks.push({
            endpoint,
            status: res.statusCode,
            responseTime,
            success: res.statusCode < 400 && responseTime < config.thresholds.responseTime
          });
          
          resolve();
        });
        
        req.on('error', (error) => {
          results.checks.push({
            endpoint,
            status: 0,
            responseTime: Date.now() - start,
            success: false,
            error: error.message
          });
          resolve();
        });
        
        req.setTimeout(10000, () => {
          req.destroy();
          results.checks.push({
            endpoint,
            status: 0,
            responseTime: Date.now() - start,
            success: false,
            error: 'Timeout'
          });
          resolve();
        });
      });
    } catch (error) {
      results.checks.push({
        endpoint,
        status: 0,
        responseTime: Date.now() - start,
        success: false,
        error: error.message
      });
    }
  }
  
  // Log results
  const logPath = '/var/log/nirvana-organics/performance.log';
  fs.appendFileSync(logPath, JSON.stringify(results) + '\\n');
  
  // Check for alerts
  const failedChecks = results.checks.filter(check => !check.success);
  if (failedChecks.length > 0) {
    console.error('Performance issues detected:', failedChecks);
  }
}

checkPerformance().catch(console.error);
`;

  const scriptPath = path.join(__dirname, '../scripts/check-performance.js');
  fs.writeFileSync(scriptPath, performanceScript);
  log(`✅ Performance monitoring script created: ${scriptPath}`, 'success');
}

// Main setup function
function main() {
  try {
    log('🌐 Setting up CDN and Static Asset Optimization...');
    
    createNginxConfig();
    createAssetOptimization();
    createPerformanceMonitoring();
    
    log('\n🎉 CDN and optimization setup completed!', 'success');
    log('\n📋 Next steps:');
    log('   1. Copy nginx-production.conf to /etc/nginx/sites-available/');
    log('   2. Enable the site: sudo ln -s /etc/nginx/sites-available/shopnirvanaorganics.com /etc/nginx/sites-enabled/');
    log('   3. Test configuration: sudo nginx -t');
    log('   4. Reload Nginx: sudo systemctl reload nginx');
    log('   5. Run asset optimization: ./scripts/optimize-assets.sh');
    log('   6. Set up performance monitoring cron job');
    
  } catch (error) {
    log(`❌ Setup failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run setup
main();
