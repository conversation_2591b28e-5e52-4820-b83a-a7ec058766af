import React from 'react';
import { CheckCircleIcon, ShieldCheckIcon, TruckIcon, HeartIcon } from '@heroicons/react/24/outline';
import SEOHead from '../components/seo/SEOHead';

const About: React.FC = () => {
  const values = [
    {
      icon: ShieldCheckIcon,
      title: 'Premium Quality',
      description: 'All our products are lab-tested for purity and potency, ensuring you receive only the highest quality hemp-derived cannabis products.'
    },
    {
      icon: CheckCircleIcon,
      title: 'Compliance First',
      description: 'We strictly adhere to all federal and state regulations, providing you with legal, safe, and reliable products you can trust.'
    },
    {
      icon: TruckIcon,
      title: 'Fast & Discreet',
      description: 'Quick, secure shipping with discreet packaging to protect your privacy. Free shipping on orders over $100.'
    },
    {
      icon: HeartIcon,
      title: 'Customer Care',
      description: 'Our dedicated team is here to help you find the perfect products for your wellness journey with personalized support.'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      bio: 'With over 10 years in the wellness industry, <PERSON> founded Nirvana Organics to make premium hemp products accessible to everyone.',
      image: '/images/team/sarah.jpg'
    },
    {
      name: 'Dr. <PERSON>',
      role: 'Chief Product Officer',
      bio: 'Dr. <PERSON> brings 15 years of pharmaceutical experience to ensure our products meet the highest quality and safety standards.',
      image: '/images/team/michael.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Head of Customer Experience',
      bio: 'Emily leads our customer service team, ensuring every customer receives exceptional support and guidance.',
      image: '/images/team/emily.jpg'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead
        title="About Us - Premium Hemp Products Company"
        description="Learn about Nirvana Organics, your trusted source for premium hemp-derived cannabis products. Discover our story, values, and commitment to quality and compliance."
        keywords={[
          'about nirvana organics',
          'hemp company',
          'cannabis company',
          'premium hemp products',
          'lab tested cannabis',
          'legal hemp products',
          'organic hemp',
          'cannabis quality',
          'hemp compliance',
          'trusted cannabis brand'
        ]}
        canonicalUrl="/about"
      />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              About Nirvana Organics
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              Your trusted partner in premium hemp-derived wellness products,
              committed to quality, compliance, and your well-being.
            </p>
          </div>
        </div>
      </div>

      {/* Our Story Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  Founded in 2020, Nirvana Organics emerged from a simple belief: everyone deserves
                  access to premium, lab-tested hemp products that enhance their quality of life.
                  What started as a small family business has grown into a trusted name in the
                  cannabis wellness industry.
                </p>
                <p>
                  We specialize in a carefully curated selection of hemp-derived products including
                  flowers, chocolates, pre-rolls, diamond sauce, and vapes. Each product is sourced
                  from licensed cultivators and manufacturers who share our commitment to excellence.
                </p>
                <p>
                  Our mission is to provide you with safe, effective, and enjoyable cannabis products
                  while maintaining the highest standards of quality, compliance, and customer service.
                </p>
              </div>
            </div>
            <div className="relative">
              <img
                src="/images/about/our-story.jpg"
                alt="Nirvana Organics Story"
                className="rounded-lg shadow-lg w-full h-96 object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/images/placeholder-about.jpg';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
            </div>
          </div>

          {/* Our Values */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Our Values</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-full mb-4">
                    <value.icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Our Team */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Meet Our Team</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-64 object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder-team.jpg';
                    }}
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                    <p className="text-primary-600 font-medium mb-3">{member.role}</p>
                    <p className="text-gray-600 text-sm leading-relaxed">{member.bio}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quality Commitment */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-16">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Our Quality Commitment</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Lab Testing & Compliance</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Third-party lab testing for all products
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Certificate of Analysis (COA) available for every batch
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Full compliance with federal and state regulations
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Delta-9 THC content below 0.3% (dry weight basis)
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Product Standards</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Organically grown hemp from licensed farms
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    No pesticides, heavy metals, or harmful solvents
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Consistent potency and purity across all batches
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    Sustainable and environmentally responsible practices
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Contact CTA */}
          <div className="text-center bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Questions About Our Products?</h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our knowledgeable team is here to help you find the perfect products for your wellness journey.
              Contact us today for personalized recommendations and expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="btn-primary px-8 py-3 text-center"
              >
                Contact Us
              </a>
              <a
                href="/shop"
                className="btn-secondary px-8 py-3 text-center"
              >
                Shop Products
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
