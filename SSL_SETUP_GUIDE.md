# SSL Certificate Setup Guide
## Nirvana Organics Production Deployment

### 🔒 Overview

This guide covers setting up SSL certificates for your production domain `shopnirvanaorganics.com` using Let's Encrypt and configuring HTTPS with proper security headers.

### 📋 Prerequisites

- Root access to your production server
- Domain name pointing to your server IP
- Nginx web server
- Port 80 and 443 open in firewall

### 🚀 Quick Setup

Run the automated SSL setup script:

```bash
# Make script executable and run
chmod +x scripts/setup-ssl-certificates.sh
sudo ./scripts/setup-ssl-certificates.sh
```

Or use the npm script:

```bash
npm run setup:ssl
```

### 📝 Manual Setup Steps

If you prefer to set up SSL manually, follow these steps:

#### 1. Install Required Packages

```bash
# Update system
sudo apt update

# Install Nginx and Certbot
sudo apt install -y nginx certbot python3-certbot-nginx

# Enable Nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

#### 2. Configure Initial Nginx Setup

Create initial HTTP configuration:

```bash
sudo nano /etc/nginx/sites-available/nirvana-organics
```

Add the following configuration:

```nginx
server {
    listen 80;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/nirvana-organics /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx
```

#### 3. Obtain SSL Certificates

```bash
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com \
    --non-interactive \
    --agree-tos \
    --email <EMAIL> \
    --redirect
```

#### 4. Configure Automatic Renewal

```bash
# Add cron job for automatic renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx" | sudo crontab -

# Test renewal
sudo certbot renew --dry-run
```

### 🔧 Enhanced Security Configuration

After SSL setup, update your Nginx configuration for enhanced security:

```nginx
# HTTP to HTTPS redirect
server {
    listen 80;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    return 301 https://$server_name$request_uri;
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/shopnirvanaorganics.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/shopnirvanaorganics.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Application proxy
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### ✅ Verification

After setup, verify your SSL configuration:

```bash
# Run verification script
npm run verify:ssl

# Manual checks
curl -I https://shopnirvanaorganics.com
openssl s_client -connect shopnirvanaorganics.com:443 -servername shopnirvanaorganics.com
```

### 🔍 Testing Tools

Use these online tools to verify your SSL setup:

1. **SSL Labs Test**: https://www.ssllabs.com/ssltest/
   - Enter: `shopnirvanaorganics.com`
   - Target grade: A or A+

2. **Security Headers**: https://securityheaders.com/
   - Check for proper security headers

3. **HSTS Preload**: https://hstspreload.org/
   - Check HSTS eligibility

### 📊 Environment Variables Update

After SSL setup, update your environment variables:

```bash
# In .env.production
FORCE_HTTPS=true
FRONTEND_URL=https://shopnirvanaorganics.com
BACKEND_URL=https://shopnirvanaorganics.com
API_BASE_URL=https://shopnirvanaorganics.com/api
CORS_ORIGIN=https://shopnirvanaorganics.com,https://www.shopnirvanaorganics.com
```

### 🚨 Troubleshooting

#### Common Issues

**Issue**: Certificate not found
```bash
# Check if certificates exist
sudo ls -la /etc/letsencrypt/live/shopnirvanaorganics.com/

# If missing, re-run certbot
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com
```

**Issue**: Nginx configuration error
```bash
# Test configuration
sudo nginx -t

# Check error logs
sudo tail -f /var/log/nginx/error.log
```

**Issue**: Domain not resolving
```bash
# Check DNS
nslookup shopnirvanaorganics.com
dig shopnirvanaorganics.com

# Check if domain points to your server IP
```

**Issue**: Firewall blocking ports
```bash
# Check firewall status
sudo ufw status

# Open required ports
sudo ufw allow 80
sudo ufw allow 443
```

### 📅 Maintenance

#### Certificate Monitoring

```bash
# Check certificate expiration
sudo certbot certificates

# Manual renewal
sudo certbot renew

# Check renewal cron job
sudo crontab -l
```

#### Security Updates

- Keep Nginx updated: `sudo apt update && sudo apt upgrade nginx`
- Monitor security advisories
- Review SSL configuration quarterly
- Update security headers as needed

### 🔐 Security Best Practices

1. **Use Strong SSL Configuration**
   - TLS 1.2+ only
   - Strong cipher suites
   - OCSP stapling enabled

2. **Implement Security Headers**
   - HSTS with preload
   - CSP (Content Security Policy)
   - X-Frame-Options
   - X-XSS-Protection

3. **Monitor and Maintain**
   - Set up certificate expiration alerts
   - Regular security scans
   - Keep software updated

4. **Backup Certificates**
   - Backup `/etc/letsencrypt/` directory
   - Document renewal process
   - Test disaster recovery

### 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review Nginx error logs: `/var/log/nginx/error.log`
3. Check Let's Encrypt logs: `/var/log/letsencrypt/letsencrypt.log`
4. Verify DNS configuration
5. Ensure firewall allows HTTPS traffic

### ✅ Final Checklist

- [ ] SSL certificates installed and valid
- [ ] HTTP to HTTPS redirect working
- [ ] Security headers configured
- [ ] Automatic renewal set up
- [ ] SSL Labs test shows A or A+ rating
- [ ] Application works over HTTPS
- [ ] Environment variables updated
- [ ] Monitoring and alerts configured
