import api from './api';

export interface CouponValidationResult {
  success: boolean;
  coupon?: {
    id: number;
    code: string;
    name: string;
    type: string;
    value: number;
  };
  discount?: number;
  message: string;
}

export interface AvailableCoupon {
  id: number;
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed_amount' | 'free_shipping' | 'buy_x_get_y';
  value: number;
  minimumOrderAmount: number;
  maximumDiscountAmount?: number;
  validUntil: string;
  isReferralCoupon: boolean;
  socialShareBonus: number;
}

export interface ReferralStats {
  totalReferrals: number;
  completedReferrals: number;
  pendingReferrals: number;
  totalRewards: number;
  referrals: Array<{
    id: string;
    referralCode: string;
    status: 'pending' | 'completed' | 'rewarded';
    referee?: {
      firstName: string;
      lastName: string;
      email: string;
    };
    order?: {
      total: number;
      createdAt: string;
    };
    rewardAmount: number;
    completedAt?: string;
  }>;
}

export interface CouponUsage {
  id: number;
  discountAmount: number;
  createdAt: string;
  coupon: {
    code: string;
    name: string;
    type: string;
    value: number;
  };
  order: {
    orderNumber: string;
    total: number;
    createdAt: string;
  };
}

export interface SocialShare {
  id: number;
  platform: string;
  shareUrl: string;
  bonusEarned: number;
  isVerified: boolean;
  verifiedAt?: string;
  createdAt: string;
  coupon?: {
    code: string;
    name: string;
  };
}

class DiscountService {
  /**
   * Validate coupon code
   */
  async validateCoupon(couponCode: string, orderData: {
    subtotal: number;
    items: Array<{
      productId: number;
      quantity: number;
      price: number;
    }>;
  }, guestEmail?: string): Promise<CouponValidationResult> {
    try {
      const response = await api.post('/api/coupons/validate', {
        couponCode,
        orderData,
        guestEmail
      });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to validate coupon'
      };
    }
  }

  /**
   * Get available coupons
   */
  async getAvailableCoupons(): Promise<{ success: boolean; data: AvailableCoupon[] }> {
    try {
      const response = await api.get('/api/coupons/available');
      return response.data;
    } catch (error) {
      console.error('Error fetching available coupons:', error);
      throw error;
    }
  }

  /**
   * Generate referral code
   */
  async generateReferralCode(): Promise<{
    success: boolean;
    data: { referralCode: string; shareUrl: string };
    message: string;
  }> {
    try {
      const response = await api.post('/api/referrals/generate');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        data: { referralCode: '', shareUrl: '' },
        message: error.response?.data?.message || 'Failed to generate referral code'
      };
    }
  }

  /**
   * Get referral statistics
   */
  async getReferralStats(): Promise<{ success: boolean; data: ReferralStats }> {
    try {
      const response = await api.get('/api/referrals/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching referral stats:', error);
      throw error;
    }
  }

  /**
   * Apply referral code
   */
  async applyReferralCode(referralCode: string): Promise<{
    success: boolean;
    data?: {
      referralCode: string;
      referrerName: string;
      discount: number;
    };
    message: string;
  }> {
    try {
      const response = await api.post('/api/referrals/apply', { referralCode });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to apply referral code'
      };
    }
  }

  /**
   * Record social media share
   */
  async recordSocialShare(platform: string, shareUrl: string, couponId?: number): Promise<{
    success: boolean;
    shareId?: number;
    message: string;
  }> {
    try {
      const response = await api.post('/api/social-shares', {
        platform,
        shareUrl,
        couponId
      });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to record social share'
      };
    }
  }

  /**
   * Get coupon usage history
   */
  async getCouponUsageHistory(page = 1, limit = 20): Promise<{
    success: boolean;
    data: {
      usages: CouponUsage[];
      pagination: {
        currentPage: number;
        totalPages: number;
        totalUsages: number;
        limit: number;
      };
    };
  }> {
    try {
      const response = await api.get('/api/coupons/usage-history', {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching coupon usage history:', error);
      throw error;
    }
  }

  /**
   * Get social sharing history
   */
  async getSocialShareHistory(page = 1, limit = 20): Promise<{
    success: boolean;
    data: {
      shares: SocialShare[];
      totalBonusEarned: number;
      pagination: {
        currentPage: number;
        totalPages: number;
        totalShares: number;
        limit: number;
      };
    };
  }> {
    try {
      const response = await api.get('/api/social-shares/history', {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching social share history:', error);
      throw error;
    }
  }

  /**
   * Get discount recommendations for cart
   */
  async getDiscountRecommendations(cartData: {
    subtotal: number;
    items: Array<{
      productId: number;
      quantity: number;
      price: number;
      categoryId?: number;
    }>;
  }): Promise<{
    success: boolean;
    data: {
      availableCoupons: AvailableCoupon[];
      recommendations: Array<{
        coupon: AvailableCoupon;
        potentialSavings: number;
        reason: string;
      }>;
    };
  }> {
    try {
      // Get available coupons
      const couponsResponse = await this.getAvailableCoupons();
      
      if (!couponsResponse.success) {
        return {
          success: false,
          data: { availableCoupons: [], recommendations: [] }
        };
      }

      const availableCoupons = couponsResponse.data;
      const recommendations = [];

      for (const coupon of availableCoupons) {
        // Skip if minimum order amount not met
        if (coupon.minimumOrderAmount > cartData.subtotal) {
          continue;
        }

        let potentialSavings = 0;
        let reason = '';

        switch (coupon.type) {
          case 'percentage':
            potentialSavings = cartData.subtotal * (coupon.value / 100);
            if (coupon.maximumDiscountAmount && potentialSavings > coupon.maximumDiscountAmount) {
              potentialSavings = coupon.maximumDiscountAmount;
            }
            reason = `Save ${coupon.value}% on your order`;
            break;

          case 'fixed_amount':
            potentialSavings = Math.min(coupon.value, cartData.subtotal);
            reason = `Save $${coupon.value.toFixed(2)} on your order`;
            break;

          case 'free_shipping':
            potentialSavings = 9.99; // Assume standard shipping cost
            reason = 'Get free shipping on your order';
            break;

          case 'buy_x_get_y':
            // This would require more complex calculation based on specific products
            reason = 'Special buy X get Y offer';
            break;
        }

        if (potentialSavings > 0) {
          recommendations.push({
            coupon,
            potentialSavings,
            reason
          });
        }
      }

      // Sort recommendations by potential savings (highest first)
      recommendations.sort((a, b) => b.potentialSavings - a.potentialSavings);

      return {
        success: true,
        data: {
          availableCoupons,
          recommendations: recommendations.slice(0, 3) // Top 3 recommendations
        }
      };

    } catch (error) {
      console.error('Error getting discount recommendations:', error);
      return {
        success: false,
        data: { availableCoupons: [], recommendations: [] }
      };
    }
  }

  /**
   * Check if user can create referral
   */
  async canCreateReferral(): Promise<{ success: boolean; canCreate: boolean; reason?: string }> {
    try {
      // This would typically check user's order history, account status, etc.
      // For now, assume all authenticated users can create referrals
      return {
        success: true,
        canCreate: true
      };
    } catch (error) {
      return {
        success: false,
        canCreate: false,
        reason: 'Unable to verify referral eligibility'
      };
    }
  }
}

export const discountService = new DiscountService();
export default discountService;
