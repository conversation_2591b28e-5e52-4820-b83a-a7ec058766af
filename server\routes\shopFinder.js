const express = require('express');
const router = express.Router();
const { authenticate, requireAdmin } = require('../middleware/auth');
const { validatePagination } = require('../middleware/validation');
const shopFinderController = require('../controllers/shopFinderController');

// Public routes
// @route   GET /api/shop-finder/locations
// @desc    Get all store locations
// @access  Public
router.get('/locations', shopFinderController.getLocations);

// @route   GET /api/shop-finder/locations/search
// @desc    Search locations by address or coordinates
// @access  Public
router.get('/locations/search', shopFinderController.searchLocations);

// @route   GET /api/shop-finder/locations/:locationId
// @desc    Get location details by ID
// @access  Public
router.get('/locations/:locationId', shopFinderController.getLocationById);

// @route   GET /api/shop-finder/locations/:locationId/inventory
// @desc    Get inventory for a specific location
// @access  Public
router.get('/locations/:locationId/inventory', shopFinderController.getLocationInventory);

// @route   GET /api/shop-finder/products/:productId/availability
// @desc    Get product availability across all locations
// @access  Public
router.get('/products/:productId/availability', shopFinderController.getProductAvailability);

// Protected routes - require authentication
// @route   POST /api/shop-finder/reservations
// @desc    Reserve product at a specific location
// @access  Private
router.post('/reservations', authenticate, shopFinderController.reserveProduct);

// @route   GET /api/shop-finder/reservations/:reservationId
// @desc    Get reservation details
// @access  Private
router.get('/reservations/:reservationId', authenticate, shopFinderController.getReservation);

// @route   DELETE /api/shop-finder/reservations/:reservationId
// @desc    Cancel a reservation
// @access  Private
router.delete('/reservations/:reservationId', authenticate, shopFinderController.cancelReservation);

module.exports = router;
