const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const emailManagementController = require('../controllers/emailManagementController');

// All routes require admin authentication
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/email-management/templates
// @desc    Get all email templates
// @access  Private (Admin)
router.get('/templates', emailManagementController.getEmailTemplates);

// @route   POST /api/admin/email-management/templates
// @desc    Create new email template
// @access  Private (Admin)
router.post('/templates', [
  body('name')
    .notEmpty()
    .withMessage('Template name is required')
    .isLength({ min: 3, max: 100 })
    .withMessage('Template name must be between 3 and 100 characters'),
  body('category')
    .isIn(['marketing', 'transactional', 'notification', 'welcome', 'abandoned_cart', 'order_confirmation'])
    .withMessage('Invalid template category'),
  body('subject')
    .notEmpty()
    .withMessage('Subject is required')
    .isLength({ min: 1, max: 200 })
    .withMessage('Subject must be between 1 and 200 characters'),
  body('htmlContent')
    .notEmpty()
    .withMessage('HTML content is required'),
  body('textContent')
    .optional()
    .isString()
    .withMessage('Text content must be a string'),
  body('variables')
    .optional()
    .isArray()
    .withMessage('Variables must be an array'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters')
], emailManagementController.createEmailTemplate);

// @route   PUT /api/admin/email-management/templates/:id
// @desc    Update email template
// @access  Private (Admin)
router.put('/templates/:id', [
  body('name')
    .optional()
    .isLength({ min: 3, max: 100 })
    .withMessage('Template name must be between 3 and 100 characters'),
  body('category')
    .optional()
    .isIn(['marketing', 'transactional', 'notification', 'welcome', 'abandoned_cart', 'order_confirmation'])
    .withMessage('Invalid template category'),
  body('subject')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('Subject must be between 1 and 200 characters'),
  body('htmlContent')
    .optional()
    .notEmpty()
    .withMessage('HTML content cannot be empty'),
  body('textContent')
    .optional()
    .isString()
    .withMessage('Text content must be a string'),
  body('variables')
    .optional()
    .isArray()
    .withMessage('Variables must be an array'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters')
], emailManagementController.updateEmailTemplate);

// @route   DELETE /api/admin/email-management/templates/:id
// @desc    Delete email template
// @access  Private (Admin)
router.delete('/templates/:id', emailManagementController.deleteEmailTemplate);

// @route   GET /api/admin/email-management/logs
// @desc    Get email delivery logs
// @access  Private (Admin)
router.get('/logs', emailManagementController.getEmailLogs);

// @route   POST /api/admin/email-management/logs/:id/retry
// @desc    Retry failed email
// @access  Private (Admin)
router.post('/logs/:id/retry', emailManagementController.retryFailedEmail);

// @route   GET /api/admin/email-management/stats
// @desc    Get email statistics
// @access  Private (Admin)
router.get('/stats', emailManagementController.getEmailStats);

// @route   GET /api/admin/email-management/settings
// @desc    Get admin email settings
// @access  Private (Admin)
router.get('/settings', emailManagementController.getAdminEmailSettings);

// @route   PUT /api/admin/email-management/settings
// @desc    Update admin email settings
// @access  Private (Admin)
router.put('/settings', [
  body('adminNotificationEmails')
    .isArray({ min: 1 })
    .withMessage('At least one admin notification email is required'),
  body('adminNotificationEmails.*')
    .isEmail()
    .withMessage('All admin notification emails must be valid email addresses'),
  body('emailFromName')
    .notEmpty()
    .withMessage('Email from name is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Email from name must be between 1 and 100 characters'),
  body('emailFromAddress')
    .isEmail()
    .withMessage('Email from address must be a valid email address'),
  body('enableOrderNotifications')
    .isBoolean()
    .withMessage('Enable order notifications must be a boolean'),
  body('enableStatusUpdates')
    .isBoolean()
    .withMessage('Enable status updates must be a boolean'),
  body('enableMarketingEmails')
    .isBoolean()
    .withMessage('Enable marketing emails must be a boolean')
], emailManagementController.updateAdminEmailSettings);

// @route   POST /api/admin/email-management/test
// @desc    Test email configuration
// @access  Private (Admin)
router.post('/test', emailManagementController.testEmailConfiguration);

module.exports = router;
