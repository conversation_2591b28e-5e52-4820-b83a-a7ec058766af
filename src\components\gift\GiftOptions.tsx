import React, { useState } from 'react';
import { 
  GiftIcon, 
  HeartIcon, 
  EnvelopeIcon,
  UserIcon,
  MapPinIcon 
} from '@heroicons/react/24/outline';
import AddressForm from '../forms/AddressForm';

interface GiftData {
  isGift: boolean;
  recipientName: string;
  recipientEmail: string;
  giftMessage: string;
  hidePrice: boolean;
  giftWrap: boolean;
  deliveryDate?: string;
  recipientAddress?: {
    firstName: string;
    lastName: string;
    streetAddress: string;
    streetAddress2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phone?: string;
  };
}

interface GiftOptionsProps {
  giftData: GiftData;
  onGiftDataChange: (data: GiftData) => void;
  showAddressForm?: boolean;
  className?: string;
}

const GiftOptions: React.FC<GiftOptionsProps> = ({
  giftData,
  onGiftDataChange,
  showAddressForm = true,
  className = ''
}) => {
  const [showRecipientAddress, setShowRecipientAddress] = useState(false);

  const handleToggleGift = (isGift: boolean) => {
    onGiftDataChange({
      ...giftData,
      isGift,
      // Reset gift data if turning off gift option
      ...(isGift ? {} : {
        recipientName: '',
        recipientEmail: '',
        giftMessage: '',
        hidePrice: false,
        giftWrap: false,
        deliveryDate: undefined,
        recipientAddress: undefined
      })
    });
    
    if (!isGift) {
      setShowRecipientAddress(false);
    }
  };

  const handleInputChange = (field: keyof GiftData, value: any) => {
    onGiftDataChange({
      ...giftData,
      [field]: value
    });
  };

  const handleAddressChange = (addressData: any) => {
    onGiftDataChange({
      ...giftData,
      recipientAddress: addressData
    });
  };

  const maxMessageLength = 250;
  const remainingChars = maxMessageLength - giftData.giftMessage.length;

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      {/* Gift Toggle */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <GiftIcon className="h-6 w-6 text-primary-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Gift Options</h3>
              <p className="text-sm text-gray-600">Send this order as a gift</p>
            </div>
          </div>
          
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={giftData.isGift}
              onChange={(e) => handleToggleGift(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      </div>

      {/* Gift Details */}
      {giftData.isGift && (
        <div className="p-6 space-y-6">
          {/* Recipient Information */}
          <div className="space-y-4">
            <h4 className="flex items-center text-md font-medium text-gray-900">
              <UserIcon className="h-5 w-5 mr-2 text-gray-500" />
              Recipient Information
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="recipientName" className="block text-sm font-medium text-gray-700 mb-1">
                  Recipient Name *
                </label>
                <input
                  type="text"
                  id="recipientName"
                  value={giftData.recipientName}
                  onChange={(e) => handleInputChange('recipientName', e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter recipient's full name"
                />
              </div>
              
              <div>
                <label htmlFor="recipientEmail" className="block text-sm font-medium text-gray-700 mb-1">
                  Recipient Email
                </label>
                <input
                  type="email"
                  id="recipientEmail"
                  value={giftData.recipientEmail}
                  onChange={(e) => handleInputChange('recipientEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter recipient's email (optional)"
                />
              </div>
            </div>
          </div>

          {/* Gift Message */}
          <div>
            <h4 className="flex items-center text-md font-medium text-gray-900 mb-3">
              <EnvelopeIcon className="h-5 w-5 mr-2 text-gray-500" />
              Gift Message
            </h4>
            
            <div className="relative">
              <textarea
                value={giftData.giftMessage}
                onChange={(e) => handleInputChange('giftMessage', e.target.value)}
                maxLength={maxMessageLength}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                placeholder="Write a personal message for the recipient..."
              />
              <div className="absolute bottom-2 right-2 text-xs text-gray-500">
                {remainingChars} characters remaining
              </div>
            </div>
          </div>

          {/* Gift Options */}
          <div className="space-y-4">
            <h4 className="flex items-center text-md font-medium text-gray-900">
              <HeartIcon className="h-5 w-5 mr-2 text-gray-500" />
              Gift Preferences
            </h4>
            
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={giftData.hidePrice}
                  onChange={(e) => handleInputChange('hidePrice', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-3 text-sm text-gray-700">
                  Hide prices on gift receipt
                </span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={giftData.giftWrap}
                  onChange={(e) => handleInputChange('giftWrap', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-3 text-sm text-gray-700">
                  Add gift wrapping (+$5.00)
                </span>
              </label>
            </div>
          </div>

          {/* Delivery Date */}
          <div>
            <label htmlFor="deliveryDate" className="block text-sm font-medium text-gray-700 mb-1">
              Preferred Delivery Date (optional)
            </label>
            <input
              type="date"
              id="deliveryDate"
              value={giftData.deliveryDate || ''}
              onChange={(e) => handleInputChange('deliveryDate', e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              We'll do our best to deliver by this date, but cannot guarantee exact delivery timing.
            </p>
          </div>

          {/* Recipient Address */}
          {showAddressForm && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="flex items-center text-md font-medium text-gray-900">
                  <MapPinIcon className="h-5 w-5 mr-2 text-gray-500" />
                  Recipient Address
                </h4>
                
                {!showRecipientAddress && (
                  <button
                    type="button"
                    onClick={() => setShowRecipientAddress(true)}
                    className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                  >
                    Add Different Address
                  </button>
                )}
              </div>

              {showRecipientAddress ? (
                <div className="border border-gray-200 rounded-lg p-4">
                  <AddressForm
                    initialData={giftData.recipientAddress}
                    onAddressChange={handleAddressChange}
                    showNameFields={false}
                    showPhoneField={true}
                    title=""
                    submitButtonText="Save Recipient Address"
                    onSubmit={handleAddressChange}
                  />
                  
                  <div className="mt-4 flex justify-end">
                    <button
                      type="button"
                      onClick={() => setShowRecipientAddress(false)}
                      className="text-gray-600 hover:text-gray-700 text-sm"
                    >
                      Use billing address instead
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  Gift will be sent to your billing address. Click "Add Different Address" to send to recipient's address.
                </div>
              )}
            </div>
          )}

          {/* Gift Summary */}
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
            <h5 className="font-medium text-primary-900 mb-2">Gift Summary</h5>
            <div className="text-sm text-primary-800 space-y-1">
              <p><strong>Recipient:</strong> {giftData.recipientName || 'Not specified'}</p>
              {giftData.recipientEmail && (
                <p><strong>Email:</strong> {giftData.recipientEmail}</p>
              )}
              {giftData.giftMessage && (
                <p><strong>Message:</strong> "{giftData.giftMessage}"</p>
              )}
              <div className="flex flex-wrap gap-2 mt-2">
                {giftData.hidePrice && (
                  <span className="inline-flex px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full">
                    Hide Prices
                  </span>
                )}
                {giftData.giftWrap && (
                  <span className="inline-flex px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full">
                    Gift Wrap (+$5.00)
                  </span>
                )}
                {giftData.deliveryDate && (
                  <span className="inline-flex px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full">
                    Deliver by {new Date(giftData.deliveryDate).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GiftOptions;
