import { useState, useCallback } from 'react';

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  data: any;
}

export interface AsyncState<T = any> {
  loading: boolean;
  error: string | null;
  data: T | null;
  success: boolean;
}

/**
 * Custom hook for managing async operations with loading states
 */
export function useAsyncOperation<T = any>(initialData: T | null = null) {
  const [state, setState] = useState<AsyncState<T>>({
    loading: false,
    error: null,
    data: initialData,
    success: false
  });

  const execute = useCallback(async (asyncFunction: () => Promise<T>) => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false
    }));

    try {
      const result = await asyncFunction();
      setState({
        loading: false,
        error: null,
        data: result,
        success: true
      });
      return result;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'An error occurred',
        success: false
      }));
      throw error;
    }
  }, []);

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      data: initialData,
      success: false
    });
  }, [initialData]);

  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data,
      success: true
    }));
  }, []);

  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      error,
      loading: false,
      success: false
    }));
  }, []);

  return {
    ...state,
    execute,
    reset,
    setData,
    setError
  };
}

/**
 * Custom hook for managing multiple loading states
 */
export function useMultipleLoadingStates() {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const setLoading = useCallback((key: string, loading: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: loading
    }));
  }, []);

  const isLoading = useCallback((key: string) => {
    return loadingStates[key] || false;
  }, [loadingStates]);

  const isAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some(loading => loading);
  }, [loadingStates]);

  const clearAll = useCallback(() => {
    setLoadingStates({});
  }, []);

  return {
    setLoading,
    isLoading,
    isAnyLoading,
    clearAll,
    loadingStates
  };
}

/**
 * Loading state manager for forms
 */
export class FormLoadingManager {
  private loadingStates: Map<string, boolean> = new Map();
  private callbacks: Set<() => void> = new Set();

  setLoading(field: string, loading: boolean) {
    this.loadingStates.set(field, loading);
    this.notifyCallbacks();
  }

  isLoading(field: string): boolean {
    return this.loadingStates.get(field) || false;
  }

  isAnyLoading(): boolean {
    return Array.from(this.loadingStates.values()).some(loading => loading);
  }

  clearAll() {
    this.loadingStates.clear();
    this.notifyCallbacks();
  }

  subscribe(callback: () => void) {
    this.callbacks.add(callback);
    return () => this.callbacks.delete(callback);
  }

  private notifyCallbacks() {
    this.callbacks.forEach(callback => callback());
  }
}

/**
 * Utility functions for loading states
 */
export const LoadingUtils = {
  /**
   * Create a delay for better UX (prevents flash of loading state)
   */
  withMinimumDelay: async <T>(
    promise: Promise<T>, 
    minimumDelay: number = 300
  ): Promise<T> => {
    const [result] = await Promise.all([
      promise,
      new Promise(resolve => setTimeout(resolve, minimumDelay))
    ]);
    return result;
  },

  /**
   * Debounce function for search/filter operations
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },

  /**
   * Throttle function for scroll/resize events
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0;
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  },

  /**
   * Create loading skeleton props
   */
  createSkeletonProps: (count: number = 1) => ({
    loading: true,
    count,
    height: 20,
    className: 'mb-2'
  }),

  /**
   * Get loading button props
   */
  getLoadingButtonProps: (loading: boolean, disabled: boolean = false) => ({
    disabled: loading || disabled,
    className: `${loading ? 'opacity-50 cursor-not-allowed' : ''}`
  }),

  /**
   * Format loading message based on operation
   */
  getLoadingMessage: (operation: string): string => {
    const messages: Record<string, string> = {
      'login': 'Signing you in...',
      'register': 'Creating your account...',
      'checkout': 'Processing your order...',
      'payment': 'Processing payment...',
      'upload': 'Uploading files...',
      'save': 'Saving changes...',
      'delete': 'Deleting item...',
      'load': 'Loading...',
      'search': 'Searching...',
      'filter': 'Applying filters...',
      'submit': 'Submitting...',
      'verify': 'Verifying...',
      'send': 'Sending...',
      'update': 'Updating...'
    };

    return messages[operation.toLowerCase()] || 'Please wait...';
  }
};

/**
 * Loading state constants
 */
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

export type LoadingStateType = typeof LOADING_STATES[keyof typeof LOADING_STATES];

/**
 * Loading priorities for better UX
 */
export const LOADING_PRIORITIES = {
  HIGH: 'high',      // Critical operations (payment, login)
  MEDIUM: 'medium',  // Important operations (form submission)
  LOW: 'low'         // Background operations (analytics, prefetch)
} as const;

export type LoadingPriority = typeof LOADING_PRIORITIES[keyof typeof LOADING_PRIORITIES];

/**
 * Loading state with priority
 */
export interface PriorityLoadingState {
  loading: boolean;
  priority: LoadingPriority;
  message?: string;
  progress?: number;
}

/**
 * Hook for managing priority-based loading states
 */
export function usePriorityLoading() {
  const [states, setStates] = useState<Record<string, PriorityLoadingState>>({});

  const setLoading = useCallback((
    key: string, 
    loading: boolean, 
    priority: LoadingPriority = LOADING_PRIORITIES.MEDIUM,
    message?: string,
    progress?: number
  ) => {
    setStates(prev => ({
      ...prev,
      [key]: { loading, priority, message, progress }
    }));
  }, []);

  const getHighestPriorityLoading = useCallback(() => {
    const loadingStates = Object.values(states).filter(state => state.loading);
    
    if (loadingStates.length === 0) return null;

    // Sort by priority (high > medium > low)
    const priorityOrder = [LOADING_PRIORITIES.HIGH, LOADING_PRIORITIES.MEDIUM, LOADING_PRIORITIES.LOW];
    
    return loadingStates.sort((a, b) => 
      priorityOrder.indexOf(a.priority) - priorityOrder.indexOf(b.priority)
    )[0];
  }, [states]);

  const clearLoading = useCallback((key: string) => {
    setStates(prev => {
      const newStates = { ...prev };
      delete newStates[key];
      return newStates;
    });
  }, []);

  const clearAll = useCallback(() => {
    setStates({});
  }, []);

  return {
    setLoading,
    clearLoading,
    clearAll,
    getHighestPriorityLoading,
    states
  };
}

export default LoadingUtils;
