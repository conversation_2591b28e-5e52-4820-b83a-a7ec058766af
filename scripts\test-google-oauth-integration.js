#!/usr/bin/env node

/**
 * Google OAuth Integration Test
 * Tests the complete Google OAuth flow after dependency fixes
 */

require('dotenv').config();
const fs = require('fs');

console.log('🔍 Testing Google OAuth Integration...\n');

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

// Test 1: Frontend Package Compatibility
function testFrontendPackages() {
  log('🔍 Testing Frontend Package Compatibility...', 'info');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Check React version
    const reactVersion = packageJson.dependencies.react;
    const reactMajorVersion = parseInt(reactVersion.replace(/[^\d]/g, ''));
    
    if (reactMajorVersion >= 18) {
      log(`✅ React ${reactVersion} - Compatible with modern OAuth`, 'success');
    } else {
      log(`❌ React ${reactVersion} - May have compatibility issues`, 'error');
      return false;
    }
    
    // Check @react-oauth/google
    if (packageJson.dependencies['@react-oauth/google']) {
      log(`✅ @react-oauth/google: ${packageJson.dependencies['@react-oauth/google']}`, 'success');
    } else {
      log('❌ @react-oauth/google not found', 'error');
      return false;
    }
    
    // Ensure old package is removed
    if (!packageJson.dependencies['react-google-login']) {
      log('✅ react-google-login properly removed', 'success');
    } else {
      log('❌ react-google-login still present', 'error');
      return false;
    }
    
    return true;
  } catch (error) {
    log(`❌ Error checking packages: ${error.message}`, 'error');
    return false;
  }
}

// Test 2: Backend Dependencies
function testBackendDependencies() {
  log('\n🔍 Testing Backend Dependencies...', 'info');
  
  try {
    // Test google-auth-library
    try {
      const { OAuth2Client } = require('google-auth-library');
      const client = new OAuth2Client();
      log('✅ google-auth-library imported successfully', 'success');
    } catch (error) {
      log(`❌ google-auth-library error: ${error.message}`, 'error');
      return false;
    }
    
    // Test passport-google-oauth20
    try {
      const GoogleStrategy = require('passport-google-oauth20').Strategy;
      log('✅ passport-google-oauth20 imported successfully', 'success');
    } catch (error) {
      log(`❌ passport-google-oauth20 error: ${error.message}`, 'error');
      return false;
    }
    
    return true;
  } catch (error) {
    log(`❌ Backend dependency test failed: ${error.message}`, 'error');
    return false;
  }
}

// Test 3: Environment Configuration
function testEnvironmentConfig() {
  log('\n🔍 Testing Environment Configuration...', 'info');
  
  const requiredVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET'
  ];
  
  const optionalVars = [
    'GOOGLE_OAUTH_CALLBACK_URL'
  ];
  
  let allRequired = true;
  
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      const displayValue = varName === 'GOOGLE_CLIENT_SECRET' 
        ? '***' + process.env[varName].slice(-4)
        : process.env[varName];
      log(`✅ ${varName}: ${displayValue}`, 'success');
    } else {
      log(`❌ ${varName}: Missing (required)`, 'error');
      allRequired = false;
    }
  });
  
  optionalVars.forEach(varName => {
    if (process.env[varName]) {
      log(`✅ ${varName}: ${process.env[varName]}`, 'success');
    } else {
      log(`⚠️  ${varName}: Not set (using default)`, 'warning');
    }
  });
  
  return allRequired;
}

// Test 4: Frontend Component Structure
function testFrontendComponents() {
  log('\n🔍 Testing Frontend Component Structure...', 'info');
  
  const componentFiles = [
    'src/components/auth/SocialLogin.tsx',
    'src/pages/Login.tsx',
    'src/pages/Register.tsx',
    'src/pages/admin/AdminLogin.tsx'
  ];
  
  let allFound = true;
  
  componentFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`✅ ${file} exists`, 'success');
      
      // Check if file uses modern @react-oauth/google
      try {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes('@react-oauth/google')) {
          log(`   ✅ Uses modern @react-oauth/google`, 'success');
        } else if (content.includes('react-google-login')) {
          log(`   ❌ Still uses old react-google-login`, 'error');
          allFound = false;
        } else {
          log(`   ℹ️  No direct Google OAuth usage`, 'info');
        }
      } catch (error) {
        log(`   ⚠️  Could not read file content`, 'warning');
      }
    } else {
      log(`❌ ${file} not found`, 'error');
      allFound = false;
    }
  });
  
  return allFound;
}

// Test 5: Backend Route Configuration
function testBackendRoutes() {
  log('\n🔍 Testing Backend Route Configuration...', 'info');
  
  const backendFiles = [
    'server/routes/auth.js',
    'server/controllers/authController.js',
    'server/config/passport.js'
  ];
  
  let allConfigured = true;
  
  backendFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`✅ ${file} exists`, 'success');
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for Google OAuth routes/configuration
        if (content.includes('google') && (content.includes('oauth') || content.includes('OAuth'))) {
          log(`   ✅ Contains Google OAuth configuration`, 'success');
        } else {
          log(`   ⚠️  No Google OAuth configuration found`, 'warning');
        }
      } catch (error) {
        log(`   ⚠️  Could not read file content`, 'warning');
      }
    } else {
      log(`❌ ${file} not found`, 'error');
      allConfigured = false;
    }
  });
  
  return allConfigured;
}

// Test 6: OAuth URL Configuration
function testOAuthUrls() {
  log('\n🔍 Testing OAuth URL Configuration...', 'info');
  
  const baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
  
  const urls = {
    'Google Login Initiation': `${baseUrl}/api/auth/google/login`,
    'Google OAuth Callback': process.env.GOOGLE_OAUTH_CALLBACK_URL || `${baseUrl}/api/auth/google/callback`,
    'Frontend Auth Callback': `${frontendUrl}/auth/callback`
  };
  
  let allValid = true;
  
  Object.entries(urls).forEach(([name, url]) => {
    try {
      new URL(url);
      log(`✅ ${name}: ${url}`, 'success');
    } catch (error) {
      log(`❌ ${name}: Invalid URL - ${url}`, 'error');
      allValid = false;
    }
  });
  
  return allValid;
}

// Test 7: Generate Integration Report
function generateIntegrationReport(results) {
  log('\n📋 Generating Integration Report...', 'info');
  
  const report = {
    timestamp: new Date().toISOString(),
    testResults: results,
    summary: {
      totalTests: Object.keys(results).length,
      passedTests: Object.values(results).filter(Boolean).length,
      failedTests: Object.values(results).filter(r => !r).length
    },
    recommendations: [],
    nextSteps: []
  };
  
  // Add recommendations based on results
  if (!results.frontendPackages) {
    report.recommendations.push('Update package.json to remove react-google-login and ensure @react-oauth/google is installed');
  }
  
  if (!results.backendDependencies) {
    report.recommendations.push('Install missing backend dependencies: google-auth-library, passport-google-oauth20');
  }
  
  if (!results.environmentConfig) {
    report.recommendations.push('Set required environment variables: GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET');
  }
  
  // Add next steps
  if (report.summary.failedTests === 0) {
    report.nextSteps = [
      'Run production build: npm run deploy:fullstack',
      'Test Google OAuth login flow manually',
      'Verify Square payment integration still works',
      'Deploy to production server'
    ];
  } else {
    report.nextSteps = [
      'Fix the failed tests listed above',
      'Run this test again to verify fixes',
      'Proceed with production build once all tests pass'
    ];
  }
  
  const reportPath = 'google-oauth-integration-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`✅ Integration report saved: ${reportPath}`, 'success');
  return report;
}

// Main test function
async function runIntegrationTests() {
  log('🚀 Google OAuth Integration Test Suite', 'info');
  log('=====================================\n');
  
  const results = {
    frontendPackages: testFrontendPackages(),
    backendDependencies: testBackendDependencies(),
    environmentConfig: testEnvironmentConfig(),
    frontendComponents: testFrontendComponents(),
    backendRoutes: testBackendRoutes(),
    oauthUrls: testOAuthUrls()
  };
  
  // Generate report
  const report = generateIntegrationReport(results);
  
  // Summary
  log('\n📊 Integration Test Summary', 'info');
  log('===========================');
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    log(`${status} ${testName}`);
  });
  
  log(`\nOverall: ${report.summary.passedTests}/${report.summary.totalTests} tests passed`);
  
  if (report.summary.failedTests === 0) {
    log('\n🎉 All Google OAuth integration tests passed!', 'success');
    log('✅ React 18 compatibility confirmed', 'success');
    log('✅ Modern @react-oauth/google package working', 'success');
    log('✅ Backend OAuth configuration valid', 'success');
    log('✅ Ready for production deployment', 'success');
    
    log('\n🚀 Next Steps:', 'info');
    report.nextSteps.forEach(step => log(`   • ${step}`));
    
  } else {
    log('\n⚠️  Some integration tests failed', 'warning');
    
    if (report.recommendations.length > 0) {
      log('\n🔧 Recommendations:', 'info');
      report.recommendations.forEach(rec => log(`   • ${rec}`));
    }
    
    log('\n📋 Next Steps:', 'info');
    report.nextSteps.forEach(step => log(`   • ${step}`));
  }
  
  log('\n✅ Google OAuth integration test completed!', 'success');
  
  return report.summary.failedTests === 0;
}

// Run the tests
runIntegrationTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log(`❌ Integration test failed: ${error.message}`, 'error');
  process.exit(1);
});
