import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminPageHeader from '../../components/admin/AdminPageHeader';
import AdminDataTable, { TableColumn, TableAction } from '../../components/admin/AdminDataTable';
import {
  UserGroupIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  isActive: boolean;
  lastLoginAt: string | null;
  createdAt: string;
  orderCount?: number;
  totalSpent?: number;
  // Enhanced customer analytics
  membershipType?: 'first-time' | 'regular' | 'premium';
  trafficSource?: 'organic' | 'direct' | 'social-media' | 'referral' | 'paid';
  authProvider?: 'local' | 'google' | 'facebook' | 'both';
  gender?: 'he' | 'she' | 'they';
  lifetimeValue?: number;
  referralCount?: number;
  socialShares?: number;
}

interface UserFilters {
  search: string;
  role: string;
  status: string;
  membershipType: string;
  trafficSource: string;
  authProvider: string;
  dateFrom: string;
  dateTo: string;
}

const UserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    role: 'all',
    status: 'all',
    membershipType: 'all',
    trafficSource: 'all',
    authProvider: 'all',
    dateFrom: '',
    dateTo: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // Sample data
  useEffect(() => {
    setUsers([
      {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: 'customer',
        isActive: true,
        lastLoginAt: '2024-01-15T10:30:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        orderCount: 5,
        totalSpent: 299.99,
        membershipType: 'regular',
        trafficSource: 'organic',
        authProvider: 'google',
        gender: 'he',
        lifetimeValue: 299.99,
        referralCount: 2,
        socialShares: 5
      },
      {
        id: 2,
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        lastLoginAt: '2024-01-15T09:15:00Z',
        createdAt: '2023-12-15T00:00:00Z',
        orderCount: 0,
        totalSpent: 0
      },
      {
        id: 3,
        firstName: 'Mike',
        lastName: 'Johnson',
        email: '<EMAIL>',
        role: 'customer',
        isActive: false,
        lastLoginAt: '2024-01-10T16:45:00Z',
        createdAt: '2023-11-20T00:00:00Z',
        orderCount: 12,
        totalSpent: 1299.50,
        membershipType: 'premium',
        trafficSource: 'referral',
        authProvider: 'local',
        gender: 'he',
        lifetimeValue: 1299.50,
        referralCount: 8,
        socialShares: 12
      },
      {
        id: 4,
        firstName: 'Sarah',
        lastName: 'Wilson',
        email: '<EMAIL>',
        role: 'manager',
        isActive: true,
        lastLoginAt: '2024-01-14T14:20:00Z',
        createdAt: '2023-10-05T00:00:00Z',
        orderCount: 3,
        totalSpent: 189.75
      }
    ]);
    setPagination(prev => ({ ...prev, total: 4 }));
  }, []);

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    // Implement search logic here
  };

  const handleFilterChange = (key: keyof UserFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    // Implement filter logic here
  };

  const handleBulkAction = (action: string, userIds: string[]) => {
    console.log(`Bulk ${action} for users:`, userIds);
    // Implement bulk actions here
    setSelectedUsers([]);
  };

  const handleUserAction = (action: string, user: User) => {
    switch (action) {
      case 'view':
        navigate(`/admin/users/${user.id}`);
        break;
      case 'edit':
        navigate(`/admin/users/${user.id}/edit`);
        break;
      case 'delete':
        if (window.confirm('Are you sure you want to delete this user?')) {
          console.log('Delete user:', user.id);
          // Implement delete logic here
        }
        break;
      case 'toggle-status':
        console.log('Toggle status for user:', user.id);
        // Implement status toggle logic here
        break;
    }
  };

  const columns: TableColumn<User>[] = [
    {
      key: 'name',
      title: 'Name',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700">
                {record.firstName.charAt(0)}{record.lastName.charAt(0)}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {record.firstName} {record.lastName}
            </div>
            <div className="text-sm text-gray-500">{record.email}</div>
            <div className="flex items-center space-x-2 mt-1">
              {record.membershipType && (
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  record.membershipType === 'premium'
                    ? 'bg-purple-100 text-purple-800'
                    : record.membershipType === 'regular'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {record.membershipType}
                </span>
              )}
              {record.authProvider && record.authProvider !== 'local' && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {record.authProvider === 'both' ? 'OAuth+Local' : record.authProvider}
                </span>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'role',
      title: 'Role',
      render: (value) => {
        const roleColors = {
          admin: 'bg-red-100 text-red-800',
          manager: 'bg-blue-100 text-blue-800',
          customer: 'bg-green-100 text-green-800'
        };
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${roleColors[value as keyof typeof roleColors]}`}>
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </span>
        );
      }
    },
    {
      key: 'isActive',
      title: 'Status',
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'orderCount',
      title: 'Orders',
      align: 'center',
      render: (value) => <span className="text-sm text-gray-900">{value || 0}</span>
    },
    {
      key: 'totalSpent',
      title: 'Total Spent',
      align: 'right',
      render: (value) => <span className="text-sm font-medium">${(value || 0).toFixed(2)}</span>
    },
    {
      key: 'analytics',
      title: 'Customer Analytics',
      render: (_, record) => (
        <div className="text-xs space-y-1">
          {record.trafficSource && (
            <div className="flex items-center">
              <span className="text-gray-500 w-12">Source:</span>
              <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                record.trafficSource === 'organic' ? 'bg-green-100 text-green-700' :
                record.trafficSource === 'paid' ? 'bg-red-100 text-red-700' :
                record.trafficSource === 'social-media' ? 'bg-blue-100 text-blue-700' :
                record.trafficSource === 'referral' ? 'bg-yellow-100 text-yellow-700' :
                'bg-gray-100 text-gray-700'
              }`}>
                {record.trafficSource}
              </span>
            </div>
          )}
          {record.lifetimeValue && (
            <div className="flex items-center">
              <span className="text-gray-500 w-12">LTV:</span>
              <span className="font-medium">${record.lifetimeValue.toFixed(2)}</span>
            </div>
          )}
          {record.referralCount && record.referralCount > 0 && (
            <div className="flex items-center">
              <span className="text-gray-500 w-12">Refs:</span>
              <span className="text-indigo-600 font-medium">{record.referralCount}</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'lastLoginAt',
      title: 'Last Login',
      render: (value) => value ? new Date(value).toLocaleDateString() : 'Never'
    },
    {
      key: 'createdAt',
      title: 'Joined',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  const actions: TableAction<User>[] = [
    {
      label: 'View Details',
      onClick: (user) => handleUserAction('view', user),
      icon: EyeIcon
    },
    {
      label: 'Edit User',
      onClick: (user) => handleUserAction('edit', user),
      icon: PencilIcon
    },
    {
      label: user => user.isActive ? 'Deactivate' : 'Activate',
      onClick: (user) => handleUserAction('toggle-status', user),
      variant: user => user.isActive ? 'secondary' : 'primary'
    },
    {
      label: 'Delete User',
      onClick: (user) => handleUserAction('delete', user),
      icon: TrashIcon,
      variant: 'danger',
      disabled: (user) => user.role === 'admin'
    }
  ];

  return (
    <AdminLayout>
      <AdminPageHeader
        title="User Management"
        subtitle="Manage user accounts, roles, and permissions"
        stats={[
          { label: 'Total Users', value: '2,890' },
          { label: 'Active Users', value: '2,654', change: { value: 5.2, type: 'increase' } },
          { label: 'New This Month', value: '156', change: { value: 12.3, type: 'increase' } },
          { label: 'Admin Users', value: '8' }
        ]}
        actions={[
          {
            label: 'Add User',
            href: '/admin/users/new',
            variant: 'primary',
            icon: PlusIcon
          },
          {
            label: 'Export Users',
            onClick: () => console.log('Export users'),
            variant: 'secondary',
            icon: ArrowDownTrayIcon
          }
        ]}
      />

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-indigo-600 hover:text-indigo-900 flex items-center"
            >
              <FunnelIcon className="h-4 w-4 mr-1" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </button>
          </div>
        </div>

        {showFilters && (
          <div className="px-6 py-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={filters.search}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  value={filters.role}
                  onChange={(e) => handleFilterChange('role', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Roles</option>
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                  <option value="customer">Customer</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date Range
                </label>
                <div className="flex space-x-2">
                  <input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
            </div>

            {/* Enhanced Filters Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Membership Type
                </label>
                <select
                  value={filters.membershipType}
                  onChange={(e) => handleFilterChange('membershipType', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Memberships</option>
                  <option value="first-time">First-time</option>
                  <option value="regular">Regular</option>
                  <option value="premium">Premium</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Traffic Source
                </label>
                <select
                  value={filters.trafficSource}
                  onChange={(e) => handleFilterChange('trafficSource', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Sources</option>
                  <option value="organic">Organic Search</option>
                  <option value="direct">Direct</option>
                  <option value="social-media">Social Media</option>
                  <option value="referral">Referral</option>
                  <option value="paid">Paid Ads</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Auth Provider
                </label>
                <select
                  value={filters.authProvider}
                  onChange={(e) => handleFilterChange('authProvider', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Providers</option>
                  <option value="local">Email/Password</option>
                  <option value="google">Google</option>
                  <option value="facebook">Facebook</option>
                  <option value="both">Multiple</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Users Table */}
      <AdminDataTable
        columns={columns}
        data={users}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(page, pageSize) => setPagination(prev => ({ ...prev, current: page, pageSize }))}
        actions={actions}
        bulkActions={{
          actions: [
            {
              label: 'Activate Selected',
              onClick: (ids) => handleBulkAction('activate', ids),
              variant: 'primary'
            },
            {
              label: 'Deactivate Selected',
              onClick: (ids) => handleBulkAction('deactivate', ids),
              variant: 'secondary'
            },
            {
              label: 'Delete Selected',
              onClick: (ids) => handleBulkAction('delete', ids),
              variant: 'danger'
            }
          ],
          getRowId: (user) => user.id.toString()
        }}
        emptyText="No users found"
      />
    </AdminLayout>
  );
};

export default UserManagement;
