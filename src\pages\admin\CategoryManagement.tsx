import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchCategories, createCategory, updateCategory, deleteCategory } from '../../store/slices/categorySlice';
import { addToast } from '../../store/slices/uiSlice';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminPageHeader from '../../components/admin/AdminPageHeader';
import AdminDataTable, { TableColumn, TableAction } from '../../components/admin/AdminDataTable';
import {
  FolderIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  TagIcon
} from '@heroicons/react/24/outline';

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parentId?: number;
  isActive: boolean;
  productCount: number;
  createdAt: string;
  updatedAt: string;
  children?: Category[];
  parent?: Category;
}

interface CategoryFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  parentCategory: 'all' | string;
}

const CategoryManagement: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { categories, loading, pagination } = useAppSelector((state) => state.categories);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<CategoryFilters>({
    search: '',
    status: 'all',
    parentCategory: 'all'
  });
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  // Fetch categories from API
  const fetchCategoriesData = async () => {
    try {
      const filterParams: any = {
        page: pagination.current,
        limit: pagination.pageSize
      };

      if (filters.search) filterParams.search = filters.search;
      if (filters.status !== 'all') filterParams.status = filters.status;
      if (filters.parentCategory !== 'all') filterParams.parentId = filters.parentCategory;

      await dispatch(fetchCategories(filterParams)).unwrap();
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to fetch categories'
      }));
    }
  };

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCategoriesData();
    }, filters.search ? 500 : 0);

    return () => clearTimeout(timeoutId);
  }, [dispatch, pagination.current, pagination.pageSize, filters]);

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  const handleFilterChange = (key: keyof CategoryFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleCategoryAction = async (action: string, category: Category) => {
    switch (action) {
      case 'view':
        navigate(`/admin/categories/${category.id}`);
        break;
      case 'edit':
        setEditingCategory(category);
        setShowCategoryForm(true);
        break;
      case 'delete':
        if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
          try {
            await dispatch(deleteCategory(category.id)).unwrap();
            dispatch(addToast({
              type: 'success',
              title: 'Category Deleted',
              message: 'Category has been deleted successfully'
            }));
            fetchCategoriesData();
          } catch (error: any) {
            dispatch(addToast({
              type: 'error',
              title: 'Deletion Failed',
              message: error.message || 'Failed to delete category'
            }));
          }
        }
        break;
      case 'toggle-status':
        try {
          const newStatus = !category.isActive;
          await dispatch(updateCategory({
            id: category.id,
            categoryData: { isActive: newStatus }
          })).unwrap();
          dispatch(addToast({
            type: 'success',
            title: 'Status Updated',
            message: `Category has been ${newStatus ? 'activated' : 'deactivated'}`
          }));
          fetchCategoriesData();
        } catch (error: any) {
          dispatch(addToast({
            type: 'error',
            title: 'Update Failed',
            message: error.message || 'Failed to update category status'
          }));
        }
        break;
    }
  };

  const handleBulkAction = async (action: string, categoryIds: string[]) => {
    if (categoryIds.length === 0) {
      dispatch(addToast({
        type: 'warning',
        title: 'No Selection',
        message: 'Please select categories to perform bulk actions'
      }));
      return;
    }

    const confirmMessage = `Are you sure you want to ${action} ${categoryIds.length} category(ies)?`;
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const promises = categoryIds.map(async (id) => {
        switch (action) {
          case 'activate':
            return dispatch(updateCategory({
              id: parseInt(id),
              categoryData: { isActive: true }
            })).unwrap();
          case 'deactivate':
            return dispatch(updateCategory({
              id: parseInt(id),
              categoryData: { isActive: false }
            })).unwrap();
          case 'delete':
            return dispatch(deleteCategory(parseInt(id))).unwrap();
          default:
            throw new Error(`Unknown action: ${action}`);
        }
      });

      await Promise.all(promises);

      dispatch(addToast({
        type: 'success',
        title: 'Bulk Action Completed',
        message: `Successfully ${action}d ${categoryIds.length} category(ies)`
      }));

      setSelectedCategories([]);
      fetchCategoriesData();
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Bulk Action Failed',
        message: error.message || `Failed to ${action} categories`
      }));
    }
  };

  // Calculate category stats
  const categoryStats = React.useMemo(() => {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(c => c.isActive).length;
    const parentCategories = categories.filter(c => !c.parentId).length;
    const subcategories = categories.filter(c => c.parentId).length;

    return {
      totalCategories,
      activeCategories,
      parentCategories,
      subcategories
    };
  }, [categories]);

  const columns: TableColumn<Category>[] = [
    {
      key: 'name',
      title: 'Category',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <FolderIcon className="h-8 w-8 text-blue-500" />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {record.parent && (
                <span className="text-gray-500">{record.parent.name} → </span>
              )}
              {record.name}
            </div>
            <div className="text-sm text-gray-500">
              Slug: {record.slug}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'productCount',
      title: 'Products',
      align: 'center',
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {value} products
        </span>
      )
    },
    {
      key: 'isActive',
      title: 'Status',
      align: 'center',
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'updatedAt',
      title: 'Last Updated',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  const actions: TableAction<Category>[] = [
    {
      label: 'View',
      icon: EyeIcon,
      onClick: (record) => handleCategoryAction('view', record)
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      onClick: (record) => handleCategoryAction('edit', record)
    },
    {
      label: 'Toggle Status',
      icon: TagIcon,
      onClick: (record) => handleCategoryAction('toggle-status', record)
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (record) => handleCategoryAction('delete', record),
      variant: 'danger'
    }
  ];

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Category Management"
        subtitle="Organize your products with categories and subcategories"
        stats={[
          { 
            label: 'Total Categories', 
            value: categoryStats.totalCategories.toString() 
          },
          { 
            label: 'Active Categories', 
            value: categoryStats.activeCategories.toString()
          },
          { 
            label: 'Parent Categories', 
            value: categoryStats.parentCategories.toString()
          },
          { 
            label: 'Subcategories', 
            value: categoryStats.subcategories.toString()
          }
        ]}
        actions={[
          {
            label: 'Add Category',
            onClick: () => {
              setEditingCategory(null);
              setShowCategoryForm(true);
            },
            variant: 'primary',
            icon: PlusIcon
          }
        ]}
      />

      {/* Category Management Table */}
      <AdminDataTable
        data={categories}
        columns={columns}
        actions={actions}
        loading={loading}
        selectedRows={selectedCategories}
        onSelectionChange={setSelectedCategories}
        onBulkAction={handleBulkAction}
        bulkActions={[
          { label: 'Activate', value: 'activate' },
          { label: 'Deactivate', value: 'deactivate' },
          { label: 'Delete', value: 'delete', variant: 'danger' }
        ]}
        searchable
        onSearch={handleSearch}
        filterable
        filters={showFilters}
        onToggleFilters={() => setShowFilters(!showFilters)}
        filterContent={
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type
              </label>
              <select
                value={filters.parentCategory}
                onChange={(e) => handleFilterChange('parentCategory', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                <option value="parent">Parent Categories</option>
                <option value="subcategory">Subcategories</option>
              </select>
            </div>
          </div>
        }
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          onChange: (page, pageSize) => {
            // Handle pagination change
          }
        }}
      />
    </AdminLayout>
  );
};

export default CategoryManagement;
