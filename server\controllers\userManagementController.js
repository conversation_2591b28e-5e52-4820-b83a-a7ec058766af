const { User, Role, Address, Order, Review, AuditLog } = require('../models');
const { Op, sequelize } = require('sequelize');
const bcrypt = require('bcrypt');
const { validationResult } = require('express-validator');

// Get all users with advanced filtering and pagination
const getUsers = async (req, res) => {
  console.log('🔍 DEBUG: getUsers method called');
  console.log('🔍 DEBUG: req.query:', req.query);
  console.log('🔍 DEBUG: req.user:', req.user);
  try {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      status,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      dateFrom,
      dateTo,
      includeStats = false
    } = req.query;

    // Build where clause
    const where = {};
    
    if (role && role !== 'all') {
      // Find role by name and use roleId
      const roleRecord = await Role.findOne({ where: { name: role } });
      if (roleRecord) {
        where.roleId = roleRecord.id;
      }
    }
    
    if (status === 'active') {
      where.isActive = true;
    } else if (status === 'inactive') {
      where.isActive = false;
    }
    
    if (search) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt[Op.gte] = new Date(dateFrom);
      if (dateTo) where.createdAt[Op.lte] = new Date(dateTo);
    }

    // Build include array
    const include = [
      {
        model: Role,
        as: 'Role',
        attributes: ['id', 'name', 'displayName']
      }
    ];
    if (includeStats === 'true') {
      include.push(
        {
          model: Order,
          as: 'orders',
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('orders.id')), 'orderCount'],
            [sequelize.fn('SUM', sequelize.col('orders.total')), 'totalSpent']
          ],
          required: false
        },
        {
          model: Review,
          as: 'reviews',
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('reviews.id')), 'reviewCount']
          ],
          required: false
        }
      );
    }

    const { count, rows: users } = await User.findAndCountAll({
      where,
      include,
      attributes: { 
        exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] 
      },
      order: [[sortBy, sortOrder.toUpperCase()]],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit),
      distinct: true
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    // Log admin action (AuditLog functionality not implemented yet)
    console.log('🔍 AUDIT LOG: VIEW_USERS action by user', req.user.id);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalUsers: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
};

// Get single user by ID
const getUserById = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId, {
      attributes: { 
        exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] 
      },
      include: [
        {
          model: Address,
          as: 'addresses'
        },
        {
          model: Order,
          as: 'orders',
          limit: 10,
          order: [['createdAt', 'DESC']],
          attributes: ['id', 'orderNumber', 'status', 'total', 'createdAt']
        },
        {
          model: Review,
          as: 'reviews',
          limit: 5,
          order: [['createdAt', 'DESC']],
          include: [{
            model: require('../models').Product,
            as: 'product',
            attributes: ['id', 'name', 'slug']
          }]
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_USER', 'USER', userId, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: { user }
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: error.message
    });
  }
};

// Create new user
const createUser = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      firstName,
      lastName,
      email,
      password,
      phone,
      dateOfBirth,
      role = 'customer',
      isActive = true,
      isEmailVerified = false
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = await User.create({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      dateOfBirth,
      role,
      isActive,
      isEmailVerified
    });

    // Remove password from response
    const userResponse = user.toJSON();
    delete userResponse.password;

    // Log admin action
    await AuditLog.logCreate(req.user.id, 'USER', user.id, userResponse, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: { user: userResponse }
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      error: error.message
    });
  }
};

// Update user
const updateUser = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const updateData = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Store old values for audit log
    const oldValues = user.toJSON();
    delete oldValues.password;

    // Handle password update
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 12);
    }

    // Prevent users from changing their own role (unless super admin)
    if (req.user.id === parseInt(userId) && req.user.role !== 'super_admin') {
      delete updateData.role;
    }

    // Update user
    await user.update(updateData);

    // Get updated user data
    const updatedUser = await User.findByPk(userId, {
      attributes: { exclude: ['password'] }
    });

    // Log admin action
    await AuditLog.logUpdate(req.user.id, 'USER', userId, oldValues, updatedUser.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.json({
      success: true,
      message: 'User updated successfully',
      data: { user: updatedUser }
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      error: error.message
    });
  }
};

// Delete user (soft delete)
const deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent self-deletion
    if (req.user.id === parseInt(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account'
      });
    }

    // Store old values for audit log
    const oldValues = user.toJSON();
    delete oldValues.password;

    // Soft delete by deactivating
    await user.update({ isActive: false });

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'DEACTIVATE_USER', 'USER', userId, {
      oldValues,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'high'
    });

    res.json({
      success: true,
      message: 'User deactivated successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete user',
      error: error.message
    });
  }
};

// Bulk operations
const bulkUpdateUsers = async (req, res) => {
  try {
    const { userIds, action, data } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'User IDs array is required'
      });
    }

    let updateData = {};
    let actionName = '';

    switch (action) {
      case 'activate':
        updateData = { isActive: true };
        actionName = 'BULK_ACTIVATE';
        break;
      case 'deactivate':
        updateData = { isActive: false };
        actionName = 'BULK_DEACTIVATE';
        break;
      case 'change_role':
        if (!data.role) {
          return res.status(400).json({
            success: false,
            message: 'Role is required for role change action'
          });
        }
        updateData = { role: data.role };
        actionName = 'BULK_CHANGE_ROLE';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    // Prevent bulk operations on self
    if (userIds.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot perform bulk operations on your own account'
      });
    }

    // Update users
    const [updatedCount] = await User.update(updateData, {
      where: {
        id: { [Op.in]: userIds }
      }
    });

    // Log admin action
    await AuditLog.logBulkAction(req.user.id, actionName, 'USER', userIds, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { updateData },
      severity: 'high'
    });

    res.json({
      success: true,
      message: `Successfully updated ${updatedCount} users`,
      data: { updatedCount }
    });

  } catch (error) {
    console.error('Bulk update users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update users',
      error: error.message
    });
  }
};

// Reset user password
const resetUserPassword = async (req, res) => {
  try {
    const { userId } = req.params;
    const { newPassword } = req.body;

    if (!newPassword || newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    await user.update({
      password: hashedPassword,
      passwordResetToken: null,
      passwordResetExpires: null
    });

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'RESET_PASSWORD', 'USER', userId, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'high'
    });

    res.json({
      success: true,
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset password',
      error: error.message
    });
  }
};

// Get user statistics
const getUserStatistics = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total users
      User.count(),

      // Active users
      User.count({ where: { isActive: true } }),

      // Users by role
      User.findAll({
        attributes: [
          'role',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['role']
      }),

      // New users this month
      User.count({
        where: {
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),

      // Users with orders
      User.count({
        include: [{
          model: Order,
          as: 'orders',
          required: true
        }]
      })
    ]);

    const [totalUsers, activeUsers, usersByRole, newUsersThisMonth, usersWithOrders] = stats;

    res.json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        usersByRole,
        newUsersThisMonth,
        usersWithOrders,
        usersWithoutOrders: totalUsers - usersWithOrders
      }
    });

  } catch (error) {
    console.error('Get user statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user statistics',
      error: error.message
    });
  }
};

module.exports = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  resetUserPassword,
  getUserStatistics
};
