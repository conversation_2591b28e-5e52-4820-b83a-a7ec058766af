import api from './api';

export interface StoreLocation {
  id: string;
  name: string;
  address: {
    addressLine1: string;
    addressLine2?: string;
    locality: string;
    administrativeDistrictLevel1: string;
    postalCode: string;
    country: string;
  };
  phoneNumber?: string;
  businessHours?: {
    periods: Array<{
      dayOfWeek: string;
      startLocalTime: string;
      endLocalTime: string;
    }>;
  };
  status: 'ACTIVE' | 'INACTIVE';
}

export interface ProductInventory {
  productId: number;
  productName: string;
  sku: string;
  quantity: number;
  price: number;
  squareItemId?: string;
  lastUpdated: string;
}

export interface AdminSettings {
  showLiveInventory: boolean;
  autoRefreshInterval: number;
  displayMode: 'live' | 'manual';
}

export interface InventoryOptions {
  displayMode: 'live' | 'manual';
  forceRefresh?: boolean;
}

class ShopFinderService {
  /**
   * Get all store locations from Square
   */
  async getLocations(): Promise<{ success: boolean; data: StoreLocation[] }> {
    try {
      const response = await api.get('/api/shop-finder/locations');
      return response.data;
    } catch (error) {
      console.error('Error fetching locations:', error);
      throw error;
    }
  }

  /**
   * Get inventory for a specific location
   */
  async getLocationInventory(
    locationId: string, 
    options: InventoryOptions = { displayMode: 'live' }
  ): Promise<{ success: boolean; data: ProductInventory[] }> {
    try {
      const response = await api.get(`/api/shop-finder/locations/${locationId}/inventory`, {
        params: options
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching location inventory:', error);
      throw error;
    }
  }

  /**
   * Get admin settings for shop finder
   */
  async getAdminSettings(): Promise<{ success: boolean; data: AdminSettings }> {
    try {
      const response = await api.get('/api/admin/shop-finder/settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching admin settings:', error);
      throw error;
    }
  }

  /**
   * Update admin settings for shop finder
   */
  async updateAdminSettings(settings: AdminSettings): Promise<{ success: boolean; data: AdminSettings }> {
    try {
      const response = await api.put('/api/admin/shop-finder/settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating admin settings:', error);
      throw error;
    }
  }

  /**
   * Sync inventory with Square for a specific location
   */
  async syncLocationInventory(locationId: string): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.post(`/api/admin/shop-finder/locations/${locationId}/sync`);
      return response.data;
    } catch (error) {
      console.error('Error syncing location inventory:', error);
      throw error;
    }
  }

  /**
   * Get location details by ID
   */
  async getLocationById(locationId: string): Promise<{ success: boolean; data: StoreLocation }> {
    try {
      const response = await api.get(`/api/shop-finder/locations/${locationId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching location details:', error);
      throw error;
    }
  }

  /**
   * Search locations by address or coordinates
   */
  async searchLocations(query: {
    address?: string;
    latitude?: number;
    longitude?: number;
    radius?: number;
  }): Promise<{ success: boolean; data: StoreLocation[] }> {
    try {
      const response = await api.get('/api/shop-finder/locations/search', {
        params: query
      });
      return response.data;
    } catch (error) {
      console.error('Error searching locations:', error);
      throw error;
    }
  }

  /**
   * Get product availability across all locations
   */
  async getProductAvailability(productId: number): Promise<{
    success: boolean;
    data: Array<{
      locationId: string;
      locationName: string;
      quantity: number;
      price: number;
      lastUpdated: string;
    }>;
  }> {
    try {
      const response = await api.get(`/api/shop-finder/products/${productId}/availability`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product availability:', error);
      throw error;
    }
  }

  /**
   * Reserve product at a specific location
   */
  async reserveProduct(data: {
    locationId: string;
    productId: number;
    quantity: number;
    customerEmail: string;
    customerPhone?: string;
  }): Promise<{ success: boolean; data: { reservationId: string; expiresAt: string } }> {
    try {
      const response = await api.post('/api/shop-finder/reservations', data);
      return response.data;
    } catch (error) {
      console.error('Error reserving product:', error);
      throw error;
    }
  }

  /**
   * Get reservation details
   */
  async getReservation(reservationId: string): Promise<{
    success: boolean;
    data: {
      id: string;
      locationId: string;
      productId: number;
      quantity: number;
      status: 'active' | 'expired' | 'fulfilled' | 'cancelled';
      expiresAt: string;
      createdAt: string;
    };
  }> {
    try {
      const response = await api.get(`/api/shop-finder/reservations/${reservationId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching reservation:', error);
      throw error;
    }
  }

  /**
   * Cancel a reservation
   */
  async cancelReservation(reservationId: string): Promise<{ success: boolean }> {
    try {
      const response = await api.delete(`/api/shop-finder/reservations/${reservationId}`);
      return response.data;
    } catch (error) {
      console.error('Error cancelling reservation:', error);
      throw error;
    }
  }
}

export const shopFinderService = new ShopFinderService();
export default shopFinderService;
