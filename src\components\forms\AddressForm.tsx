import React, { useState, useEffect } from 'react';
import { useGeolocation } from '../../hooks/useGeolocation';
import LoadingSpinner from '../common/LoadingSpinner';
import { 
  MapPinIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline';

interface AddressData {
  firstName?: string;
  lastName?: string;
  streetAddress: string;
  streetAddress2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

interface AddressFormProps {
  initialData?: Partial<AddressData>;
  onSubmit: (data: AddressData) => void;
  onAddressChange?: (data: Partial<AddressData>) => void;
  showNameFields?: boolean;
  showPhoneField?: boolean;
  title?: string;
  submitButtonText?: string;
  loading?: boolean;
  className?: string;
}

const AddressForm: React.FC<AddressFormProps> = ({
  initialData = {},
  onSubmit,
  onAddressChange,
  showNameFields = true,
  showPhoneField = true,
  title = 'Address Information',
  submitButtonText = 'Save Address',
  loading = false,
  className = ''
}) => {
  const [formData, setFormData] = useState<AddressData>({
    firstName: initialData.firstName || '',
    lastName: initialData.lastName || '',
    streetAddress: initialData.streetAddress || '',
    streetAddress2: initialData.streetAddress2 || '',
    city: initialData.city || '',
    state: initialData.state || '',
    postalCode: initialData.postalCode || '',
    country: initialData.country || 'United States',
    phone: initialData.phone || ''
  });

  const [showGpsOption, setShowGpsOption] = useState(true);
  const [gpsUsed, setGpsUsed] = useState(false);

  const {
    getCurrentLocationAndAddress,
    loading: gpsLoading,
    error: gpsError,
    isSupported: gpsSupported,
    clearError,
    permission
  } = useGeolocation();

  // Handle form field changes
  const handleChange = (field: keyof AddressData, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    onAddressChange?.(updatedData);
  };

  // Handle GPS location request
  const handleUseCurrentLocation = async () => {
    clearError();
    
    try {
      const result = await getCurrentLocationAndAddress();
      
      if (result?.address) {
        const { address } = result;
        const updatedData = {
          ...formData,
          streetAddress: `${address.streetNumber || ''} ${address.streetName || ''}`.trim(),
          city: address.city || '',
          state: address.state || '',
          postalCode: address.postalCode || '',
          country: address.country || 'United States'
        };
        
        setFormData(updatedData);
        onAddressChange?.(updatedData);
        setGpsUsed(true);
        setShowGpsOption(false);
      }
    } catch (error) {
      console.error('Failed to get location:', error);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Validate required fields
  const isValid = formData.streetAddress && formData.city && formData.state && formData.postalCode;

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        
        {/* GPS Success Indicator */}
        {gpsUsed && (
          <div className="flex items-center text-green-600 text-sm">
            <CheckCircleIcon className="h-5 w-5 mr-1" />
            Location detected
          </div>
        )}
      </div>

      {/* GPS Location Option */}
      {showGpsOption && gpsSupported && permission !== 'denied' && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start">
            <MapPinIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                Use Current Location
              </h4>
              <p className="text-sm text-blue-700 mb-3">
                Allow location access to automatically fill your address
              </p>
              <button
                type="button"
                onClick={handleUseCurrentLocation}
                disabled={gpsLoading}
                className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {gpsLoading ? (
                  <>
                    <LoadingSpinner size="small" color="white" />
                    <span className="ml-2">Getting location...</span>
                  </>
                ) : (
                  <>
                    <MapPinIcon className="h-4 w-4 mr-2" />
                    Use My Location
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* GPS Error */}
      {gpsError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-900 mb-1">
                Location Access Error
              </h4>
              <p className="text-sm text-red-700">{gpsError}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Name Fields */}
        {showNameFields && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                First Name
              </label>
              <input
                type="text"
                id="firstName"
                value={formData.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter first name"
              />
            </div>
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                Last Name
              </label>
              <input
                type="text"
                id="lastName"
                value={formData.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter last name"
              />
            </div>
          </div>
        )}

        {/* Street Address */}
        <div>
          <label htmlFor="streetAddress" className="block text-sm font-medium text-gray-700 mb-1">
            Street Address *
          </label>
          <input
            type="text"
            id="streetAddress"
            value={formData.streetAddress}
            onChange={(e) => handleChange('streetAddress', e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Enter street address"
          />
        </div>

        {/* Street Address 2 */}
        <div>
          <label htmlFor="streetAddress2" className="block text-sm font-medium text-gray-700 mb-1">
            Apartment, suite, etc. (optional)
          </label>
          <input
            type="text"
            id="streetAddress2"
            value={formData.streetAddress2}
            onChange={(e) => handleChange('streetAddress2', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Apartment, suite, unit, etc."
          />
        </div>

        {/* City, State, ZIP */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
              City *
            </label>
            <input
              type="text"
              id="city"
              value={formData.city}
              onChange={(e) => handleChange('city', e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter city"
            />
          </div>
          <div>
            <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
              State *
            </label>
            <input
              type="text"
              id="state"
              value={formData.state}
              onChange={(e) => handleChange('state', e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter state"
            />
          </div>
          <div>
            <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 mb-1">
              ZIP Code *
            </label>
            <input
              type="text"
              id="postalCode"
              value={formData.postalCode}
              onChange={(e) => handleChange('postalCode', e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter ZIP code"
            />
          </div>
        </div>

        {/* Country */}
        <div>
          <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
            Country *
          </label>
          <select
            id="country"
            value={formData.country}
            onChange={(e) => handleChange('country', e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="United States">United States</option>
            <option value="Canada">Canada</option>
          </select>
        </div>

        {/* Phone Field */}
        {showPhoneField && (
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              value={formData.phone}
              onChange={(e) => handleChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter phone number"
            />
          </div>
        )}

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={!isValid || loading}
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {loading ? (
              <>
                <LoadingSpinner size="small" color="white" />
                <span className="ml-2">Saving...</span>
              </>
            ) : (
              submitButtonText
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddressForm;
