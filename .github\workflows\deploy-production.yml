name: Deploy to Production

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: 'false'

env:
  NODE_VERSION: '18.x'
  DEPLOYMENT_ENVIRONMENT: production

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: npm run lint
      
    - name: Run type checking
      run: npm run type-check
      
    - name: Run tests
      run: npm run test
      
    - name: Test build
      run: npm run build:check

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: test
    if: success() || github.event.inputs.force_deploy == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --production
      
    - name: Create production environment file
      run: |
        echo "NODE_ENV=production" > .env.production.ci
        echo "VITE_API_URL=${{ secrets.VITE_API_URL }}" >> .env.production.ci
        echo "VITE_SQUARE_APPLICATION_ID=${{ secrets.VITE_SQUARE_APPLICATION_ID }}" >> .env.production.ci
        echo "VITE_SQUARE_ENVIRONMENT=production" >> .env.production.ci
        
    - name: Build frontend
      run: npm run build:prod
      env:
        VITE_API_URL: ${{ secrets.VITE_API_URL }}
        VITE_SQUARE_APPLICATION_ID: ${{ secrets.VITE_SQUARE_APPLICATION_ID }}
        
    - name: Build admin panel
      run: npm run build:admin
      
    - name: Create deployment package
      run: |
        mkdir -p deployment-package
        cp -r dist deployment-package/
        cp -r dist-admin deployment-package/
        cp -r server deployment-package/
        cp -r scripts deployment-package/
        cp -r public deployment-package/
        cp package.json deployment-package/
        cp ecosystem.config.js deployment-package/
        cp .env.production deployment-package/
        
    - name: Upload deployment artifact
      uses: actions/upload-artifact@v4
      with:
        name: deployment-package
        path: deployment-package/
        retention-days: 30

  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    environment: production
    
    steps:
    - name: Download deployment artifact
      uses: actions/download-artifact@v4
      with:
        name: deployment-package
        path: deployment-package/
        
    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
        
    - name: Add server to known hosts
      run: |
        ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts
        
    - name: Create deployment directory
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "
          sudo mkdir -p /var/www/nirvana-organics/releases/${{ github.sha }}
          sudo chown -R ${{ secrets.SSH_USER }}:${{ secrets.SSH_USER }} /var/www/nirvana-organics
        "
        
    - name: Upload files to server
      run: |
        rsync -avz --progress deployment-package/ \
          ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }}:/var/www/nirvana-organics/releases/${{ github.sha }}/
          
    - name: Create production environment
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "
          cd /var/www/nirvana-organics/releases/${{ github.sha }}
          cat > .env << EOF
          NODE_ENV=production
          PORT=5000
          DB_HOST=${{ secrets.DB_HOST }}
          DB_PORT=${{ secrets.DB_PORT }}
          DB_NAME=${{ secrets.DB_NAME }}
          DB_USER=${{ secrets.DB_USER }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          JWT_REFRESH_SECRET=${{ secrets.JWT_REFRESH_SECRET }}
          SESSION_SECRET=${{ secrets.SESSION_SECRET }}
          SQUARE_APPLICATION_ID=${{ secrets.SQUARE_APPLICATION_ID }}
          SQUARE_ACCESS_TOKEN=${{ secrets.SQUARE_ACCESS_TOKEN }}
          SQUARE_LOCATION_ID=${{ secrets.SQUARE_LOCATION_ID }}
          SQUARE_ENVIRONMENT=production
          SQUARE_WEBHOOK_SIGNATURE_KEY=${{ secrets.SQUARE_WEBHOOK_SIGNATURE_KEY }}
          FRONTEND_URL=https://shopnirvanaorganics.com
          BACKEND_URL=https://shopnirvanaorganics.com
          API_BASE_URL=https://shopnirvanaorganics.com/api
          CORS_ORIGIN=https://shopnirvanaorganics.com,https://www.shopnirvanaorganics.com
          EMAIL_HOST=${{ secrets.EMAIL_HOST }}
          EMAIL_PORT=${{ secrets.EMAIL_PORT }}
          EMAIL_USER=${{ secrets.EMAIL_USER }}
          EMAIL_PASS=${{ secrets.EMAIL_PASS }}
          EMAIL_FROM=${{ secrets.EMAIL_FROM }}
          VAPID_PUBLIC_KEY=${{ secrets.VAPID_PUBLIC_KEY }}
          VAPID_PRIVATE_KEY=${{ secrets.VAPID_PRIVATE_KEY }}
          VAPID_EMAIL=${{ secrets.VAPID_EMAIL }}
          EOF
        "
        
    - name: Install dependencies and run migrations
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "
          cd /var/www/nirvana-organics/releases/${{ github.sha }}
          npm ci --production
          npm run migrate:prod
        "
        
    - name: Test Square configuration
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "
          cd /var/www/nirvana-organics/releases/${{ github.sha }}
          if [ -f scripts/test-square-production.js ]; then
            node scripts/test-square-production.js
          fi
        "
        
    - name: Create symlink and restart services
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "
          # Create backup of current deployment
          if [ -L /var/www/nirvana-organics/current ]; then
            cp -P /var/www/nirvana-organics/current /var/www/nirvana-organics/previous
          fi
          
          # Update symlink to new release
          ln -sfn /var/www/nirvana-organics/releases/${{ github.sha }} /var/www/nirvana-organics/current
          
          # Restart PM2 processes
          cd /var/www/nirvana-organics/current
          pm2 reload ecosystem.config.js --env production || pm2 start ecosystem.config.js --env production
          pm2 save
        "
        
    - name: Health check
      run: |
        sleep 30
        curl -f https://shopnirvanaorganics.com/health || exit 1
        
    - name: Cleanup old releases
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "
          cd /var/www/nirvana-organics/releases
          ls -t | tail -n +6 | xargs rm -rf
        "

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [test, build, deploy]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Deployment to production successful!"
        echo "🌐 Application: https://shopnirvanaorganics.com"
        echo "📊 Admin: https://shopnirvanaorganics.com/admin"
        
    - name: Notify failure
      if: needs.deploy.result == 'failure'
      run: |
        echo "❌ Deployment to production failed!"
        echo "Please check the logs and fix any issues."
