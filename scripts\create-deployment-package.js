#!/usr/bin/env node

/**
 * Backend Deployment Package Creator
 * Creates a production-ready deployment package for VPS upload
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔧${colors.reset} ${msg}`)
};

// Configuration
const PACKAGE_NAME = 'nirvana-organics-backend-production';
const PACKAGE_VERSION = '1.0.0';
const OUTPUT_DIR = './deployment-package';
const ARCHIVE_NAME = `${PACKAGE_NAME}-v${PACKAGE_VERSION}.tar.gz`;

// Files and directories to include in production package
const INCLUDE_PATTERNS = [
  'server/**/*',
  'scripts/**/*',
  'public/**/*',
  'uploads/**/*',
  'package.json',
  'package-lock.json',
  'ecosystem.config.js',
  'deployment.config.js',
  '.env.production',
  'README.md',
  'PRODUCTION_*.md',
  'SQUARE_*.md',
  'SSL_*.md'
];

// Files and directories to exclude from production package
const EXCLUDE_PATTERNS = [
  'node_modules/**/*',
  '.git/**/*',
  '.github/**/*',
  'src/**/*',
  'tests/**/*',
  'test/**/*',
  '__tests__/**/*',
  'coverage/**/*',
  '.nyc_output/**/*',
  'dist/**/*',
  'build/**/*',
  '.env',
  '.env.local',
  '.env.development',
  '.env.staging',
  '.env.test',
  '*.log',
  'logs/**/*',
  'tmp/**/*',
  'temp/**/*',
  '.DS_Store',
  'Thumbs.db',
  '*.swp',
  '*.swo',
  '.vscode/**/*',
  '.idea/**/*',
  'vite.config.ts',
  'vite.admin.config.ts',
  'vitest.config.ts',
  'tsconfig.*.json',
  'tailwind.config.js',
  'postcss.config.cjs',
  'index.html',
  'admin.html',
  'deployment-package/**/*'
];

/**
 * Check if file should be included
 */
function shouldIncludeFile(filePath) {
  const relativePath = path.relative('.', filePath);
  
  // Check exclude patterns first
  for (const pattern of EXCLUDE_PATTERNS) {
    if (minimatch(relativePath, pattern)) {
      return false;
    }
  }
  
  // Check include patterns
  for (const pattern of INCLUDE_PATTERNS) {
    if (minimatch(relativePath, pattern)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Simple minimatch implementation
 */
function minimatch(str, pattern) {
  const regexPattern = pattern
    .replace(/\*\*/g, '.*')
    .replace(/\*/g, '[^/]*')
    .replace(/\?/g, '[^/]');
  
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(str);
}

/**
 * Copy files recursively
 */
function copyFiles(srcDir, destDir, basePath = '') {
  if (!fs.existsSync(srcDir)) {
    return;
  }

  const items = fs.readdirSync(srcDir);
  
  for (const item of items) {
    const srcPath = path.join(srcDir, item);
    const destPath = path.join(destDir, item);
    const relativePath = path.join(basePath, item);
    
    if (fs.statSync(srcPath).isDirectory()) {
      if (shouldIncludeFile(relativePath + '/')) {
        fs.mkdirSync(destPath, { recursive: true });
        copyFiles(srcPath, destPath, relativePath);
      }
    } else {
      if (shouldIncludeFile(relativePath)) {
        fs.mkdirSync(path.dirname(destPath), { recursive: true });
        fs.copyFileSync(srcPath, destPath);
        log.info(`Copied: ${relativePath}`);
      }
    }
  }
}

/**
 * Create production package.json
 */
function createProductionPackageJson(destDir) {
  log.step('Creating production package.json...');
  
  const packageJsonPath = './package.json';
  if (!fs.existsSync(packageJsonPath)) {
    log.error('package.json not found');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Create production-optimized package.json
  const productionPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    description: packageJson.description,
    main: packageJson.main || 'server/index.js',
    scripts: {
      start: 'NODE_ENV=production node server/index.js',
      'start:prod': 'NODE_ENV=production node server/index.js',
      'migrate:prod': 'NODE_ENV=production node scripts/run-migrations.js',
      'seed:prod': 'NODE_ENV=production node scripts/seed-production-data.js',
      'setup:database': 'node scripts/setup-production-database.js',
      'setup:services': 'node scripts/setup-third-party-services.js',
      'setup:ssl': 'chmod +x scripts/setup-ssl-certificates.sh && sudo ./scripts/setup-ssl-certificates.sh',
      'verify:ssl': 'node scripts/verify-ssl-setup.js',
      'validate:env': 'node scripts/validate-env.js',
      'test:database': 'node scripts/test-database-connection.js',
      'generate:secrets': 'node scripts/generate-production-secrets.js',
      'deploy': 'chmod +x scripts/deploy-production.sh && ./scripts/deploy-production.sh',
      'status': 'pm2 status',
      'logs': 'pm2 logs',
      'restart': 'pm2 restart all',
      'stop': 'pm2 stop all'
    },
    dependencies: packageJson.dependencies,
    engines: packageJson.engines,
    author: packageJson.author,
    license: packageJson.license,
    keywords: packageJson.keywords
  };
  
  fs.writeFileSync(
    path.join(destDir, 'package.json'),
    JSON.stringify(productionPackageJson, null, 2)
  );
  
  log.success('Production package.json created');
  return true;
}

/**
 * Create deployment instructions
 */
function createDeploymentInstructions(destDir) {
  log.step('Creating deployment instructions...');
  
  const instructions = `# Nirvana Organics Backend - VPS Deployment Instructions

## 📦 Package Contents

This package contains the production-ready backend for Nirvana Organics e-commerce platform.

### Included Files:
- \`server/\` - Backend application code
- \`scripts/\` - Deployment and setup scripts
- \`public/\` - Static assets
- \`uploads/\` - Upload directories
- \`.env.production\` - Production environment template
- \`ecosystem.config.js\` - PM2 configuration
- Configuration and documentation files

## 🚀 Quick Deployment

### 1. Extract Package
\`\`\`bash
tar -xzf ${ARCHIVE_NAME}
cd ${PACKAGE_NAME}
\`\`\`

### 2. Install Dependencies
\`\`\`bash
npm ci --production
\`\`\`

### 3. Configure Environment
\`\`\`bash
# Copy and edit production environment
cp .env.production .env
nano .env  # Update with your production values
\`\`\`

### 4. Deploy
\`\`\`bash
# Run automated deployment
npm run deploy
\`\`\`

## 📋 Manual Setup (if needed)

### Prerequisites
- Node.js 18+
- MySQL/MariaDB
- Nginx
- PM2

### Step-by-Step Setup
1. \`npm run setup:database\` - Setup database
2. \`npm run setup:services\` - Validate services
3. \`npm run setup:ssl\` - Setup SSL certificates
4. \`npm run migrate:prod\` - Run migrations
5. \`npm start\` - Start application

## 📚 Documentation

- \`PRODUCTION_DEPLOYMENT_SUMMARY.md\` - Complete deployment guide
- \`PRODUCTION_CONFIGURATION_CHECKLIST.md\` - Configuration checklist
- \`SQUARE_PRODUCTION_SETUP.md\` - Square payment setup
- \`SSL_SETUP_GUIDE.md\` - SSL certificate setup

## 🆘 Support

For issues, check the troubleshooting guides in the documentation files.

---
Package created: ${new Date().toISOString()}
Version: ${PACKAGE_VERSION}
`;

  fs.writeFileSync(path.join(destDir, 'DEPLOYMENT_INSTRUCTIONS.md'), instructions);
  log.success('Deployment instructions created');
}

/**
 * Create deployment package
 */
async function createDeploymentPackage() {
  console.log(`${colors.bright}📦 Creating Backend Deployment Package${colors.reset}`);
  console.log('='.repeat(50));

  try {
    // Clean and create output directory
    log.step('Preparing output directory...');
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });

    const packageDir = path.join(OUTPUT_DIR, PACKAGE_NAME);
    fs.mkdirSync(packageDir, { recursive: true });

    // Copy production files
    log.step('Copying production files...');
    
    // Copy individual files that match include patterns
    const rootFiles = fs.readdirSync('.');
    for (const file of rootFiles) {
      const filePath = `./${file}`;
      if (fs.statSync(filePath).isFile() && shouldIncludeFile(file)) {
        fs.copyFileSync(filePath, path.join(packageDir, file));
        log.info(`Copied: ${file}`);
      }
    }

    // Copy directories
    const directories = ['server', 'scripts', 'public', 'uploads'];
    for (const dir of directories) {
      if (fs.existsSync(dir)) {
        const destDir = path.join(packageDir, dir);
        fs.mkdirSync(destDir, { recursive: true });
        copyFiles(dir, destDir, dir);
      }
    }

    // Create production package.json
    createProductionPackageJson(packageDir);

    // Create deployment instructions
    createDeploymentInstructions(packageDir);

    // Create archive
    log.step('Creating compressed archive...');
    
    const archivePath = path.join(OUTPUT_DIR, ARCHIVE_NAME);
    const output = fs.createWriteStream(archivePath);
    const archive = archiver('tar', { gzip: true });

    return new Promise((resolve, reject) => {
      output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        log.success(`Archive created: ${ARCHIVE_NAME} (${sizeInMB} MB)`);
        
        console.log('');
        log.success('🎉 Deployment package created successfully!');
        console.log('');
        log.info('Package Details:');
        console.log(`  Name: ${PACKAGE_NAME}`);
        console.log(`  Version: ${PACKAGE_VERSION}`);
        console.log(`  Archive: ${archivePath}`);
        console.log(`  Size: ${sizeInMB} MB`);
        console.log('');
        log.step('Next Steps:');
        console.log('1. Upload the archive to your VPS');
        console.log('2. Extract and follow DEPLOYMENT_INSTRUCTIONS.md');
        console.log('3. Configure .env.production with your values');
        console.log('4. Run the deployment script');
        
        resolve(true);
      });

      output.on('error', reject);
      archive.on('error', reject);

      archive.pipe(output);
      archive.directory(packageDir, PACKAGE_NAME);
      archive.finalize();
    });

  } catch (error) {
    log.error(`Package creation failed: ${error.message}`);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  createDeploymentPackage()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      log.error(`Deployment package creation failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = {
  createDeploymentPackage,
  PACKAGE_NAME,
  PACKAGE_VERSION,
  OUTPUT_DIR,
  ARCHIVE_NAME
};
