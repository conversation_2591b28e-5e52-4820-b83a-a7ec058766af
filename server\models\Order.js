const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'order_number'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true, // Allow null for guest orders
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  guestInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'guest_info'
  },
  guestTrackingToken: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'guest_tracking_token'
  },
  items: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  billingAddress: {
    type: DataTypes.JSON,
    allowNull: false,
    field: 'billing_address'
  },
  shippingAddress: {
    type: DataTypes.JSON,
    allowNull: false,
    field: 'shipping_address'
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  tax: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  shipping: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  discount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  total: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },
  paymentStatus: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded'),
    defaultValue: 'pending',
    field: 'payment_status'
  },
  paymentMethod: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'payment_method'
  },
  paymentId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'payment_id'
  },
  squarePaymentId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'square_payment_id'
  },
  trackingNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'tracking_number'
  },
  shippingCarrier: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'USPS',
    field: 'shipping_carrier'
  },
  verificationStatus: {
    type: DataTypes.ENUM('pending', 'verified', 'rejected'),
    defaultValue: 'pending',
    field: 'verification_status'
  },
  verificationMethod: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'verification_method'
  },
  verificationNotes: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'verification_notes'
  },
  verifiedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'verified_by'
  },
  verifiedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'verified_at'
  },
  verificationContactAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'verification_contact_attempts'
  },
  lastVerificationContact: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_verification_contact'
  },
  verificationContactMethod: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'verification_contact_method'
  },
  shippingLabelGenerated: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'shipping_label_generated'
  },
  shippingLabelUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'shipping_label_url'
  },
  shippingLabelId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'shipping_label_id'
  },
  shippedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'shipped_at'
  },
  deliveredAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'delivered_at'
  },
  trackingLastUpdated: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'tracking_last_updated'
  },
  trackingNotes: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'tracking_notes'
  },
  invoiceId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'invoice_id'
  },
  invoiceUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'invoice_url'
  },
  shippingCarrier: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'shipping_carrier'
  },
  estimatedDelivery: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'estimated_delivery'
  },
  actualDelivery: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'actual_delivery'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  adminNotes: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'admin_notes'
  },
  couponCode: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'coupon_code'
  },
  refundAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'refund_amount',
    validate: {
      min: 0
    }
  },
  refundReason: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'refund_reason'
  },
  statusHistory: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    field: 'status_history'
  }
}, {
  tableName: 'orders',
  hooks: {
    beforeCreate: async (order) => {
      if (!order.orderNumber) {
        const count = await Order.count();
        order.orderNumber = `NO-${Date.now()}-${(count + 1).toString().padStart(4, '0')}`;
      }
    },
    beforeUpdate: (order) => {
      if (order.changed('status')) {
        const statusHistory = order.statusHistory || [];
        statusHistory.push({
          status: order.status,
          date: new Date(),
          note: `Status changed to ${order.status}`
        });
        order.statusHistory = statusHistory;
      }
    }
  }
});

// Instance methods
Order.prototype.calculateTotals = function() {
  const items = this.items || [];
  this.subtotal = items.reduce((sum, item) => sum + item.total, 0);
  this.total = this.subtotal + this.tax + this.shipping - this.discount;
  return this;
};

Order.prototype.canBeCancelled = function() {
  return ['pending', 'processing'].includes(this.status);
};

Order.prototype.canBeRefunded = function() {
  return ['delivered'].includes(this.status) && this.paymentStatus === 'paid';
};

Order.prototype.getBillingAddressFormatted = function() {
  const addr = this.billingAddress;
  return `${addr.firstName} ${addr.lastName}\n${addr.address1}${addr.address2 ? '\n' + addr.address2 : ''}\n${addr.city}, ${addr.state} ${addr.zipCode}\n${addr.country}`;
};

Order.prototype.getShippingAddressFormatted = function() {
  const addr = this.shippingAddress;
  return `${addr.firstName} ${addr.lastName}\n${addr.address1}${addr.address2 ? '\n' + addr.address2 : ''}\n${addr.city}, ${addr.state} ${addr.zipCode}\n${addr.country}`;
};

// Static methods
Order.getStatistics = async function(startDate, endDate) {
  const whereClause = {};
  if (startDate && endDate) {
    whereClause.createdAt = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  }

  const result = await Order.findAll({
    where: whereClause,
    attributes: [
      [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
      [sequelize.fn('SUM', sequelize.col('total')), 'totalRevenue'],
      [sequelize.fn('AVG', sequelize.col('total')), 'averageOrderValue']
    ]
  });

  return result[0];
};

module.exports = Order;
