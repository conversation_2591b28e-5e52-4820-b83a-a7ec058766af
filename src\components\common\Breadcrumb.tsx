import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
  const location = useLocation();

  // Always include Home as the first item if not already present
  const breadcrumbItems = items[0]?.label !== 'Home' 
    ? [{ label: 'Home', href: '/' }, ...items]
    : items;

  return (
    <nav className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const isCurrent = item.current || isLast;

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" aria-hidden="true" />
              )}
              
              {item.href && !isCurrent ? (
                <Link
                  to={item.href}
                  className="flex items-center hover:text-primary-600 transition-colors"
                  aria-current={isCurrent ? 'page' : undefined}
                >
                  {index === 0 && (
                    <HomeIcon className="h-4 w-4 mr-1" aria-hidden="true" />
                  )}
                  <span className="truncate max-w-[200px]">{item.label}</span>
                </Link>
              ) : (
                <span
                  className={`flex items-center ${
                    isCurrent 
                      ? 'text-gray-900 font-medium' 
                      : 'text-gray-600'
                  }`}
                  aria-current={isCurrent ? 'page' : undefined}
                >
                  {index === 0 && (
                    <HomeIcon className="h-4 w-4 mr-1" aria-hidden="true" />
                  )}
                  <span className="truncate max-w-[200px]">{item.label}</span>
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

// Utility function to generate breadcrumbs from pathname
export const generateBreadcrumbsFromPath = (pathname: string, customLabels?: Record<string, string>): BreadcrumbItem[] => {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  // Default labels for common paths
  const defaultLabels: Record<string, string> = {
    shop: 'Shop',
    product: 'Product',
    cart: 'Shopping Cart',
    checkout: 'Checkout',
    profile: 'My Account',
    orders: 'My Orders',
    about: 'About Us',
    contact: 'Contact',
    faq: 'FAQ',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    shipping: 'Shipping Info',
    returns: 'Returns',
    login: 'Sign In',
    register: 'Create Account',
    admin: 'Admin Dashboard',
    ...customLabels
  };

  segments.forEach((segment, index) => {
    const path = '/' + segments.slice(0, index + 1).join('/');
    const label = defaultLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
    
    breadcrumbs.push({
      label,
      href: path,
      current: index === segments.length - 1
    });
  });

  return breadcrumbs;
};

// Hook to generate breadcrumbs based on current location
export const useBreadcrumbs = (customItems?: BreadcrumbItem[], customLabels?: Record<string, string>): BreadcrumbItem[] => {
  const location = useLocation();

  if (customItems) {
    return customItems;
  }

  return generateBreadcrumbsFromPath(location.pathname, customLabels);
};

export default Breadcrumb;
