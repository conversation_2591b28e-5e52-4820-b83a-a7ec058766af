import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  PaperAirplaneIcon,
  ChartBarIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import LoadingSpinner from '../common/LoadingSpinner';
import AdminDataTable, { TableColumn, TableAction } from './AdminDataTable';

interface Campaign {
  id: number;
  name: string;
  description: string;
  type: 'email' | 'push' | 'sms' | 'in_app';
  status: 'draft' | 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';
  targetAudience: 'all' | 'customers' | 'subscribers' | 'segment' | 'custom';
  totalRecipients: number;
  sentCount: number;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
  createdAt: string;
  creator: {
    firstName: string;
    lastName: string;
  };
}

interface CampaignFilters {
  search: string;
  type: string;
  status: string;
}

const CampaignManager: React.FC = () => {
  const dispatch = useAppDispatch();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<CampaignFilters>({
    search: '',
    type: 'all',
    status: 'all'
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCampaigns: 0,
    limit: 20
  });

  useEffect(() => {
    fetchCampaigns();
  }, [filters, pagination.currentPage]);

  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      // Mock API call - replace with actual API
      const mockCampaigns: Campaign[] = [
        {
          id: 1,
          name: 'Welcome Series',
          description: 'Automated welcome email series for new customers',
          type: 'email',
          status: 'active',
          targetAudience: 'customers',
          totalRecipients: 1250,
          sentCount: 1250,
          deliveredCount: 1198,
          openedCount: 456,
          clickedCount: 89,
          createdAt: '2024-01-15T10:00:00Z',
          creator: {
            firstName: 'Admin',
            lastName: 'User'
          }
        },
        {
          id: 2,
          name: 'Monthly Newsletter',
          description: 'Monthly product updates and promotions',
          type: 'email',
          status: 'completed',
          targetAudience: 'subscribers',
          totalRecipients: 2890,
          sentCount: 2890,
          deliveredCount: 2756,
          openedCount: 1234,
          clickedCount: 345,
          createdAt: '2024-01-01T09:00:00Z',
          creator: {
            firstName: 'Marketing',
            lastName: 'Team'
          }
        },
        {
          id: 3,
          name: 'Abandoned Cart Recovery',
          description: 'Recover abandoned shopping carts',
          type: 'email',
          status: 'draft',
          targetAudience: 'segment',
          totalRecipients: 0,
          sentCount: 0,
          deliveredCount: 0,
          openedCount: 0,
          clickedCount: 0,
          createdAt: '2024-01-20T14:30:00Z',
          creator: {
            firstName: 'Admin',
            lastName: 'User'
          }
        }
      ];

      setCampaigns(mockCampaigns);
      setPagination(prev => ({
        ...prev,
        totalCampaigns: mockCampaigns.length,
        totalPages: Math.ceil(mockCampaigns.length / prev.limit)
      }));
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      dispatch(addToast({
        type: 'error',
        message: 'Failed to fetch campaigns'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleCampaignAction = async (action: string, campaign: Campaign) => {
    try {
      switch (action) {
        case 'send':
          if (campaign.status !== 'draft') {
            dispatch(addToast({
              type: 'error',
              message: 'Only draft campaigns can be sent'
            }));
            return;
          }
          
          // Mock API call
          dispatch(addToast({
            type: 'success',
            message: `Campaign "${campaign.name}" sent successfully`
          }));
          fetchCampaigns();
          break;

        case 'edit':
          console.log('Edit campaign:', campaign.id);
          break;

        case 'view':
          console.log('View campaign:', campaign.id);
          break;

        case 'analytics':
          console.log('View analytics for campaign:', campaign.id);
          break;

        case 'delete':
          if (campaign.status === 'active' || campaign.status === 'completed') {
            dispatch(addToast({
              type: 'error',
              message: 'Cannot delete active or completed campaigns'
            }));
            return;
          }

          // Mock API call
          dispatch(addToast({
            type: 'success',
            message: `Campaign "${campaign.name}" deleted successfully`
          }));
          fetchCampaigns();
          break;
      }
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        message: 'Action failed. Please try again.'
      }));
    }
  };

  const handleFilterChange = (key: keyof CampaignFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      scheduled: 'bg-blue-100 text-blue-800',
      active: 'bg-green-100 text-green-800',
      paused: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-purple-100 text-purple-800',
      cancelled: 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[status as keyof typeof statusColors]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getTypeBadge = (type: string) => {
    const typeColors = {
      email: 'bg-blue-100 text-blue-800',
      push: 'bg-green-100 text-green-800',
      sms: 'bg-yellow-100 text-yellow-800',
      in_app: 'bg-purple-100 text-purple-800'
    };

    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeColors[type as keyof typeof typeColors]}`}>
        {type.toUpperCase()}
      </span>
    );
  };

  const columns: TableColumn<Campaign>[] = [
    {
      key: 'name',
      title: 'Campaign',
      render: (_, record) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{record.name}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
          <div className="flex items-center space-x-2 mt-1">
            {getTypeBadge(record.type)}
            {getStatusBadge(record.status)}
          </div>
        </div>
      )
    },
    {
      key: 'targetAudience',
      title: 'Audience',
      render: (value) => (
        <span className="text-sm text-gray-900 capitalize">
          {value.replace('_', ' ')}
        </span>
      )
    },
    {
      key: 'metrics',
      title: 'Performance',
      render: (_, record) => {
        const openRate = record.deliveredCount > 0 ? (record.openedCount / record.deliveredCount * 100).toFixed(1) : '0';
        const clickRate = record.openedCount > 0 ? (record.clickedCount / record.openedCount * 100).toFixed(1) : '0';
        
        return (
          <div className="text-sm">
            <div className="text-gray-900">
              {record.totalRecipients.toLocaleString()} recipients
            </div>
            <div className="text-gray-500">
              {openRate}% open • {clickRate}% click
            </div>
          </div>
        );
      }
    },
    {
      key: 'creator',
      title: 'Created By',
      render: (_, record) => (
        <div className="text-sm">
          <div className="text-gray-900">
            {record.creator.firstName} {record.creator.lastName}
          </div>
          <div className="text-gray-500">
            {new Date(record.createdAt).toLocaleDateString()}
          </div>
        </div>
      )
    }
  ];

  const actions: TableAction<Campaign>[] = [
    {
      label: 'View Details',
      onClick: (campaign) => handleCampaignAction('view', campaign),
      icon: EyeIcon
    },
    {
      label: 'Edit Campaign',
      onClick: (campaign) => handleCampaignAction('edit', campaign),
      icon: PencilIcon,
      disabled: (campaign) => campaign.status === 'completed'
    },
    {
      label: 'Send Campaign',
      onClick: (campaign) => handleCampaignAction('send', campaign),
      icon: PaperAirplaneIcon,
      variant: 'primary',
      disabled: (campaign) => campaign.status !== 'draft'
    },
    {
      label: 'View Analytics',
      onClick: (campaign) => handleCampaignAction('analytics', campaign),
      icon: ChartBarIcon,
      disabled: (campaign) => campaign.totalRecipients === 0
    },
    {
      label: 'Delete Campaign',
      onClick: (campaign) => handleCampaignAction('delete', campaign),
      icon: TrashIcon,
      variant: 'danger',
      disabled: (campaign) => campaign.status === 'active' || campaign.status === 'completed'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Campaign Management</h2>
          <p className="text-gray-600">Create and manage email campaigns and marketing automation</p>
        </div>
        <button
          onClick={() => console.log('Create new campaign')}
          className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Campaign
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-primary-600 hover:text-primary-900 flex items-center"
            >
              <FunnelIcon className="h-4 w-4 mr-1" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </button>
          </div>
        </div>

        {showFilters && (
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  placeholder="Search campaigns..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">All Types</option>
                  <option value="email">Email</option>
                  <option value="push">Push Notification</option>
                  <option value="sms">SMS</option>
                  <option value="in_app">In-App</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="active">Active</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Campaigns Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <AdminDataTable
          columns={columns}
          data={campaigns}
          actions={actions}
          loading={loading}
          emptyText="No campaigns found"
          pagination={pagination}
          onPageChange={(page) => setPagination(prev => ({ ...prev, currentPage: page }))}
        />
      </div>
    </div>
  );
};

export default CampaignManager;
