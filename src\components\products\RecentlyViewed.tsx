import React from 'react';
import { Link } from 'react-router-dom';
import { useRecentlyViewed } from '../../hooks/useRecentlyViewed';
import { ClockIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface RecentlyViewedProps {
  className?: string;
  showTitle?: boolean;
  maxItems?: number;
}

const RecentlyViewed: React.FC<RecentlyViewedProps> = ({ 
  className = '', 
  showTitle = true,
  maxItems = 5 
}) => {
  const { recentlyViewed, removeFromRecentlyViewed, clearRecentlyViewed } = useRecentlyViewed();

  if (recentlyViewed.length === 0) {
    return null;
  }

  const displayItems = recentlyViewed.slice(0, maxItems);

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      {showTitle && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <ClockIcon className="h-5 w-5 text-gray-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Recently Viewed</h3>
          </div>
          <button
            onClick={clearRecentlyViewed}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            Clear All
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        {displayItems.map((product) => (
          <div key={product.id} className="relative group">
            <Link to={`/product/${product.slug}`} className="block">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-2">
                <img
                  src={product.images[0]?.url || '/images/placeholder-product.jpg'}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                  onError={(e) => {
                    e.currentTarget.src = '/images/placeholder-product.jpg';
                  }}
                />
              </div>
              <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                {product.name}
              </h4>
              <p className="text-sm font-semibold text-primary-600">
                ${product.price.toFixed(2)}
                {product.comparePrice && product.comparePrice > product.price && (
                  <span className="ml-2 text-xs text-gray-500 line-through">
                    ${product.comparePrice.toFixed(2)}
                  </span>
                )}
              </p>
            </Link>
            
            {/* Remove button */}
            <button
              onClick={(e) => {
                e.preventDefault();
                removeFromRecentlyViewed(product.id);
              }}
              className="absolute top-2 right-2 p-1 bg-white bg-opacity-80 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-opacity-100"
              title="Remove from recently viewed"
            >
              <XMarkIcon className="h-4 w-4 text-gray-600" />
            </button>
          </div>
        ))}
      </div>

      {recentlyViewed.length > maxItems && (
        <div className="mt-4 text-center">
          <Link
            to="/shop"
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            View All Products →
          </Link>
        </div>
      )}
    </div>
  );
};

export default RecentlyViewed;
