#!/usr/bin/env node

/**
 * Quick Deployment Test
 * Tests the key components before running full deployment
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🚀 Quick Deployment Test\n');

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

function runTest(testName, testFn) {
  try {
    log(`🔍 ${testName}...`, 'info');
    const result = testFn();
    if (result) {
      log(`✅ ${testName} - PASSED`, 'success');
    } else {
      log(`⚠️  ${testName} - WARNING`, 'warning');
    }
    return result;
  } catch (error) {
    log(`❌ ${testName} - FAILED: ${error.message}`, 'error');
    return false;
  }
}

// Test 1: Package.json validity
function testPackageJson() {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check for duplicate keys (this was the issue)
  const scripts = packageJson.scripts;
  const scriptKeys = Object.keys(scripts);
  const uniqueKeys = [...new Set(scriptKeys)];
  
  if (scriptKeys.length !== uniqueKeys.length) {
    log('   Found duplicate script keys', 'warning');
    return false;
  }
  
  // Check for required scripts
  const requiredScripts = ['build:frontend', 'deploy:fullstack'];
  const missingScripts = requiredScripts.filter(script => !scripts[script]);
  
  if (missingScripts.length > 0) {
    log(`   Missing scripts: ${missingScripts.join(', ')}`, 'warning');
    return false;
  }
  
  return true;
}

// Test 2: Dependencies check
function testDependencies() {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check that old react-google-login is removed
  if (packageJson.dependencies['react-google-login']) {
    log('   Old react-google-login still present', 'warning');
    return false;
  }
  
  // Check that modern @react-oauth/google exists
  if (!packageJson.dependencies['@react-oauth/google']) {
    log('   Modern @react-oauth/google not found', 'warning');
    return false;
  }
  
  // Check React version
  const reactVersion = packageJson.dependencies.react;
  if (!reactVersion || !reactVersion.includes('18')) {
    log(`   React version may be incompatible: ${reactVersion}`, 'warning');
    return false;
  }
  
  return true;
}

// Test 3: Node modules check
function testNodeModules() {
  if (!fs.existsSync('node_modules')) {
    log('   node_modules not found - need to install dependencies', 'warning');
    return false;
  }
  
  // Check for @react-oauth/google
  if (!fs.existsSync('node_modules/@react-oauth/google')) {
    log('   @react-oauth/google not installed', 'warning');
    return false;
  }
  
  return true;
}

// Test 4: Environment files
function testEnvironmentFiles() {
  const envFiles = ['.env', '.env.production'];
  let foundEnv = false;
  
  envFiles.forEach(file => {
    if (fs.existsSync(file)) {
      foundEnv = true;
      log(`   Found: ${file}`, 'info');
    }
  });
  
  if (!foundEnv) {
    log('   No environment files found', 'warning');
    return false;
  }
  
  return true;
}

// Test 5: Build scripts
function testBuildScripts() {
  const buildScripts = [
    'scripts/deploy-windows.bat',
    'scripts/fix-dependencies.js'
  ];
  
  let allFound = true;
  
  buildScripts.forEach(script => {
    if (fs.existsSync(script)) {
      log(`   Found: ${script}`, 'info');
    } else {
      log(`   Missing: ${script}`, 'warning');
      allFound = false;
    }
  });
  
  return allFound;
}

// Test 6: Vite availability
function testViteAvailability() {
  try {
    const viteVersion = execSync('npx vite --version', { encoding: 'utf8', stdio: 'pipe' }).trim();
    log(`   Vite version: ${viteVersion}`, 'info');
    return true;
  } catch (error) {
    log('   Vite not available or not working', 'warning');
    return false;
  }
}

// Main test function
async function runQuickTest() {
  log('🔍 Running Quick Deployment Tests', 'info');
  log('=================================\n');
  
  const tests = [
    { name: 'Package.json Validity', fn: testPackageJson },
    { name: 'Dependencies Check', fn: testDependencies },
    { name: 'Node Modules Check', fn: testNodeModules },
    { name: 'Environment Files', fn: testEnvironmentFiles },
    { name: 'Build Scripts', fn: testBuildScripts },
    { name: 'Vite Availability', fn: testViteAvailability }
  ];
  
  let passed = 0;
  let warnings = 0;
  
  tests.forEach(test => {
    const result = runTest(test.name, test.fn);
    if (result) {
      passed++;
    } else {
      warnings++;
    }
    console.log(''); // Add spacing
  });
  
  // Summary
  log('📊 Quick Test Summary', 'info');
  log('====================');
  log(`Passed: ${passed}/${tests.length}`);
  log(`Warnings: ${warnings}`);
  
  if (warnings === 0) {
    log('\n🎉 All tests passed! Ready for deployment.', 'success');
    log('\n🚀 Next step: npm run deploy:fullstack', 'info');
    return true;
  } else if (warnings <= 2) {
    log('\n⚠️  Minor issues detected, but deployment should work.', 'warning');
    log('\n🚀 Try: npm run deploy:fullstack', 'info');
    return true;
  } else {
    log('\n❌ Multiple issues detected. Fix before deployment.', 'error');
    log('\n🔧 Try: npm run fix:dependencies', 'info');
    return false;
  }
}

// Run the test
runQuickTest().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log(`❌ Quick test failed: ${error.message}`, 'error');
  process.exit(1);
});
