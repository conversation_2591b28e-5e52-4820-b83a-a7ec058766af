import { Cart, CartItem } from '../types';

/**
 * Service for persisting cart data across browser sessions
 */
class CartPersistenceService {
  private readonly CART_STORAGE_KEY = 'nirvana_cart';
  private readonly GUEST_CART_KEY = 'nirvana_guest_cart';
  private readonly CART_EXPIRY_DAYS = 30;

  /**
   * Save cart to localStorage for persistence
   */
  saveCart(cart: Cart, isGuest: boolean = false): void {
    try {
      const cartData = {
        cart,
        timestamp: new Date().toISOString(),
        expiresAt: new Date(Date.now() + this.CART_EXPIRY_DAYS * 24 * 60 * 60 * 1000).toISOString()
      };

      const storageKey = isGuest ? this.GUEST_CART_KEY : this.CART_STORAGE_KEY;
      localStorage.setItem(storageKey, JSON.stringify(cartData));
    } catch (error) {
      console.warn('Failed to save cart to localStorage:', error);
    }
  }

  /**
   * Load cart from localStorage
   */
  loadCart(isGuest: boolean = false): Cart | null {
    try {
      const storageKey = isGuest ? this.GUEST_CART_KEY : this.CART_STORAGE_KEY;
      const stored = localStorage.getItem(storageKey);
      
      if (!stored) {
        return null;
      }

      const cartData = JSON.parse(stored);
      
      // Check if cart has expired
      if (new Date(cartData.expiresAt) < new Date()) {
        this.clearCart(isGuest);
        return null;
      }

      return cartData.cart;
    } catch (error) {
      console.warn('Failed to load cart from localStorage:', error);
      return null;
    }
  }

  /**
   * Clear cart from localStorage
   */
  clearCart(isGuest: boolean = false): void {
    try {
      const storageKey = isGuest ? this.GUEST_CART_KEY : this.CART_STORAGE_KEY;
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.warn('Failed to clear cart from localStorage:', error);
    }
  }

  /**
   * Merge guest cart with user cart when user logs in
   */
  mergeGuestCart(userCart: Cart | null, guestCart: Cart | null): Cart | null {
    if (!guestCart || !guestCart.items || guestCart.items.length === 0) {
      return userCart;
    }

    if (!userCart || !userCart.items || userCart.items.length === 0) {
      return guestCart;
    }

    // Merge items from guest cart into user cart
    const mergedItems: CartItem[] = [...userCart.items];
    
    guestCart.items.forEach(guestItem => {
      const existingItemIndex = mergedItems.findIndex(
        item => item.productId === guestItem.productId && 
                JSON.stringify(item.variant) === JSON.stringify(guestItem.variant)
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item already exists
        mergedItems[existingItemIndex].quantity += guestItem.quantity;
      } else {
        // Add new item
        mergedItems.push(guestItem);
      }
    });

    return {
      ...userCart,
      items: mergedItems,
      itemCount: mergedItems.reduce((count, item) => count + item.quantity, 0)
    };
  }

  /**
   * Save cart item temporarily (for quick add/remove operations)
   */
  saveCartItem(item: CartItem, isGuest: boolean = false): void {
    try {
      const cart = this.loadCart(isGuest) || { items: [], itemCount: 0, total: 0 };
      
      const existingItemIndex = cart.items.findIndex(
        cartItem => cartItem.productId === item.productId && 
                    JSON.stringify(cartItem.variant) === JSON.stringify(item.variant)
      );

      if (existingItemIndex >= 0) {
        cart.items[existingItemIndex] = item;
      } else {
        cart.items.push(item);
      }

      cart.itemCount = cart.items.reduce((count, cartItem) => count + cartItem.quantity, 0);
      this.saveCart(cart, isGuest);
    } catch (error) {
      console.warn('Failed to save cart item:', error);
    }
  }

  /**
   * Remove cart item
   */
  removeCartItem(productId: string, variant: any = null, isGuest: boolean = false): void {
    try {
      const cart = this.loadCart(isGuest);
      if (!cart || !cart.items) return;

      cart.items = cart.items.filter(
        item => !(item.productId === productId && 
                 JSON.stringify(item.variant) === JSON.stringify(variant))
      );

      cart.itemCount = cart.items.reduce((count, item) => count + item.quantity, 0);
      this.saveCart(cart, isGuest);
    } catch (error) {
      console.warn('Failed to remove cart item:', error);
    }
  }

  /**
   * Update cart item quantity
   */
  updateCartItemQuantity(productId: string, variant: any, quantity: number, isGuest: boolean = false): void {
    try {
      const cart = this.loadCart(isGuest);
      if (!cart || !cart.items) return;

      const itemIndex = cart.items.findIndex(
        item => item.productId === productId && 
                JSON.stringify(item.variant) === JSON.stringify(variant)
      );

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          cart.items.splice(itemIndex, 1);
        } else {
          cart.items[itemIndex].quantity = quantity;
        }

        cart.itemCount = cart.items.reduce((count, item) => count + item.quantity, 0);
        this.saveCart(cart, isGuest);
      }
    } catch (error) {
      console.warn('Failed to update cart item quantity:', error);
    }
  }

  /**
   * Get cart summary for display
   */
  getCartSummary(isGuest: boolean = false): { itemCount: number; total: number } {
    const cart = this.loadCart(isGuest);
    
    if (!cart || !cart.items) {
      return { itemCount: 0, total: 0 };
    }

    const itemCount = cart.items.reduce((count, item) => count + item.quantity, 0);
    const total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    return { itemCount, total };
  }

  /**
   * Check if cart has items
   */
  hasItems(isGuest: boolean = false): boolean {
    const cart = this.loadCart(isGuest);
    return !!(cart && cart.items && cart.items.length > 0);
  }

  /**
   * Get cart item count for header display
   */
  getItemCount(isGuest: boolean = false): number {
    const cart = this.loadCart(isGuest);
    return cart?.itemCount || 0;
  }

  /**
   * Cleanup expired carts
   */
  cleanupExpiredCarts(): void {
    try {
      // Check and cleanup user cart
      const userCart = localStorage.getItem(this.CART_STORAGE_KEY);
      if (userCart) {
        const userData = JSON.parse(userCart);
        if (new Date(userData.expiresAt) < new Date()) {
          localStorage.removeItem(this.CART_STORAGE_KEY);
        }
      }

      // Check and cleanup guest cart
      const guestCart = localStorage.getItem(this.GUEST_CART_KEY);
      if (guestCart) {
        const guestData = JSON.parse(guestCart);
        if (new Date(guestData.expiresAt) < new Date()) {
          localStorage.removeItem(this.GUEST_CART_KEY);
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup expired carts:', error);
    }
  }

  /**
   * Initialize cart persistence (call on app startup)
   */
  initialize(): void {
    // Cleanup expired carts on initialization
    this.cleanupExpiredCarts();

    // Set up periodic cleanup (every hour)
    setInterval(() => {
      this.cleanupExpiredCarts();
    }, 60 * 60 * 1000);
  }

  /**
   * Sync cart with server (for authenticated users)
   */
  async syncWithServer(serverCart: Cart): Promise<void> {
    try {
      // Save server cart to localStorage
      this.saveCart(serverCart, false);
      
      // Clear guest cart if user is now authenticated
      this.clearCart(true);
    } catch (error) {
      console.warn('Failed to sync cart with server:', error);
    }
  }

  /**
   * Handle user logout - convert user cart to guest cart
   */
  handleUserLogout(): void {
    try {
      const userCart = this.loadCart(false);
      if (userCart) {
        // Save user cart as guest cart
        this.saveCart(userCart, true);
        // Clear user cart
        this.clearCart(false);
      }
    } catch (error) {
      console.warn('Failed to handle user logout cart conversion:', error);
    }
  }

  /**
   * Get cart for checkout (includes validation)
   */
  getCartForCheckout(isGuest: boolean = false): Cart | null {
    const cart = this.loadCart(isGuest);
    
    if (!cart || !cart.items || cart.items.length === 0) {
      return null;
    }

    // Validate cart items (ensure all required fields are present)
    const validItems = cart.items.filter(item => 
      item.productId && 
      item.name && 
      item.price > 0 && 
      item.quantity > 0
    );

    if (validItems.length === 0) {
      return null;
    }

    return {
      ...cart,
      items: validItems,
      itemCount: validItems.reduce((count, item) => count + item.quantity, 0)
    };
  }
}

// Export singleton instance
export const cartPersistenceService = new CartPersistenceService();
export default cartPersistenceService;
