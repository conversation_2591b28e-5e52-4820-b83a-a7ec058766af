import api from './api';
import { AxiosResponse } from 'axios';

export interface GuestOrderData {
  // Contact Information
  email: string;
  phone: string;

  // Shipping Address
  shippingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Billing Address
  billingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Order Details
  items: Array<{
    productId: number;
    variantId?: number;
    quantity: number;
    price: number;
  }>;

  // Payment Information
  paymentMethod: string;
  paymentToken?: string;

  // Additional Information
  orderNotes?: string;
  ageVerification: boolean;
  termsAccepted: boolean;

  // Totals
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
}

export interface GuestOrder {
  id: string;
  orderNumber: string;
  email: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: Array<{
    id: number;
    productId: number;
    productName: string;
    variantName?: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  shippingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipCode: string;
  };
  billingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipCode: string;
  };
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  orderNotes?: string;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface GuestOrderResponse {
  success: boolean;
  data: {
    order: GuestOrder;
    trackingToken: string; // Special token for guest order tracking
  };
  message?: string;
}

export interface GuestOrderTrackingResponse {
  success: boolean;
  data: {
    order: GuestOrder;
    trackingHistory: Array<{
      status: string;
      description: string;
      timestamp: string;
      location?: string;
    }>;
  };
  message?: string;
}

class GuestOrderService {
  /**
   * Create a guest order
   */
  async createGuestOrder(orderData: GuestOrderData): Promise<AxiosResponse<GuestOrderResponse>> {
    return api.post('/orders/guest', orderData);
  }

  /**
   * Track a guest order using email and order number
   */
  async trackGuestOrder(
    email: string, 
    orderNumber: string
  ): Promise<AxiosResponse<GuestOrderTrackingResponse>> {
    return api.post('/orders/guest/track', {
      email,
      orderNumber
    });
  }

  /**
   * Track a guest order using tracking token
   */
  async trackGuestOrderByToken(
    trackingToken: string
  ): Promise<AxiosResponse<GuestOrderTrackingResponse>> {
    return api.get(`/orders/guest/track/${trackingToken}`);
  }

  /**
   * Resend order confirmation email
   */
  async resendOrderConfirmation(
    email: string,
    orderNumber: string
  ): Promise<AxiosResponse<{ success: boolean; message: string }>> {
    return api.post('/orders/guest/resend-confirmation', {
      email,
      orderNumber
    });
  }

  /**
   * Cancel a guest order (if still in pending/processing status)
   */
  async cancelGuestOrder(
    email: string,
    orderNumber: string,
    reason?: string
  ): Promise<AxiosResponse<{ success: boolean; message: string }>> {
    return api.post('/orders/guest/cancel', {
      email,
      orderNumber,
      reason
    });
  }

  /**
   * Validate guest order data before submission
   */
  validateGuestOrderData(orderData: GuestOrderData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Email validation
    if (!orderData.email || !this.isValidEmail(orderData.email)) {
      errors.push('Valid email address is required');
    }

    // Phone validation
    if (!orderData.phone || orderData.phone.length < 10) {
      errors.push('Valid phone number is required');
    }

    // Shipping address validation
    if (!orderData.shippingAddress.firstName?.trim()) {
      errors.push('Shipping first name is required');
    }
    if (!orderData.shippingAddress.lastName?.trim()) {
      errors.push('Shipping last name is required');
    }
    if (!orderData.shippingAddress.address1?.trim()) {
      errors.push('Shipping address is required');
    }
    if (!orderData.shippingAddress.city?.trim()) {
      errors.push('Shipping city is required');
    }
    if (!orderData.shippingAddress.state?.trim()) {
      errors.push('Shipping state is required');
    }
    if (!orderData.shippingAddress.zipCode?.trim()) {
      errors.push('Shipping ZIP code is required');
    }

    // Billing address validation
    if (!orderData.billingAddress.firstName?.trim()) {
      errors.push('Billing first name is required');
    }
    if (!orderData.billingAddress.lastName?.trim()) {
      errors.push('Billing last name is required');
    }
    if (!orderData.billingAddress.address1?.trim()) {
      errors.push('Billing address is required');
    }
    if (!orderData.billingAddress.city?.trim()) {
      errors.push('Billing city is required');
    }
    if (!orderData.billingAddress.state?.trim()) {
      errors.push('Billing state is required');
    }
    if (!orderData.billingAddress.zipCode?.trim()) {
      errors.push('Billing ZIP code is required');
    }

    // Order items validation
    if (!orderData.items || orderData.items.length === 0) {
      errors.push('Order must contain at least one item');
    }

    // Age verification
    if (!orderData.ageVerification) {
      errors.push('Age verification is required');
    }

    // Terms acceptance
    if (!orderData.termsAccepted) {
      errors.push('Terms and conditions must be accepted');
    }

    // Payment method
    if (!orderData.paymentMethod) {
      errors.push('Payment method is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate guest order tracking URL
   */
  generateTrackingUrl(trackingToken: string): string {
    return `${window.location.origin}/track-order?token=${trackingToken}`;
  }

  /**
   * Store guest order tracking info in localStorage for easy access
   */
  storeGuestOrderTracking(orderNumber: string, email: string, trackingToken: string): void {
    try {
      const guestOrders = this.getStoredGuestOrders();
      guestOrders[orderNumber] = {
        email,
        trackingToken,
        createdAt: new Date().toISOString()
      };
      localStorage.setItem('nirvana_guest_orders', JSON.stringify(guestOrders));
    } catch (error) {
      console.warn('Failed to store guest order tracking info:', error);
    }
  }

  /**
   * Get stored guest orders from localStorage
   */
  getStoredGuestOrders(): Record<string, { email: string; trackingToken: string; createdAt: string }> {
    try {
      const stored = localStorage.getItem('nirvana_guest_orders');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn('Failed to retrieve stored guest orders:', error);
      return {};
    }
  }

  /**
   * Clear old guest order tracking info (older than 90 days)
   */
  cleanupOldGuestOrders(): void {
    try {
      const guestOrders = this.getStoredGuestOrders();
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

      const cleanedOrders = Object.entries(guestOrders).reduce((acc, [orderNumber, data]) => {
        if (new Date(data.createdAt) > ninetyDaysAgo) {
          acc[orderNumber] = data;
        }
        return acc;
      }, {} as Record<string, any>);

      localStorage.setItem('nirvana_guest_orders', JSON.stringify(cleanedOrders));
    } catch (error) {
      console.warn('Failed to cleanup old guest orders:', error);
    }
  }

  /**
   * Email validation helper
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

export const guestOrderService = new GuestOrderService();
export default guestOrderService;
