# Troubleshooting Guide
## Nirvana Organics E-commerce Platform

### 🔍 Overview

This guide provides solutions to common issues you may encounter during deployment and operation of the Nirvana Organics backend.

## 🚨 Emergency Quick Fixes

### Application Down
```bash
# Quick restart
sudo -u nirvana pm2 restart all

# Check status
sudo -u nirvana pm2 status

# View logs
sudo -u nirvana pm2 logs
```

### Database Connection Lost
```bash
# Restart MySQL
sudo systemctl restart mysql

# Test connection
sudo -u nirvana npm run test:database

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log
```

### SSL Certificate Expired
```bash
# Renew certificate
sudo certbot renew

# Reload Nginx
sudo systemctl reload nginx

# Check status
sudo certbot certificates
```

## 🔧 Deployment Issues

### Issue: Deployment Package Not Found
**Error**: `Deployment package not found!`

**Solution**:
```bash
# Ensure package is in current directory
ls -la *.tar.gz

# If missing, upload package
scp nirvana-organics-backend-production-*.tar.gz root@YOUR_VPS_IP:/root/

# Extract if needed
tar -xzf nirvana-organics-backend-production-*.tar.gz
cd nirvana-organics-backend-production
```

### Issue: Permission Denied During Deployment
**Error**: `Permission denied` or `Operation not permitted`

**Solution**:
```bash
# Ensure running as root
sudo ./scripts/vps-deployment.sh

# Fix ownership if needed
sudo chown -R nirvana:nirvana /var/www/nirvana-organics-backend

# Fix permissions
sudo chmod -R 755 /var/www/nirvana-organics-backend
```

### Issue: Node.js Installation Failed
**Error**: `Node.js installation failed` or `command not found: node`

**Solution**:
```bash
# Manual Node.js installation
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version
npm --version

# Install PM2 if needed
sudo npm install -g pm2
```

### Issue: MySQL Setup Failed
**Error**: `Access denied for user` or `Database connection failed`

**Solution**:
```bash
# Reset MySQL root password
sudo mysql
```

```sql
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'new_password';
FLUSH PRIVILEGES;
EXIT;
```

```bash
# Recreate database and user
sudo mysql -u root -p
```

```sql
DROP DATABASE IF EXISTS u106832845_nirvana;
DROP USER IF EXISTS 'u106832845_root'@'localhost';
CREATE DATABASE u106832845_nirvana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'u106832845_root'@'localhost' IDENTIFIED BY 'YOUR_PASSWORD';
GRANT ALL PRIVILEGES ON u106832845_nirvana.* TO 'u106832845_root'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 🌐 Application Issues

### Issue: Application Won't Start
**Error**: PM2 shows `errored` or `stopped` status

**Diagnosis**:
```bash
# Check PM2 logs
sudo -u nirvana pm2 logs

# Check environment file
sudo -u nirvana cat /var/www/nirvana-organics-backend/current/.env

# Test manually
cd /var/www/nirvana-organics-backend/current
sudo -u nirvana node server/index.js
```

**Common Solutions**:

1. **Missing Environment Variables**:
```bash
# Copy production template
sudo -u nirvana cp .env.production.final .env

# Generate secrets
sudo -u nirvana npm run generate:secrets
```

2. **Database Connection Issues**:
```bash
# Test database connection
sudo -u nirvana npm run test:database

# Update database credentials in .env
sudo -u nirvana nano .env
```

3. **Port Already in Use**:
```bash
# Check what's using port 5000
sudo lsof -i :5000

# Kill process if needed
sudo kill -9 PID

# Or change port in .env
PORT=5001
```

### Issue: 502 Bad Gateway (Nginx)
**Error**: Nginx returns 502 Bad Gateway

**Diagnosis**:
```bash
# Check Nginx logs
sudo tail -f /var/log/nginx/error.log

# Check if application is running
sudo -u nirvana pm2 status

# Test application directly
curl http://localhost:5000/health
```

**Solutions**:

1. **Application Not Running**:
```bash
# Start application
sudo -u nirvana pm2 start ecosystem.config.js --env production
```

2. **Wrong Port Configuration**:
```bash
# Check Nginx configuration
sudo nano /etc/nginx/sites-available/nirvana-organics-backend

# Ensure proxy_pass matches application port
proxy_pass http://localhost:5000;
```

3. **Firewall Blocking Internal Communication**:
```bash
# Allow internal communication
sudo ufw allow from 127.0.0.1
```

### Issue: Database Migration Failed
**Error**: `Migration failed` or `Table doesn't exist`

**Solution**:
```bash
# Check database connection
sudo -u nirvana npm run test:database

# Run migrations manually
cd /var/www/nirvana-organics-backend/current
sudo -u nirvana npm run migrate:prod

# If still failing, check migration files
ls -la server/migrations/

# Reset database if needed (CAUTION: This will delete all data)
sudo -u nirvana npm run reset:database
sudo -u nirvana npm run migrate:prod
```

## 🔒 SSL and Security Issues

### Issue: SSL Certificate Not Working
**Error**: `SSL certificate problem` or `Certificate not found`

**Diagnosis**:
```bash
# Check certificate status
sudo certbot certificates

# Test SSL connection
openssl s_client -connect shopnirvanaorganics.com:443

# Check Nginx SSL configuration
sudo nginx -t
```

**Solutions**:

1. **Certificate Not Obtained**:
```bash
# Obtain certificate manually
sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com \
    --non-interactive --agree-tos --email <EMAIL>
```

2. **Certificate Expired**:
```bash
# Renew certificate
sudo certbot renew

# Reload Nginx
sudo systemctl reload nginx
```

3. **Wrong Domain Configuration**:
```bash
# Check DNS resolution
nslookup shopnirvanaorganics.com

# Update domain in Nginx config if needed
sudo nano /etc/nginx/sites-available/nirvana-organics-backend
```

### Issue: HTTPS Redirect Not Working
**Error**: Site loads on HTTP but doesn't redirect to HTTPS

**Solution**:
```bash
# Check Nginx configuration
sudo nano /etc/nginx/sites-available/nirvana-organics-backend

# Ensure redirect is configured
server {
    listen 80;
    server_name shopnirvanaorganics.com www.shopnirvanaorganics.com;
    return 301 https://$server_name$request_uri;
}

# Reload Nginx
sudo systemctl reload nginx
```

## 💳 Payment and API Issues

### Issue: Square Payment Not Working
**Error**: `Payment failed` or `Invalid credentials`

**Diagnosis**:
```bash
# Check Square configuration
sudo -u nirvana npm run setup:services

# Check environment variables
grep SQUARE /var/www/nirvana-organics-backend/current/.env
```

**Solutions**:

1. **Using Sandbox Credentials in Production**:
```bash
# Update to production credentials in .env
SQUARE_APPLICATION_ID=sq0idp-YOUR_PRODUCTION_APP_ID
SQUARE_ACCESS_TOKEN=YOUR_PRODUCTION_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_PRODUCTION_LOCATION_ID
SQUARE_ENVIRONMENT=production

# Restart application
sudo -u nirvana pm2 restart all
```

2. **Invalid API Credentials**:
- Verify credentials in Square Developer Dashboard
- Ensure application is approved for production
- Check location ID is correct

### Issue: Email Not Sending
**Error**: `Email send failed` or `SMTP connection failed`

**Diagnosis**:
```bash
# Test email configuration
sudo -u nirvana npm run test:email

# Check email settings in .env
grep EMAIL /var/www/nirvana-organics-backend/current/.env
```

**Solutions**:

1. **Invalid SMTP Credentials**:
```bash
# Update email configuration in .env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Restart application
sudo -u nirvana pm2 restart all
```

2. **Gmail App Password Required**:
- Enable 2-factor authentication on Gmail
- Generate app-specific password
- Use app password instead of regular password

## 🖥️ System Resource Issues

### Issue: High Memory Usage
**Error**: Application crashes or becomes unresponsive

**Diagnosis**:
```bash
# Check memory usage
free -h
sudo -u nirvana pm2 monit

# Check for memory leaks
sudo -u nirvana pm2 logs --lines 100
```

**Solutions**:

1. **Restart Application**:
```bash
sudo -u nirvana pm2 restart all
```

2. **Increase Swap Space**:
```bash
# Create swap file
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Make permanent
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

3. **Optimize PM2 Configuration**:
```bash
# Edit ecosystem.config.js
max_memory_restart: '500M'
```

### Issue: Disk Space Full
**Error**: `No space left on device`

**Diagnosis**:
```bash
# Check disk usage
df -h

# Find large files
sudo du -sh /* | sort -rh | head -10
```

**Solutions**:

1. **Clean Log Files**:
```bash
# Clean PM2 logs
sudo -u nirvana pm2 flush

# Clean system logs
sudo journalctl --vacuum-time=7d

# Clean old backups
find /var/backups -name "*.tar.gz" -mtime +30 -delete
```

2. **Clean Package Cache**:
```bash
# Clean apt cache
sudo apt clean

# Clean npm cache
sudo npm cache clean --force
```

## 📊 Monitoring and Logging Issues

### Issue: Monitoring Not Working
**Error**: Health checks failing or monitoring scripts not running

**Solution**:
```bash
# Check cron jobs
crontab -l

# Reinstall monitoring
sudo ./scripts/setup-monitoring.sh

# Test health check manually
/opt/monitoring/scripts/health-check.sh

# Check monitoring logs
tail -f /opt/monitoring/logs/health-check.log
```

### Issue: Backup Failed
**Error**: Backup scripts failing or not running

**Solution**:
```bash
# Check backup status
/var/backups/nirvana-organics-backend/scripts/backup-status.sh

# Test backup manually
/var/backups/nirvana-organics-backend/scripts/backup-all.sh

# Check backup logs
tail -f /var/backups/nirvana-organics-backend/logs/master-backup.log

# Fix permissions if needed
sudo chown -R root:root /var/backups/nirvana-organics-backend
sudo chmod +x /var/backups/nirvana-organics-backend/scripts/*.sh
```

## 🔄 Recovery Procedures

### Complete Application Recovery
```bash
# 1. Stop application
sudo -u nirvana pm2 stop all

# 2. Restore from backup
/var/backups/nirvana-organics-backend/scripts/restore-database.sh /path/to/backup.sql.gz

# 3. Restart application
sudo -u nirvana pm2 start all

# 4. Verify functionality
curl https://shopnirvanaorganics.com/health
```

### Rollback to Previous Deployment
```bash
# Use rollback script
sudo ./scripts/rollback-deployment.sh

# Or manual rollback
cd /var/www/nirvana-organics-backend/releases
ls -la  # Find previous release
sudo ln -sfn previous_release_directory /var/www/nirvana-organics-backend/current
sudo -u nirvana pm2 restart all
```

### Emergency Maintenance Mode
```bash
# Create maintenance page
echo "Site under maintenance" | sudo tee /var/www/html/maintenance.html

# Update Nginx to show maintenance page
sudo nano /etc/nginx/sites-available/nirvana-organics-backend
# Add: return 503; in server block

sudo systemctl reload nginx
```

## 📞 Getting Help

### Log Collection for Support
```bash
# Collect all relevant logs
mkdir -p /tmp/nirvana-logs
sudo -u nirvana pm2 logs --lines 100 > /tmp/nirvana-logs/pm2.log
sudo cp /var/log/nginx/error.log /tmp/nirvana-logs/
sudo cp /var/log/mysql/error.log /tmp/nirvana-logs/
sudo cp /var/log/nirvana-organics-backend-deployment.log /tmp/nirvana-logs/
tar -czf nirvana-logs-$(date +%Y%m%d).tar.gz -C /tmp nirvana-logs
```

### System Information for Support
```bash
# System info
uname -a
lsb_release -a
free -h
df -h
sudo -u nirvana pm2 status
sudo systemctl status nginx
sudo systemctl status mysql
```

### Contact Information
- Check documentation files for specific issues
- Review application logs for error details
- Use monitoring dashboard for system status
- Consult deployment guides for configuration issues

Remember: Always backup before making changes, and test in a staging environment when possible.
