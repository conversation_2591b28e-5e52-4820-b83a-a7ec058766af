const express = require('express');
const router = express.Router();
const couponManagementController = require('../controllers/couponManagementController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const {
  validateCouponId,
  validateCouponCode,
  validateCreateCoupon,
  validateUpdateCoupon,
  validateBulkCouponOperation,
  validateCouponQuery,
  validateCouponCodeValidation,
  requireCouponManagementAccess,
  checkCouponExists
} = require('../middleware/couponValidation');

// All routes require authentication and admin privileges
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/coupons
// @desc    Get all coupons with filtering and pagination
// @access  Private (Admin/Manager)
router.get('/', 
  requireCouponManagementAccess,
  validateCouponQuery,
  couponManagementController.getCoupons
);

// @route   GET /api/admin/coupons/statistics
// @desc    Get coupon statistics
// @access  Private (Admin/Manager)
router.get('/statistics',
  requireCouponManagementAccess,
  couponManagementController.getCouponStatistics
);

// @route   GET /api/admin/coupons/validate/:code
// @desc    Validate coupon code
// @access  Private (Admin/Manager)
router.get('/validate/:code',
  requireCouponManagementAccess,
  validateCouponCodeValidation,
  couponManagementController.validateCouponCode
);

// @route   GET /api/admin/coupons/:couponId
// @desc    Get single coupon by ID
// @access  Private (Admin/Manager)
router.get('/:couponId',
  requireCouponManagementAccess,
  validateCouponId,
  checkCouponExists,
  couponManagementController.getCouponById
);

// @route   POST /api/admin/coupons
// @desc    Create new coupon
// @access  Private (Admin/Manager)
router.post('/',
  requireCouponManagementAccess,
  validateCreateCoupon,
  couponManagementController.createCoupon
);

// @route   PUT /api/admin/coupons/:couponId
// @desc    Update coupon
// @access  Private (Admin/Manager)
router.put('/:couponId',
  requireCouponManagementAccess,
  validateUpdateCoupon,
  checkCouponExists,
  couponManagementController.updateCoupon
);

// @route   PATCH /api/admin/coupons/:couponId/toggle
// @desc    Toggle coupon status (activate/deactivate)
// @access  Private (Admin/Manager)
router.patch('/:couponId/toggle',
  requireCouponManagementAccess,
  validateCouponId,
  checkCouponExists,
  couponManagementController.toggleCouponStatus
);

// @route   DELETE /api/admin/coupons/:couponId
// @desc    Delete coupon
// @access  Private (Admin/Manager)
router.delete('/:couponId',
  requireCouponManagementAccess,
  validateCouponId,
  checkCouponExists,
  couponManagementController.deleteCoupon
);

// @route   POST /api/admin/coupons/bulk
// @desc    Bulk operations on coupons (activate/deactivate/delete)
// @access  Private (Admin/Manager)
router.post('/bulk',
  requireCouponManagementAccess,
  validateBulkCouponOperation,
  couponManagementController.bulkUpdateCoupons
);

module.exports = router;
