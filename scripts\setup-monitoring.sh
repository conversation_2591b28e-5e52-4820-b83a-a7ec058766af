#!/bin/bash

# ============================================================================
# Production Monitoring Setup Script
# Sets up comprehensive monitoring for Nirvana Organics backend
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${CYAN}🔧 $1${NC}"; }

# Configuration
PROJECT_NAME="nirvana-organics-backend"
PROJECT_PATH="/var/www/$PROJECT_NAME"
LOG_PATH="$PROJECT_PATH/shared/logs"
BACKUP_PATH="/var/backups/$PROJECT_NAME"
MONITORING_PATH="/opt/monitoring"

echo "============================================================================"
echo "🔍 PRODUCTION MONITORING SETUP"
echo "============================================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: Create monitoring directories
log_step "Step 1: Creating monitoring directories"

mkdir -p "$MONITORING_PATH"/{scripts,logs,alerts}
mkdir -p "$LOG_PATH"
mkdir -p "$BACKUP_PATH"

log_success "Monitoring directories created"

# Step 2: Setup log rotation
log_step "Step 2: Configuring log rotation"

cat > /etc/logrotate.d/$PROJECT_NAME << EOF
# Nirvana Organics Backend Log Rotation
$LOG_PATH/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nirvana nirvana
    postrotate
        sudo -u nirvana pm2 reloadLogs
    endscript
}

# Nginx logs
/var/log/nginx/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        systemctl reload nginx
    endscript
}

# MySQL logs
/var/log/mysql/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 mysql mysql
    postrotate
        systemctl reload mysql
    endscript
}
EOF

log_success "Log rotation configured"

# Step 3: Create health check script
log_step "Step 3: Creating health check script"

cat > "$MONITORING_PATH/scripts/health-check.sh" << 'EOF'
#!/bin/bash

# Health check script for Nirvana Organics backend
LOG_FILE="/opt/monitoring/logs/health-check.log"
ALERT_FILE="/opt/monitoring/alerts/health-alerts.log"
PROJECT_PATH="/var/www/nirvana-organics-backend"

# Function to log with timestamp
log_with_timestamp() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Function to send alert
send_alert() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - ALERT: $1" >> "$ALERT_FILE"
    # Add email notification here if configured
}

# Check application health
check_app_health() {
    if curl -f -s http://localhost:5000/health >/dev/null 2>&1; then
        log_with_timestamp "Application health check: PASSED"
        return 0
    else
        log_with_timestamp "Application health check: FAILED"
        send_alert "Application health check failed"
        return 1
    fi
}

# Check HTTPS health
check_https_health() {
    if curl -f -s https://shopnirvanaorganics.com/health >/dev/null 2>&1; then
        log_with_timestamp "HTTPS health check: PASSED"
        return 0
    else
        log_with_timestamp "HTTPS health check: FAILED"
        send_alert "HTTPS health check failed"
        return 1
    fi
}

# Check PM2 processes
check_pm2_processes() {
    if sudo -u nirvana pm2 list | grep -q "online"; then
        log_with_timestamp "PM2 processes check: PASSED"
        return 0
    else
        log_with_timestamp "PM2 processes check: FAILED"
        send_alert "PM2 processes not running properly"
        return 1
    fi
}

# Check database connection
check_database() {
    cd "$PROJECT_PATH/current"
    if sudo -u nirvana npm run test:database >/dev/null 2>&1; then
        log_with_timestamp "Database connection check: PASSED"
        return 0
    else
        log_with_timestamp "Database connection check: FAILED"
        send_alert "Database connection failed"
        return 1
    fi
}

# Check disk space
check_disk_space() {
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        log_with_timestamp "Disk space check: PASSED ($DISK_USAGE% used)"
        return 0
    else
        log_with_timestamp "Disk space check: WARNING ($DISK_USAGE% used)"
        send_alert "Disk space usage high: $DISK_USAGE%"
        return 1
    fi
}

# Check memory usage
check_memory() {
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$MEMORY_USAGE" -lt 90 ]; then
        log_with_timestamp "Memory usage check: PASSED ($MEMORY_USAGE% used)"
        return 0
    else
        log_with_timestamp "Memory usage check: WARNING ($MEMORY_USAGE% used)"
        send_alert "Memory usage high: $MEMORY_USAGE%"
        return 1
    fi
}

# Check SSL certificate expiration
check_ssl_certificate() {
    if [ -f "/etc/letsencrypt/live/shopnirvanaorganics.com/cert.pem" ]; then
        EXPIRY_DATE=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/shopnirvanaorganics.com/cert.pem | cut -d= -f2)
        EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
        CURRENT_EPOCH=$(date +%s)
        DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))
        
        if [ "$DAYS_UNTIL_EXPIRY" -gt 7 ]; then
            log_with_timestamp "SSL certificate check: PASSED ($DAYS_UNTIL_EXPIRY days until expiry)"
            return 0
        else
            log_with_timestamp "SSL certificate check: WARNING ($DAYS_UNTIL_EXPIRY days until expiry)"
            send_alert "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
            return 1
        fi
    else
        log_with_timestamp "SSL certificate check: FAILED (certificate not found)"
        send_alert "SSL certificate not found"
        return 1
    fi
}

# Run all checks
log_with_timestamp "Starting health checks"

FAILED_CHECKS=0

check_app_health || ((FAILED_CHECKS++))
check_https_health || ((FAILED_CHECKS++))
check_pm2_processes || ((FAILED_CHECKS++))
check_database || ((FAILED_CHECKS++))
check_disk_space || ((FAILED_CHECKS++))
check_memory || ((FAILED_CHECKS++))
check_ssl_certificate || ((FAILED_CHECKS++))

if [ "$FAILED_CHECKS" -eq 0 ]; then
    log_with_timestamp "All health checks passed"
else
    log_with_timestamp "$FAILED_CHECKS health checks failed"
    send_alert "$FAILED_CHECKS health checks failed"
fi

log_with_timestamp "Health checks completed"
EOF

chmod +x "$MONITORING_PATH/scripts/health-check.sh"
log_success "Health check script created"

# Step 4: Create performance monitoring script
log_step "Step 4: Creating performance monitoring script"

cat > "$MONITORING_PATH/scripts/performance-monitor.sh" << 'EOF'
#!/bin/bash

# Performance monitoring script
LOG_FILE="/opt/monitoring/logs/performance.log"

# Function to log with timestamp
log_performance() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Get system metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
LOAD_AVERAGE=$(uptime | awk -F'load average:' '{print $2}')

# Get application metrics
if sudo -u nirvana pm2 list >/dev/null 2>&1; then
    PM2_MEMORY=$(sudo -u nirvana pm2 list | grep nirvana | awk '{print $8}' | head -1)
    PM2_CPU=$(sudo -u nirvana pm2 list | grep nirvana | awk '{print $9}' | head -1)
else
    PM2_MEMORY="N/A"
    PM2_CPU="N/A"
fi

# Log metrics
log_performance "CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}%, Load: ${LOAD_AVERAGE}, PM2 Memory: ${PM2_MEMORY}, PM2 CPU: ${PM2_CPU}"
EOF

chmod +x "$MONITORING_PATH/scripts/performance-monitor.sh"
log_success "Performance monitoring script created"

# Step 5: Setup cron jobs
log_step "Step 5: Setting up monitoring cron jobs"

# Create cron jobs for monitoring
cat > /tmp/monitoring-cron << EOF
# Nirvana Organics Backend Monitoring
# Health checks every 5 minutes
*/5 * * * * /opt/monitoring/scripts/health-check.sh

# Performance monitoring every minute
* * * * * /opt/monitoring/scripts/performance-monitor.sh

# SSL certificate check daily
0 6 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx

# Log cleanup weekly
0 2 * * 0 find /opt/monitoring/logs -name "*.log" -mtime +30 -delete
EOF

crontab /tmp/monitoring-cron
rm /tmp/monitoring-cron

log_success "Monitoring cron jobs configured"

# Step 6: Create monitoring dashboard script
log_step "Step 6: Creating monitoring dashboard"

cat > "$MONITORING_PATH/scripts/dashboard.sh" << 'EOF'
#!/bin/bash

# Simple monitoring dashboard
clear
echo "============================================================================"
echo "🔍 NIRVANA ORGANICS BACKEND - MONITORING DASHBOARD"
echo "============================================================================"
echo "Last updated: $(date)"
echo ""

# System Information
echo "📊 SYSTEM METRICS"
echo "----------------"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')%"
echo "Memory Usage: $(free | awk 'NR==2{printf "%.1f", $3*100/$2}')%"
echo "Disk Usage: $(df / | awk 'NR==2 {print $5}')"
echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
echo "Uptime: $(uptime | awk '{print $3,$4}' | sed 's/,//')"
echo ""

# Application Status
echo "🚀 APPLICATION STATUS"
echo "--------------------"
if curl -f -s http://localhost:5000/health >/dev/null 2>&1; then
    echo "Application Health: ✅ HEALTHY"
else
    echo "Application Health: ❌ UNHEALTHY"
fi

if curl -f -s https://shopnirvanaorganics.com/health >/dev/null 2>&1; then
    echo "HTTPS Health: ✅ HEALTHY"
else
    echo "HTTPS Health: ❌ UNHEALTHY"
fi

# PM2 Status
echo ""
echo "⚙️  PM2 PROCESSES"
echo "----------------"
sudo -u nirvana pm2 list

# SSL Certificate
echo ""
echo "🔒 SSL CERTIFICATE"
echo "-----------------"
if [ -f "/etc/letsencrypt/live/shopnirvanaorganics.com/cert.pem" ]; then
    EXPIRY_DATE=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/shopnirvanaorganics.com/cert.pem | cut -d= -f2)
    EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_EPOCH=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))
    echo "Certificate expires in: $DAYS_UNTIL_EXPIRY days"
else
    echo "Certificate: ❌ NOT FOUND"
fi

# Recent Alerts
echo ""
echo "🚨 RECENT ALERTS"
echo "---------------"
if [ -f "/opt/monitoring/alerts/health-alerts.log" ]; then
    tail -5 /opt/monitoring/alerts/health-alerts.log 2>/dev/null || echo "No recent alerts"
else
    echo "No alerts file found"
fi

echo ""
echo "============================================================================"
echo "Commands:"
echo "• View logs: sudo -u nirvana pm2 logs"
echo "• Restart app: sudo -u nirvana pm2 restart all"
echo "• Check health: /opt/monitoring/scripts/health-check.sh"
echo "• View performance: tail -f /opt/monitoring/logs/performance.log"
echo "============================================================================"
EOF

chmod +x "$MONITORING_PATH/scripts/dashboard.sh"
log_success "Monitoring dashboard created"

# Step 7: Set permissions
log_step "Step 7: Setting permissions"

chown -R root:root "$MONITORING_PATH"
chmod -R 755 "$MONITORING_PATH/scripts"
chmod -R 644 "$MONITORING_PATH/logs" 2>/dev/null || true
chmod -R 644 "$MONITORING_PATH/alerts" 2>/dev/null || true

log_success "Permissions set"

# Final status
echo ""
echo "============================================================================"
log_success "🎉 MONITORING SETUP COMPLETED!"
echo "============================================================================"

log_info "Monitoring Components:"
echo "• Health checks: Every 5 minutes"
echo "• Performance monitoring: Every minute"
echo "• SSL certificate monitoring: Daily"
echo "• Log rotation: Configured"
echo "• Dashboard: /opt/monitoring/scripts/dashboard.sh"

echo ""
log_info "Log Files:"
echo "• Health checks: /opt/monitoring/logs/health-check.log"
echo "• Performance: /opt/monitoring/logs/performance.log"
echo "• Alerts: /opt/monitoring/alerts/health-alerts.log"

echo ""
log_info "Useful Commands:"
echo "• View dashboard: /opt/monitoring/scripts/dashboard.sh"
echo "• Manual health check: /opt/monitoring/scripts/health-check.sh"
echo "• View cron jobs: crontab -l"

echo ""
log_warning "Next Steps:"
echo "1. Configure email notifications for alerts"
echo "2. Set up external monitoring service"
echo "3. Configure backup procedures"
echo "4. Test all monitoring components"

log_success "Monitoring setup completed successfully! 🔍"
