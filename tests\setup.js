/**
 * Test Setup Configuration
 * Global setup for all tests in the Nirvana Organics test suite
 */

// Try to load .env.test first, then fallback to .env
require('dotenv').config({ path: '.env.test' });
require('dotenv').config();

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-for-testing-only';

// Database configuration for testing
process.env.DB_HOST = process.env.DB_HOST || 'localhost';
process.env.DB_NAME = process.env.DB_NAME || 'nirvana_organics_test';
process.env.DB_USER = process.env.DB_USER || 'root';
process.env.DB_PASSWORD = process.env.DB_PASSWORD || '';
process.env.DB_PORT = process.env.DB_PORT || '3306';

// Email configuration for testing (mock)
process.env.EMAIL_HOST = process.env.EMAIL_HOST || 'smtp.example.com';
process.env.EMAIL_PORT = process.env.EMAIL_PORT || '587';
process.env.EMAIL_USER = process.env.EMAIL_USER || '<EMAIL>';
process.env.EMAIL_PASS = process.env.EMAIL_PASS || 'test-password';

// Square API configuration for testing (sandbox)
process.env.SQUARE_APPLICATION_ID = 'sandbox-test-app-id';
process.env.SQUARE_ACCESS_TOKEN = 'sandbox-test-access-token';
process.env.SQUARE_ENVIRONMENT = 'sandbox';

// VAPID keys for web-push notifications (test keys)
process.env.VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa40HI8DLLuxN-RgKBWw2RWvdvKPXiRWjdNd_MgSL4-SQVVZMvZQDOoi2-T_I8';
process.env.VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY || 'tUkzMHalpnZnNyKFWqRUYbPUkKJXKcVm3kU6DgqdOLg';

// Other test configurations
process.env.FRONTEND_URL = 'http://localhost:3000';
process.env.API_BASE_URL = 'http://localhost:5000';
process.env.UPLOAD_PATH = './uploads/test';

// Disable console logs during testing (optional)
if (process.env.SILENT_TESTS === 'true') {
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
}

// Global test utilities
global.testUtils = {
  // Add any global test utilities here
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Mock fetch for API calls
  mockFetch: (data, status = 200) => {
    return Promise.resolve({
      ok: status < 400,
      status,
      json: () => Promise.resolve(data),
      text: () => Promise.resolve(JSON.stringify(data))
    });
  },

  // Generate test data
  generateTestEmail: () => `test.${Date.now()}@example.com`,
  generateTestSKU: () => `TEST-${Date.now()}`,
  generateTestOrderNumber: () => `ORD-${Date.now()}`,

  // Validation helpers
  isValidEmail: (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
  isValidUUID: (uuid) => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid),
  isValidDate: (date) => !isNaN(Date.parse(date)),

  // Mock WebSocket
  createMockSocket: () => ({
    on: vi.fn(),
    emit: vi.fn(),
    connected: true,
    close: vi.fn()
  })
};

// Setup global error handling for tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

console.log('Test environment setup completed');
console.log('Database:', process.env.DB_NAME);
console.log('Environment:', process.env.NODE_ENV);
