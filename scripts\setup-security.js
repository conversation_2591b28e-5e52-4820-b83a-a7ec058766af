#!/usr/bin/env node

/**
 * Security Setup Script for Nirvana Organics E-commerce
 * Generates secure secrets and validates security configuration
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`)
};

/**
 * Generate cryptographically secure random string
 */
function generateSecureSecret(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate JWT secret (256 bits minimum)
 */
function generateJWTSecret() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate encryption key (32 characters)
 */
function generateEncryptionKey() {
  return crypto.randomBytes(16).toString('hex');
}

/**
 * Generate session secret
 */
function generateSessionSecret() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate VAPID keys for web push notifications
 */
function generateVAPIDKeys() {
  try {
    const webpush = require('web-push');
    return webpush.generateVAPIDKeys();
  } catch (error) {
    log.warning('web-push package not found, skipping VAPID key generation');
    return null;
  }
}

/**
 * Validate password strength
 */
function validatePasswordStrength(password) {
  const minLength = 12;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const issues = [];
  
  if (password.length < minLength) {
    issues.push(`Password must be at least ${minLength} characters long`);
  }
  if (!hasUpperCase) {
    issues.push('Password must contain at least one uppercase letter');
  }
  if (!hasLowerCase) {
    issues.push('Password must contain at least one lowercase letter');
  }
  if (!hasNumbers) {
    issues.push('Password must contain at least one number');
  }
  if (!hasSpecialChar) {
    issues.push('Password must contain at least one special character');
  }

  return {
    isStrong: issues.length === 0,
    issues
  };
}

/**
 * Generate secure configuration
 */
function generateSecureConfig() {
  log.step('Generating secure configuration...');

  const config = {
    // JWT Secrets
    JWT_SECRET: generateJWTSecret(),
    JWT_REFRESH_SECRET: generateJWTSecret(),
    
    // Encryption
    ENCRYPTION_KEY: generateEncryptionKey(),
    
    // Session
    SESSION_SECRET: generateSessionSecret(),
    
    // General secrets
    WEBHOOK_SECRET: generateSecureSecret(32),
    API_KEY: generateSecureSecret(24),
    
    // Security settings
    BCRYPT_SALT_ROUNDS: '12',
    RATE_LIMIT_WINDOW_MS: '900000',
    RATE_LIMIT_MAX_REQUESTS: '100',
    
    // HTTPS settings
    FORCE_HTTPS: 'true',
    SECURE_COOKIES: 'true',
    TRUST_PROXY: 'true'
  };

  // Generate VAPID keys if web-push is available
  const vapidKeys = generateVAPIDKeys();
  if (vapidKeys) {
    config.VAPID_PUBLIC_KEY = vapidKeys.publicKey;
    config.VAPID_PRIVATE_KEY = vapidKeys.privateKey;
  }

  return config;
}

/**
 * Update .env file with secure configuration
 */
function updateEnvFile(config) {
  const envPath = path.join(__dirname, '../.env');
  const envExamplePath = path.join(__dirname, '../.env.production.example');
  
  log.step('Updating environment configuration...');

  // Read existing .env or use production example as template
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
    log.info('Using existing .env file as base');
  } else if (fs.existsSync(envExamplePath)) {
    envContent = fs.readFileSync(envExamplePath, 'utf8');
    log.info('Using .env.production.example as template');
  } else {
    log.error('No .env template found');
    return false;
  }

  // Update configuration values
  for (const [key, value] of Object.entries(config)) {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    
    if (regex.test(envContent)) {
      // Update existing value
      envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
      // Add new value
      envContent += `\n${key}=${value}`;
    }
  }

  // Write updated .env file
  fs.writeFileSync(envPath, envContent);
  
  // Set secure file permissions
  fs.chmodSync(envPath, 0o600);
  
  log.success('.env file updated with secure configuration');
  return true;
}

/**
 * Validate environment configuration
 */
function validateEnvironment() {
  log.step('Validating environment configuration...');

  const envPath = path.join(__dirname, '../.env');
  
  if (!fs.existsSync(envPath)) {
    log.error('.env file not found');
    return false;
  }

  // Load environment variables
  require('dotenv').config({ path: envPath });

  const requiredVars = [
    'NODE_ENV',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'ENCRYPTION_KEY',
    'SESSION_SECRET',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'EMAIL_HOST',
    'EMAIL_USER',
    'EMAIL_PASS',
    'FRONTEND_URL',
    'API_BASE_URL'
  ];

  const missingVars = [];
  const weakSecrets = [];

  for (const varName of requiredVars) {
    const value = process.env[varName];
    
    if (!value) {
      missingVars.push(varName);
    } else if (varName.includes('SECRET') || varName.includes('KEY')) {
      // Check secret strength
      if (value.length < 32) {
        weakSecrets.push(`${varName} (too short: ${value.length} chars)`);
      }
    }
  }

  // Report validation results
  if (missingVars.length > 0) {
    log.error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  if (weakSecrets.length > 0) {
    log.warning(`Weak secrets detected: ${weakSecrets.join(', ')}`);
  }

  // Check database password strength
  if (process.env.DB_PASSWORD) {
    const passwordValidation = validatePasswordStrength(process.env.DB_PASSWORD);
    if (!passwordValidation.isStrong) {
      log.warning('Database password is weak:');
      passwordValidation.issues.forEach(issue => log.warning(`  - ${issue}`));
    }
  }

  // Check HTTPS configuration
  if (process.env.NODE_ENV === 'production') {
    if (process.env.FORCE_HTTPS !== 'true') {
      log.warning('HTTPS is not enforced in production');
    }
    if (process.env.SECURE_COOKIES !== 'true') {
      log.warning('Secure cookies are not enabled in production');
    }
  }

  const isValid = missingVars.length === 0;
  
  if (isValid) {
    log.success('Environment configuration is valid');
  } else {
    log.error('Environment configuration has issues');
  }

  return isValid;
}

/**
 * Display security checklist
 */
function displaySecurityChecklist() {
  log.info('Security Configuration Checklist:');
  console.log('=====================================');
  console.log('✓ Strong JWT secrets generated');
  console.log('✓ Encryption key generated');
  console.log('✓ Session secret generated');
  console.log('✓ HTTPS enforcement enabled');
  console.log('✓ Secure cookies enabled');
  console.log('✓ Rate limiting configured');
  console.log('✓ File permissions set (600)');
  console.log('');
  console.log('Manual steps required:');
  console.log('- Update database credentials');
  console.log('- Configure email SMTP settings');
  console.log('- Set up Square payment credentials');
  console.log('- Configure domain URLs');
  console.log('- Install SSL certificates');
  console.log('- Test all integrations');
  console.log('');
  console.log('Security best practices:');
  console.log('- Never commit .env files to version control');
  console.log('- Regularly rotate secrets and passwords');
  console.log('- Monitor for security vulnerabilities');
  console.log('- Keep dependencies updated');
  console.log('- Enable audit logging');
  console.log('- Set up backup systems');
}

/**
 * Main setup function
 */
function setupSecurity() {
  log.info('Starting Nirvana Organics Security Setup...');
  log.info('==========================================');

  try {
    // Generate secure configuration
    const config = generateSecureConfig();
    
    // Update .env file
    const envUpdated = updateEnvFile(config);
    if (!envUpdated) {
      log.error('Failed to update environment configuration');
      process.exit(1);
    }

    // Validate configuration
    const isValid = validateEnvironment();
    if (!isValid) {
      log.warning('Configuration has issues but setup completed');
    }

    // Display checklist
    displaySecurityChecklist();

    log.success('Security setup completed successfully!');
    log.info('Next steps: Update database and email credentials in .env file');

  } catch (error) {
    log.error(`Security setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle script arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node setup-security.js [--validate-only] [--help]');
  console.log('');
  console.log('Options:');
  console.log('  --validate-only    Only validate existing configuration');
  console.log('  --help, -h         Show this help message');
  console.log('');
  console.log('This script generates secure secrets and validates the security configuration');
  console.log('for the Nirvana Organics e-commerce application.');
  process.exit(0);
}

if (process.argv.includes('--validate-only')) {
  log.info('Validating existing security configuration...');
  const isValid = validateEnvironment();
  process.exit(isValid ? 0 : 1);
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupSecurity();
}

export { setupSecurity, validateEnvironment };
