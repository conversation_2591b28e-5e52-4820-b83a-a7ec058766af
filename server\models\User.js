const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('./database');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true, // Allow null for social authentication users
    validate: {
      len: {
        args: [8, 255],
        msg: 'Password must be between 8 and 255 characters'
      },
      notNullIfNotSocial(value) {
        // Only validate password if it's not a social authentication user
        if (!value && !this.googleId && !this.facebookId) {
          throw new Error('Password is required for non-social authentication users');
        }
      }
    }
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'first_name'
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'last_name'
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  dateOfBirth: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'date_of_birth'
  },
  roleId: {
    type: DataTypes.INTEGER,
    allowNull: true, // Will be set to customer role by default in hooks
    field: 'role_id',
    references: {
      model: 'roles',
      key: 'id'
    }
  },
  // Keep legacy role field for backward compatibility during migration
  legacyRole: {
    type: DataTypes.ENUM('customer', 'admin', 'super_admin', 'manager'),
    allowNull: true,
    field: 'legacy_role'
  },
  isVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_verified'
  },
  verificationToken: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'verification_token'
  },
  resetPasswordToken: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'reset_password_token'
  },
  resetPasswordExpires: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'reset_password_expires'
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_login'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  // Social Authentication Fields
  googleId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'google_id'
  },
  facebookId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'facebook_id'
  },
  profilePicture: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'profile_picture'
  }
}, {
  tableName: 'Users',
  hooks: {
    beforeCreate: async (user) => {
      // Hash password only if provided (for social auth users, password may be null)
      if (user.password) {
        const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12);
        user.password = await bcrypt.hash(user.password, salt);
      }

      // Set default role if not specified
      if (!user.roleId) {
        const Role = require('./Role');
        const customerRole = await Role.getByName('customer');
        if (customerRole) {
          user.roleId = customerRole.id;
        }
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12);
        user.password = await bcrypt.hash(user.password, salt);
      }
    }
  }
});

// Instance methods
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

User.prototype.getFullName = function() {
  return `${this.firstName} ${this.lastName}`;
};

// Get user's role information
User.prototype.getRole = async function() {
  if (!this.Role) {
    const Role = require('./Role');
    return await Role.findByPk(this.roleId);
  }
  return this.Role;
};

// Check if user has specific permission
User.prototype.hasPermission = async function(category, action) {
  const role = await this.getRole();
  if (!role) return false;
  return role.hasPermission(category, action);
};

// Check if user has specific role
User.prototype.hasRole = async function(roleName) {
  const role = await this.getRole();
  if (!role) return false;
  return role.name === roleName;
};

// Check if user is admin
User.prototype.isAdmin = async function() {
  return await this.hasRole('admin');
};

// Check if user is manager
User.prototype.isManager = async function() {
  return await this.hasRole('manager');
};

// Check if user is customer
User.prototype.isCustomer = async function() {
  return await this.hasRole('customer');
};

// Get legacy role for backward compatibility
User.prototype.getLegacyRole = async function() {
  const role = await this.getRole();
  if (!role) return 'customer';
  return role.name;
};

// Virtual for full name
User.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  delete values.password;
  delete values.verificationToken;
  delete values.resetPasswordToken;
  delete values.resetPasswordExpires;
  values.fullName = this.getFullName();

  // Add role information if available
  if (this.Role) {
    values.role = this.Role.name;
    values.roleDisplayName = this.Role.displayName;
    values.permissions = this.Role.permissions;
  }

  return values;
};

module.exports = User;
