import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Initialize app and signal completion to browser
const initializeApp = () => {
  // Add preload class to prevent transitions during initial render
  document.documentElement.classList.add('preload');

  // Create and render the React app
  const root = createRoot(document.getElementById('root')!);
  root.render(
    <StrictMode>
      <App />
    </StrictMode>
  );

  // Signal that the app is ready
  setTimeout(() => {
    document.documentElement.classList.remove('preload');
    document.documentElement.classList.add('loaded');

    // Force browser to recognize loading is complete
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        window.dispatchEvent(new Event('load'));
      });
    } else {
      window.dispatchEvent(new Event('load'));
    }
  }, 50);
};

// Initialize the app
initializeApp();
