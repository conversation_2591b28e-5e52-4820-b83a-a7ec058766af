import React, { useState } from 'react';
import { 
  ShoppingCartIcon, 
  HeartIcon, 
  TruckIcon, 
  ShieldCheckIcon,
  StarIcon,
  GiftIcon,
  CreditCardIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

interface CTAButtonProps {
  variant: 'primary' | 'secondary' | 'urgent' | 'bundle' | 'wishlist';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  icon?: React.ComponentType<any>;
  className?: string;
}

interface ProductCTAProps {
  product: any;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  onAddToCart: () => void;
  onAddToWishlist: () => void;
  isInWishlist: boolean;
  isLoading?: boolean;
  className?: string;
}

const CTAButton: React.FC<CTAButtonProps> = ({
  variant,
  size = 'medium',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  children,
  icon: Icon,
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const sizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-6 py-4 text-lg'
  };

  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300',
    urgent: 'bg-gradient-to-r from-red-600 to-pink-600 text-white hover:from-red-700 hover:to-pink-700 focus:ring-red-500 shadow-lg hover:shadow-xl animate-pulse',
    bundle: 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 focus:ring-green-500 shadow-lg hover:shadow-xl',
    wishlist: 'bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500 border border-gray-300 hover:border-red-300 hover:text-red-600'
  };

  const widthClass = fullWidth ? 'w-full' : '';
  const disabledClass = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${widthClass} ${disabledClass} ${className}`}
    >
      {loading ? (
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
      ) : Icon ? (
        <Icon className="h-5 w-5 mr-2" />
      ) : null}
      {children}
    </button>
  );
};

const ProductCTA: React.FC<ProductCTAProps> = ({
  product,
  quantity,
  onQuantityChange,
  onAddToCart,
  onAddToWishlist,
  isInWishlist,
  isLoading = false,
  className = ''
}) => {
  const [showBenefits, setShowBenefits] = useState(false);

  const benefits = [
    { icon: TruckIcon, text: 'Free shipping over $100' },
    { icon: ShieldCheckIcon, text: '30-day money-back guarantee' },
    { icon: StarIcon, text: 'Lab-tested for quality' },
    { icon: CreditCardIcon, text: 'Secure payment processing' }
  ];

  const isOutOfStock = product.stock === 0;
  const isLowStock = product.stock > 0 && product.stock <= 5;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quantity Selector */}
      <div className="flex items-center space-x-4">
        <label className="text-sm font-medium text-gray-700">Quantity:</label>
        <div className="flex items-center border border-gray-300 rounded-lg">
          <button
            onClick={() => onQuantityChange(Math.max(1, quantity - 1))}
            disabled={quantity <= 1}
            className="px-3 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            -
          </button>
          <span className="px-4 py-2 text-gray-900 font-medium border-x border-gray-300">
            {quantity}
          </span>
          <button
            onClick={() => onQuantityChange(Math.min(product.stock, quantity + 1))}
            disabled={quantity >= product.stock}
            className="px-3 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            +
          </button>
        </div>
        {isLowStock && (
          <span className="text-sm text-red-600 font-medium">
            Only {product.stock} left!
          </span>
        )}
      </div>

      {/* Main CTA Buttons */}
      <div className="flex space-x-3">
        <CTAButton
          variant={isOutOfStock ? 'secondary' : isLowStock ? 'urgent' : 'primary'}
          size="large"
          fullWidth
          disabled={isOutOfStock}
          loading={isLoading}
          onClick={onAddToCart}
          icon={ShoppingCartIcon}
          className="flex-1"
        >
          {isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
        </CTAButton>

        <CTAButton
          variant="wishlist"
          size="large"
          onClick={onAddToWishlist}
          icon={isInWishlist ? HeartSolidIcon : HeartIcon}
          className={isInWishlist ? 'text-red-600 border-red-300' : ''}
        >
          {isInWishlist ? 'Saved' : 'Save'}
        </CTAButton>
      </div>

      {/* Buy Now Button for High-Value Items */}
      {product.price > 50 && !isOutOfStock && (
        <CTAButton
          variant="secondary"
          size="large"
          fullWidth
          onClick={() => {
            onAddToCart();
            // Navigate to checkout
          }}
          className="border-2 border-primary-600 text-primary-600 hover:bg-primary-50"
        >
          Buy Now - Skip Cart
        </CTAButton>
      )}

      {/* Product Benefits */}
      <div className="border-t border-gray-200 pt-4">
        <button
          onClick={() => setShowBenefits(!showBenefits)}
          className="flex items-center justify-between w-full text-left"
        >
          <span className="text-sm font-medium text-gray-700">Why choose this product?</span>
          <span className="text-gray-400">
            {showBenefits ? '−' : '+'}
          </span>
        </button>
        
        {showBenefits && (
          <div className="mt-3 space-y-2">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center text-sm text-gray-600">
                <benefit.icon className="h-4 w-4 text-green-600 mr-2" />
                {benefit.text}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Trust Indicators */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <CheckCircleIcon className="h-6 w-6 text-green-600 mx-auto mb-1" />
            <p className="text-xs text-gray-600">Lab Tested</p>
          </div>
          <div>
            <TruckIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
            <p className="text-xs text-gray-600">Fast Shipping</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Cart CTA Component
export const CartCTA: React.FC<{
  itemCount: number;
  total: number;
  onViewCart: () => void;
  onCheckout: () => void;
  className?: string;
}> = ({ itemCount, total, onViewCart, onCheckout, className = '' }) => {
  if (itemCount === 0) return null;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div>
          <p className="text-sm text-gray-600">{itemCount} item{itemCount !== 1 ? 's' : ''} in cart</p>
          <p className="text-lg font-bold text-gray-900">${total.toFixed(2)}</p>
        </div>
        <ShoppingCartIcon className="h-8 w-8 text-primary-600" />
      </div>
      
      <div className="flex space-x-2">
        <CTAButton
          variant="secondary"
          size="small"
          onClick={onViewCart}
          className="flex-1"
        >
          View Cart
        </CTAButton>
        <CTAButton
          variant="primary"
          size="small"
          onClick={onCheckout}
          className="flex-1"
        >
          Checkout
        </CTAButton>
      </div>
    </div>
  );
};

// Newsletter CTA Component
export const NewsletterCTA: React.FC<{
  onSubscribe: (email: string) => void;
  className?: string;
}> = ({ onSubscribe, className = '' }) => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      onSubscribe(email);
      setIsSubscribed(true);
      setEmail('');
    }
  };

  if (isSubscribed) {
    return (
      <div className={`bg-green-50 border border-green-200 rounded-lg p-6 text-center ${className}`}>
        <CheckCircleIcon className="h-12 w-12 text-green-600 mx-auto mb-4" />
        <h3 className="text-lg font-bold text-green-900 mb-2">Thank You!</h3>
        <p className="text-green-800">You're now subscribed to our newsletter.</p>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-lg p-6 ${className}`}>
      <div className="text-center mb-4">
        <GiftIcon className="h-12 w-12 mx-auto mb-4" />
        <h3 className="text-xl font-bold mb-2">Get 10% Off Your First Order</h3>
        <p className="text-primary-100">
          Subscribe to our newsletter for exclusive deals and product updates.
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="flex space-x-2">
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email"
          className="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"
          required
        />
        <CTAButton
          variant="secondary"
          size="medium"
          type="submit"
          className="bg-white text-primary-600 hover:bg-gray-100"
        >
          Subscribe
        </CTAButton>
      </form>
    </div>
  );
};

export { CTAButton, ProductCTA };
export default ProductCTA;
