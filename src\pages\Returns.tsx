import React from 'react';
import { 
  ArrowUturnLeftIcon, 
  ClockIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

const Returns: React.FC = () => {
  const returnSteps = [
    {
      step: 1,
      title: 'Contact Us',
      description: 'Email <NAME_EMAIL> or call (555) 123-4567 within 30 days of delivery.',
      icon: DocumentTextIcon
    },
    {
      step: 2,
      title: 'Get Authorization',
      description: 'We\'ll provide a Return Authorization (RA) number and return shipping label.',
      icon: CheckCircleIcon
    },
    {
      step: 3,
      title: 'Package Items',
      description: 'Pack items in original packaging with RA number clearly visible on the package.',
      icon: ArrowUturnLeftIcon
    },
    {
      step: 4,
      title: 'Ship Back',
      description: 'Use our prepaid return label and drop off at any USPS location.',
      icon: ClockIcon
    }
  ];

  const eligibleItems = [
    'Unopened products in original packaging',
    'Items received within 30 days',
    'Defective or damaged products',
    'Wrong items shipped by us',
    'Products that don\'t match description'
  ];

  const nonEligibleItems = [
    'Opened or used products',
    'Items without original packaging',
    'Products damaged by customer misuse',
    'Items returned after 30 days',
    'Custom or personalized products'
  ];

  const refundMethods = [
    {
      method: 'Original Payment Method',
      timeframe: '3-5 business days',
      description: 'Refund to your original credit card or payment method',
      icon: CurrencyDollarIcon
    },
    {
      method: 'Store Credit',
      timeframe: 'Immediate',
      description: 'Instant store credit for future purchases (10% bonus)',
      icon: CheckCircleIcon
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Returns & Refunds
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              Not satisfied with your purchase? We offer a hassle-free 30-day return policy.
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Return Policy Overview */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Return Promise</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ClockIcon className="h-8 w-8" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">30-Day Window</h3>
                <p className="text-gray-600 text-sm">Return unopened items within 30 days of delivery</p>
              </div>
              <div>
                <div className="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ArrowUturnLeftIcon className="h-8 w-8" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Free Returns</h3>
                <p className="text-gray-600 text-sm">We provide prepaid return labels for your convenience</p>
              </div>
              <div>
                <div className="w-16 h-16 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CurrencyDollarIcon className="h-8 w-8" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Full Refund</h3>
                <p className="text-gray-600 text-sm">Get your money back or choose store credit with bonus</p>
              </div>
            </div>
          </div>

          {/* Return Process */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">How to Return Items</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {returnSteps.map((step, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6 text-center relative">
                  <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                    {step.step}
                  </div>
                  <div className="w-10 h-10 bg-primary-100 text-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <step.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{step.title}</h3>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                  {index < returnSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                      <svg className="w-8 h-8 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Eligible vs Non-Eligible Items */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
                Eligible for Return
              </h3>
              <ul className="space-y-3">
                {eligibleItems.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span className="text-gray-600 text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <XCircleIcon className="h-6 w-6 text-red-600 mr-3" />
                Not Eligible for Return
              </h3>
              <ul className="space-y-3">
                {nonEligibleItems.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <XCircleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span className="text-gray-600 text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Refund Options */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Refund Options</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {refundMethods.map((method, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-primary-100 text-primary-600 rounded-lg flex items-center justify-center mr-3">
                      <method.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{method.method}</h3>
                      <p className="text-sm text-gray-600">{method.timeframe}</p>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm">{method.description}</p>
                  {method.method === 'Store Credit' && (
                    <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-green-800 text-sm font-medium">
                        💰 Bonus: Get 10% extra value when choosing store credit!
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Special Circumstances */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Defective Products</h3>
              <p className="text-gray-600 mb-4 text-sm">
                If you receive a defective or damaged product, we'll make it right immediately:
              </p>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Full refund or replacement at no cost</li>
                <li>• Expedited processing (24-48 hours)</li>
                <li>• Keep the defective item (no return required)</li>
                <li>• Additional compensation for inconvenience</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Wrong Item Shipped</h3>
              <p className="text-gray-600 mb-4 text-sm">
                If we shipped the wrong item, we'll correct it immediately:
              </p>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Immediate shipment of correct item</li>
                <li>• Prepaid return label for wrong item</li>
                <li>• No restocking fees or penalties</li>
                <li>• Upgrade to express shipping at no cost</li>
              </ul>
            </div>
          </div>

          {/* Important Notes */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-8 mb-12">
            <h3 className="text-xl font-bold text-yellow-900 mb-4 flex items-center">
              <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mr-3" />
              Important Return Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-yellow-800 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Health & Safety</h4>
                <p>
                  Due to health regulations, we cannot accept returns of opened cannabis products. 
                  This policy protects all customers and ensures product integrity.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Return Authorization Required</h4>
                <p>
                  All returns must have a Return Authorization (RA) number. Items returned 
                  without authorization may be refused or delayed.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Condition Requirements</h4>
                <p>
                  Items must be in original condition with all packaging, labels, and 
                  accessories included. Damaged packaging may affect return eligibility.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Processing Time</h4>
                <p>
                  Returns are processed within 2-3 business days of receipt. Refunds 
                  appear on your statement within 3-5 business days after processing.
                </p>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Return FAQ</h2>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">What if I lost my receipt or order confirmation?</h4>
                <p className="text-gray-600 text-sm">
                  No problem! We can look up your order using your email address or phone number. 
                  Just contact our customer service team with your information.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Can I return items purchased with a discount or coupon?</h4>
                <p className="text-gray-600 text-sm">
                  Yes, but the refund will be for the amount actually paid after discounts. 
                  The original coupon or discount cannot be reissued.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">What happens if my return is lost in shipping?</h4>
                <p className="text-gray-600 text-sm">
                  All return shipments are tracked and insured. If your return is lost, 
                  we'll work with the carrier to locate it and process your refund regardless.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Can I exchange an item instead of returning it?</h4>
                <p className="text-gray-600 text-sm">
                  Currently, we process returns for refunds only. To get a different item, 
                  please return the original item and place a new order.
                </p>
              </div>
            </div>
          </div>

          {/* Contact for Returns */}
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-8 text-center">
            <h3 className="text-xl font-bold text-primary-900 mb-4">Need Help with a Return?</h3>
            <p className="text-primary-800 mb-6">
              Our customer service team is here to make your return process as smooth as possible.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <h4 className="font-semibold text-primary-900 mb-1">Email</h4>
                <p className="text-primary-800 text-sm"><EMAIL></p>
              </div>
              <div className="text-center">
                <h4 className="font-semibold text-primary-900 mb-1">Phone</h4>
                <p className="text-primary-800 text-sm">+****************</p>
              </div>
              <div className="text-center">
                <h4 className="font-semibold text-primary-900 mb-1">Hours</h4>
                <p className="text-primary-800 text-sm">Mon-Fri: 9AM-6PM EST</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Returns;
