import React from 'react';
import { 
  BuildingOfficeIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  CheckBadgeIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface BusinessInfo {
  companyName: string;
  legalName: string;
  founded: string;
  employees: string;
  headquarters: {
    address: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
  businessHours: {
    weekdays: string;
    weekends: string;
    timezone: string;
  };
  licenses: Array<{
    name: string;
    number: string;
    issuer: string;
    expires: string;
    status: 'active' | 'pending' | 'expired';
  }>;
  certifications: Array<{
    name: string;
    issuer: string;
    date: string;
    description: string;
  }>;
  memberships: Array<{
    organization: string;
    memberSince: string;
    status: string;
  }>;
}

const BusinessCredentials: React.FC = () => {
  const businessInfo: BusinessInfo = {
    companyName: 'Nirvana Organics',
    legalName: 'Nirvana Organics LLC',
    founded: '2020',
    employees: '25-50',
    headquarters: {
      address: '123 Wellness Way',
      city: 'Denver',
      state: 'Colorado',
      zip: '80202',
      phone: '+****************',
      email: '<EMAIL>'
    },
    businessHours: {
      weekdays: '9:00 AM - 6:00 PM',
      weekends: '10:00 AM - 4:00 PM',
      timezone: 'EST'
    },
    licenses: [
      {
        name: 'Hemp Processing License',
        number: 'HPL-2024-001234',
        issuer: 'Colorado Department of Agriculture',
        expires: '2025-12-31',
        status: 'active'
      },
      {
        name: 'Business License',
        number: 'BL-CO-2024-5678',
        issuer: 'City of Denver',
        expires: '2025-06-30',
        status: 'active'
      },
      {
        name: 'Federal EIN',
        number: '88-1234567',
        issuer: 'Internal Revenue Service',
        expires: 'N/A',
        status: 'active'
      }
    ],
    certifications: [
      {
        name: 'Good Manufacturing Practices (GMP)',
        issuer: 'NSF International',
        date: '2024-01-15',
        description: 'Certified for quality manufacturing processes and facility standards'
      },
      {
        name: 'ISO 9001:2015 Quality Management',
        issuer: 'International Organization for Standardization',
        date: '2023-11-20',
        description: 'Quality management system certification for consistent product quality'
      },
      {
        name: 'Organic Certification',
        issuer: 'USDA Organic',
        date: '2023-08-10',
        description: 'Certified organic processing and handling of hemp products'
      }
    ],
    memberships: [
      {
        organization: 'Hemp Industries Association',
        memberSince: '2020',
        status: 'Active Member'
      },
      {
        organization: 'National Hemp Association',
        memberSince: '2021',
        status: 'Premium Member'
      },
      {
        organization: 'Better Business Bureau',
        memberSince: '2020',
        status: 'A+ Rating'
      }
    ]
  };

  const achievements = [
    {
      icon: StarIcon,
      title: '4+ Years in Business',
      description: 'Established and trusted since 2020'
    },
    {
      icon: CheckBadgeIcon,
      title: '50,000+ Orders Fulfilled',
      description: 'Serving customers nationwide'
    },
    {
      icon: ShieldCheckIcon,
      title: '100% Compliant',
      description: 'All licenses and certifications current'
    },
    {
      icon: BuildingOfficeIcon,
      title: 'Colorado Headquarters',
      description: 'Based in hemp-friendly Denver, CO'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'expired':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Our Business Credentials
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transparency and trust are at the core of our business. Learn about our 
            licenses, certifications, and commitment to legal compliance.
          </p>
        </div>

        {/* Company Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg p-8">
            <div className="flex items-center mb-6">
              <BuildingOfficeIcon className="h-8 w-8 text-primary-600 mr-3" />
              <h3 className="text-2xl font-bold text-gray-900">Company Information</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Legal Business Name</label>
                <p className="text-lg font-semibold text-gray-900">{businessInfo.legalName}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Founded</label>
                  <p className="text-lg font-semibold text-gray-900">{businessInfo.founded}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Team Size</label>
                  <p className="text-lg font-semibold text-gray-900">{businessInfo.employees}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Headquarters</label>
                <div className="text-gray-900">
                  <p>{businessInfo.headquarters.address}</p>
                  <p>{businessInfo.headquarters.city}, {businessInfo.headquarters.state} {businessInfo.headquarters.zip}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-8">
            <div className="flex items-center mb-6">
              <PhoneIcon className="h-8 w-8 text-primary-600 mr-3" />
              <h3 className="text-2xl font-bold text-gray-900">Contact Information</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <PhoneIcon className="h-5 w-5 text-gray-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">{businessInfo.headquarters.phone}</p>
                  <p className="text-sm text-gray-600">Customer Service</p>
                </div>
              </div>

              <div className="flex items-center">
                <EnvelopeIcon className="h-5 w-5 text-gray-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">{businessInfo.headquarters.email}</p>
                  <p className="text-sm text-gray-600">General Inquiries</p>
                </div>
              </div>

              <div className="flex items-start">
                <ClockIcon className="h-5 w-5 text-gray-600 mr-3 mt-1" />
                <div>
                  <p className="font-medium text-gray-900">Business Hours ({businessInfo.businessHours.timezone})</p>
                  <p className="text-sm text-gray-600">Mon-Fri: {businessInfo.businessHours.weekdays}</p>
                  <p className="text-sm text-gray-600">Sat-Sun: {businessInfo.businessHours.weekends}</p>
                </div>
              </div>

              <div className="flex items-center">
                <MapPinIcon className="h-5 w-5 text-gray-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Visit Our Facility</p>
                  <p className="text-sm text-gray-600">By appointment only</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Achievements */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {achievements.map((achievement, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <achievement.icon className="h-8 w-8" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">{achievement.title}</h4>
              <p className="text-gray-600 text-sm">{achievement.description}</p>
            </div>
          ))}
        </div>

        {/* Licenses and Certifications */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Licenses */}
          <div>
            <div className="flex items-center mb-6">
              <DocumentTextIcon className="h-6 w-6 text-primary-600 mr-3" />
              <h3 className="text-xl font-bold text-gray-900">Business Licenses</h3>
            </div>
            
            <div className="space-y-4">
              {businessInfo.licenses.map((license, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-900">{license.name}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(license.status)}`}>
                      {license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><strong>License #:</strong> {license.number}</p>
                    <p><strong>Issued by:</strong> {license.issuer}</p>
                    <p><strong>Expires:</strong> {license.expires}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Certifications */}
          <div>
            <div className="flex items-center mb-6">
              <ShieldCheckIcon className="h-6 w-6 text-primary-600 mr-3" />
              <h3 className="text-xl font-bold text-gray-900">Certifications</h3>
            </div>
            
            <div className="space-y-4">
              {businessInfo.certifications.map((cert, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-900">{cert.name}</h4>
                    <CheckBadgeIcon className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><strong>Certified by:</strong> {cert.issuer}</p>
                    <p><strong>Date:</strong> {new Date(cert.date).toLocaleDateString()}</p>
                    <p className="mt-2">{cert.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Industry Memberships */}
        <div className="mt-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Industry Memberships</h3>
            <p className="text-gray-600">
              We're proud members of leading hemp and cannabis industry organizations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {businessInfo.memberships.map((membership, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckBadgeIcon className="h-6 w-6" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{membership.organization}</h4>
                <p className="text-sm text-gray-600 mb-1">Member since {membership.memberSince}</p>
                <span className="inline-block px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  {membership.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Verification Notice */}
        <div className="mt-16 bg-blue-50 border border-blue-200 rounded-lg p-8 text-center">
          <ShieldCheckIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-blue-900 mb-4">Verification Available</h3>
          <p className="text-blue-800 mb-6 max-w-2xl mx-auto">
            All licenses, certifications, and business credentials can be independently verified. 
            Contact us for copies of certificates or verification documents.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" className="btn-primary px-6 py-3">
              Request Verification Documents
            </a>
            <a href="tel:+***********" className="btn-secondary px-6 py-3">
              Call for Verification
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessCredentials;
