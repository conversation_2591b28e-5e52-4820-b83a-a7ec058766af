# ============================================================================
# Nirvana Organics E-commerce - Git Ignore Configuration
# ============================================================================

# Environment Variables
.env
.env.local
.env.development
.env.staging
.env.production
.env.admin
.env.test

# Node.js Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime Data
pids
*.pid
*.seed
*.pid.lock

# Coverage Directory Used by Tools like Istanbul
coverage/
*.lcov

# nyc Test Coverage
.nyc_output

# Grunt Intermediate Storage
.grunt

# Bower Dependency Directory
bower_components

# Node-waf Configuration
.lock-wscript

# Compiled Binary Addons
build/Release

# Dependency Directories
jspm_packages/

# TypeScript Cache
*.tsbuildinfo

# Optional npm Cache Directory
.npm

# Optional eslint Cache
.eslintcache

# Microbundle Cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL History
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity File
.yarn-integrity

# dotenv Environment Variables File
.env

# parcel-bundler Cache
.cache
.parcel-cache

# Next.js Build Output
.next

# Nuxt.js Build / Generate Output
.nuxt
dist

# Gatsby Files
.cache/
public

# Vuepress Build Output
.vuepress/dist

# Serverless Directories
.serverless/

# FuseBox Cache
.fusebox/

# DynamoDB Local Files
.dynamodb/

# TernJS Port File
.tern-port

# Stores VSCode Versions Used for Testing VSCode Extensions
.vscode-test

# ============================================================================
# Application Specific
# ============================================================================

# Build Directories
build/
dist/
out/

# Logs
logs/
*.log
server/logs/
server/logs/*.log

# Uploads and User Generated Content
uploads/
public/uploads/
server/uploads/
temp/
tmp/

# Database Files
*.sqlite
*.sqlite3
*.db

# Backup Files
backups/
*.backup
*.bak
*.sql.gz

# SSL Certificates
*.pem
*.key
*.crt
*.cert

# Configuration Files with Sensitive Data
config/production.json
config/staging.json
config/local.json

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Deployment Files
deployment-package/
*.zip
*.tar.gz
*.tar

# Test Files
test-results/
coverage/

# Package Manager Lock Files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# ============================================================================
# Hostinger Deployment Specific
# ============================================================================

# Deployment Scripts Output
hostinger-deployment/
deployment-temp/
deploy-*.log

# Server Configuration Files
nginx.conf
apache.conf
.htaccess

# ============================================================================
# Development Tools
# ============================================================================

# Storybook Build Outputs
storybook-static

# Temporary Folders
.tmp/
.temp/

# Editor Directories and Files
.vscode/
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
