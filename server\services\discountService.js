const { models } = require('../models');
const { Op } = require('sequelize');
const crypto = require('crypto');

/**
 * Discount Service
 * Handles coupon validation, application, and referral system
 */
class DiscountService {
  /**
   * Validate and apply coupon to order
   */
  static async validateAndApplyCoupon(couponCode, orderData, userId = null, guestEmail = null) {
    try {
      // Find the coupon
      const coupon = await models.Coupon.findOne({
        where: {
          code: couponCode.toUpperCase(),
          isActive: true,
          validFrom: { [Op.lte]: new Date() },
          validUntil: { [Op.gte]: new Date() }
        },
        include: [
          {
            model: models.CouponUsage,
            as: 'usages'
          }
        ]
      });

      if (!coupon) {
        return {
          success: false,
          message: 'Invalid or expired coupon code'
        };
      }

      // Check usage limits
      const totalUsages = coupon.usages.length;
      if (coupon.usageLimit && totalUsages >= coupon.usageLimit) {
        return {
          success: false,
          message: 'Coupon usage limit exceeded'
        };
      }

      // Check user-specific usage limit
      if (userId && coupon.userUsageLimit) {
        const userUsages = coupon.usages.filter(usage => usage.userId === userId).length;
        if (userUsages >= coupon.userUsageLimit) {
          return {
            success: false,
            message: 'You have already used this coupon the maximum number of times'
          };
        }
      }

      // Check minimum order amount
      if (coupon.minimumOrderAmount && orderData.subtotal < coupon.minimumOrderAmount) {
        return {
          success: false,
          message: `Minimum order amount of $${coupon.minimumOrderAmount} required`
        };
      }

      // Check first-time customer restriction
      if (coupon.firstTimeCustomerOnly && userId) {
        const previousOrders = await models.Order.count({
          where: {
            userId,
            status: { [Op.in]: ['completed', 'delivered'] }
          }
        });

        if (previousOrders > 0) {
          return {
            success: false,
            message: 'This coupon is only valid for first-time customers'
          };
        }
      }

      // Calculate discount
      const discountResult = this.calculateDiscount(coupon, orderData);
      
      if (!discountResult.success) {
        return discountResult;
      }

      return {
        success: true,
        coupon: {
          id: coupon.id,
          code: coupon.code,
          name: coupon.name,
          type: coupon.type,
          value: coupon.value
        },
        discount: discountResult.discount,
        message: `Coupon applied! You saved $${discountResult.discount.toFixed(2)}`
      };

    } catch (error) {
      console.error('Error validating coupon:', error);
      return {
        success: false,
        message: 'Error validating coupon'
      };
    }
  }

  /**
   * Calculate discount amount based on coupon type
   */
  static calculateDiscount(coupon, orderData) {
    let discount = 0;
    const { subtotal, items } = orderData;

    try {
      switch (coupon.type) {
        case 'percentage':
          discount = subtotal * (coupon.value / 100);
          if (coupon.maximumDiscountAmount && discount > coupon.maximumDiscountAmount) {
            discount = coupon.maximumDiscountAmount;
          }
          break;

        case 'fixed_amount':
          discount = Math.min(coupon.value, subtotal);
          break;

        case 'free_shipping':
          // This would be handled in shipping calculation
          discount = 0;
          break;

        case 'buy_x_get_y':
          discount = this.calculateBuyXGetYDiscount(coupon, items);
          break;

        default:
          return {
            success: false,
            message: 'Invalid coupon type'
          };
      }

      return {
        success: true,
        discount: Math.max(0, discount)
      };

    } catch (error) {
      console.error('Error calculating discount:', error);
      return {
        success: false,
        message: 'Error calculating discount'
      };
    }
  }

  /**
   * Calculate Buy X Get Y discount
   */
  static calculateBuyXGetYDiscount(coupon, items) {
    if (!coupon.buyQuantity || !coupon.getQuantity) {
      return 0;
    }

    let discount = 0;
    const applicableItems = items.filter(item => 
      coupon.applicableProducts.length === 0 || 
      coupon.applicableProducts.includes(item.productId)
    );

    let totalQuantity = applicableItems.reduce((sum, item) => sum + item.quantity, 0);
    const setsEligible = Math.floor(totalQuantity / coupon.buyQuantity);

    if (setsEligible > 0) {
      // Find cheapest items to discount
      const sortedItems = applicableItems.sort((a, b) => a.price - b.price);
      let itemsToDiscount = setsEligible * coupon.getQuantity;

      for (const item of sortedItems) {
        if (itemsToDiscount <= 0) break;
        
        const discountQuantity = Math.min(itemsToDiscount, item.quantity);
        discount += discountQuantity * item.price;
        itemsToDiscount -= discountQuantity;
      }
    }

    return discount;
  }

  /**
   * Record coupon usage
   */
  static async recordCouponUsage(couponId, orderId, discountAmount, userId = null, guestEmail = null) {
    try {
      await models.CouponUsage.create({
        couponId,
        userId,
        orderId,
        discountAmount,
        guestEmail
      });

      // Update coupon usage count
      await models.Coupon.increment('usageCount', {
        where: { id: couponId }
      });

      return { success: true };
    } catch (error) {
      console.error('Error recording coupon usage:', error);
      return { success: false, message: 'Error recording coupon usage' };
    }
  }

  /**
   * Generate referral code for user
   */
  static async generateReferralCode(userId) {
    try {
      const user = await models.User.findByPk(userId);
      if (!user) {
        return { success: false, message: 'User not found' };
      }

      // Check if user already has an active referral code
      const existingReferral = await models.Referral.findOne({
        where: {
          referrerId: userId,
          status: 'pending'
        }
      });

      if (existingReferral) {
        return {
          success: true,
          referralCode: existingReferral.referralCode,
          message: 'Existing referral code retrieved'
        };
      }

      // Generate unique referral code
      let referralCode;
      let isUnique = false;
      let attempts = 0;

      while (!isUnique && attempts < 10) {
        referralCode = `${user.firstName.substring(0, 2).toUpperCase()}${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
        
        const existing = await models.Referral.findOne({
          where: { referralCode }
        });

        if (!existing) {
          isUnique = true;
        }
        attempts++;
      }

      if (!isUnique) {
        return { success: false, message: 'Unable to generate unique referral code' };
      }

      // Create referral record
      const referral = await models.Referral.create({
        referrerId: userId,
        referralCode,
        status: 'pending'
      });

      return {
        success: true,
        referralCode: referral.referralCode,
        message: 'Referral code generated successfully'
      };

    } catch (error) {
      console.error('Error generating referral code:', error);
      return { success: false, message: 'Error generating referral code' };
    }
  }

  /**
   * Process referral when referee makes first purchase
   */
  static async processReferral(referralCode, refereeUserId, orderId) {
    try {
      const referral = await models.Referral.findOne({
        where: {
          referralCode: referralCode.toUpperCase(),
          status: 'pending'
        },
        include: [
          {
            model: models.User,
            as: 'referrer'
          }
        ]
      });

      if (!referral) {
        return { success: false, message: 'Invalid referral code' };
      }

      // Check if referee is different from referrer
      if (referral.referrerId === refereeUserId) {
        return { success: false, message: 'Cannot refer yourself' };
      }

      // Update referral record
      await referral.update({
        refereeId: refereeUserId,
        orderId,
        status: 'completed',
        completedAt: new Date()
      });

      // Create reward for referrer (this could be a coupon or credit)
      const rewardAmount = 10.00; // $10 reward
      await this.createReferralReward(referral.referrerId, rewardAmount, referral.id);

      return {
        success: true,
        message: 'Referral processed successfully',
        rewardAmount
      };

    } catch (error) {
      console.error('Error processing referral:', error);
      return { success: false, message: 'Error processing referral' };
    }
  }

  /**
   * Create referral reward coupon
   */
  static async createReferralReward(userId, rewardAmount, referralId) {
    try {
      const user = await models.User.findByPk(userId);
      const rewardCode = `REFER${crypto.randomBytes(4).toString('hex').toUpperCase()}`;

      const coupon = await models.Coupon.create({
        code: rewardCode,
        name: 'Referral Reward',
        description: 'Thank you for referring a friend!',
        type: 'fixed_amount',
        value: rewardAmount,
        minimumOrderAmount: 0,
        usageLimit: 1,
        userUsageLimit: 1,
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        isActive: true,
        isReferralCoupon: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        firstTimeCustomerOnly: false,
        stackable: false,
        priority: 10,
        createdBy: null // System generated
      });

      // Update referral with reward info
      await models.Referral.update({
        rewardAmount,
        rewardType: 'discount',
        rewardedAt: new Date()
      }, {
        where: { id: referralId }
      });

      return {
        success: true,
        couponCode: rewardCode,
        rewardAmount
      };

    } catch (error) {
      console.error('Error creating referral reward:', error);
      return { success: false, message: 'Error creating referral reward' };
    }
  }

  /**
   * Record social media share
   */
  static async recordSocialShare(userId, platform, shareUrl, couponId = null) {
    try {
      const socialShare = await models.SocialShare.create({
        userId,
        couponId,
        platform,
        shareUrl,
        bonusEarned: 0, // Will be updated when verified
        isVerified: false
      });

      return {
        success: true,
        shareId: socialShare.id,
        message: 'Social share recorded'
      };

    } catch (error) {
      console.error('Error recording social share:', error);
      return { success: false, message: 'Error recording social share' };
    }
  }

  /**
   * Get user's referral statistics
   */
  static async getUserReferralStats(userId) {
    try {
      const stats = await models.Referral.findAll({
        where: { referrerId: userId },
        include: [
          {
            model: models.User,
            as: 'referee',
            attributes: ['firstName', 'lastName', 'email']
          },
          {
            model: models.Order,
            as: 'order',
            attributes: ['total', 'createdAt']
          }
        ]
      });

      const totalReferrals = stats.length;
      const completedReferrals = stats.filter(r => r.status === 'completed').length;
      const totalRewards = stats.reduce((sum, r) => sum + parseFloat(r.rewardAmount || 0), 0);

      return {
        success: true,
        data: {
          totalReferrals,
          completedReferrals,
          pendingReferrals: totalReferrals - completedReferrals,
          totalRewards,
          referrals: stats
        }
      };

    } catch (error) {
      console.error('Error getting referral stats:', error);
      return { success: false, message: 'Error getting referral stats' };
    }
  }

  /**
   * Verify social media share and award bonus
   */
  static async verifySocialShare(shareId, bonusAmount = 1.00) {
    try {
      const share = await models.SocialShare.findByPk(shareId);
      if (!share) {
        return { success: false, message: 'Share not found' };
      }

      if (share.isVerified) {
        return { success: false, message: 'Share already verified' };
      }

      await share.update({
        isVerified: true,
        verifiedAt: new Date(),
        bonusEarned: bonusAmount
      });

      // Create bonus coupon for user
      const bonusCode = `SHARE${crypto.randomBytes(3).toString('hex').toUpperCase()}`;
      await models.Coupon.create({
        code: bonusCode,
        name: 'Social Share Bonus',
        description: 'Thank you for sharing!',
        type: 'fixed_amount',
        value: bonusAmount,
        minimumOrderAmount: 0,
        usageLimit: 1,
        userUsageLimit: 1,
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        firstTimeCustomerOnly: false,
        stackable: false,
        priority: 5,
        createdBy: null
      });

      return {
        success: true,
        bonusCode,
        bonusAmount,
        message: 'Social share verified and bonus awarded'
      };

    } catch (error) {
      console.error('Error verifying social share:', error);
      return { success: false, message: 'Error verifying social share' };
    }
  }

  /**
   * Get available coupons for user
   */
  static async getAvailableCoupons(userId = null) {
    try {
      const whereClause = {
        isActive: true,
        validFrom: { [Op.lte]: new Date() },
        validUntil: { [Op.gte]: new Date() }
      };

      // Add usage limit check
      const coupons = await models.Coupon.findAll({
        where: whereClause,
        include: [
          {
            model: models.CouponUsage,
            as: 'usages',
            required: false
          }
        ],
        order: [['priority', 'DESC'], ['createdAt', 'DESC']]
      });

      // Filter coupons based on usage limits
      const availableCoupons = coupons.filter(coupon => {
        // Check global usage limit
        if (coupon.usageLimit && coupon.usages.length >= coupon.usageLimit) {
          return false;
        }

        // Check user-specific usage limit
        if (userId && coupon.userUsageLimit) {
          const userUsages = coupon.usages.filter(usage => usage.userId === userId).length;
          if (userUsages >= coupon.userUsageLimit) {
            return false;
          }
        }

        return true;
      });

      return {
        success: true,
        data: availableCoupons.map(coupon => ({
          id: coupon.id,
          code: coupon.code,
          name: coupon.name,
          description: coupon.description,
          type: coupon.type,
          value: coupon.value,
          minimumOrderAmount: coupon.minimumOrderAmount,
          maximumDiscountAmount: coupon.maximumDiscountAmount,
          validUntil: coupon.validUntil,
          isReferralCoupon: coupon.isReferralCoupon,
          socialShareBonus: coupon.socialShareBonus
        }))
      };

    } catch (error) {
      console.error('Error getting available coupons:', error);
      return { success: false, message: 'Error getting available coupons' };
    }
  }
}

module.exports = DiscountService;
