import React, { useState, useEffect } from 'react';
import { 
  ShareIcon, 
  HeartIcon, 
  ChatBubbleLeftIcon,
  PlayIcon,
  EyeIcon,
  CalendarIcon,
  UserGroupIcon,
  LinkIcon
} from '@heroicons/react/24/outline';
import SEOHead from '../components/seo/SEOHead';
import Breadcrumb from '../components/common/Breadcrumb';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { socialMediaService } from '../services/socialMediaService';

interface SocialMediaPost {
  id: string;
  platform: 'facebook' | 'instagram' | 'youtube' | 'tiktok' | 'medium';
  title: string;
  content: string;
  imageUrl?: string;
  videoUrl?: string;
  url: string;
  publishedAt: string;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
    views?: number;
  };
  author?: string;
}

interface SocialMediaStats {
  platform: string;
  followers: number;
  posts: number;
  engagement: number;
  growth: number;
  url: string;
  icon: string;
  color: string;
}

const SocialMedia: React.FC = () => {
  const [posts, setPosts] = useState<SocialMediaPost[]>([]);
  const [stats, setStats] = useState<SocialMediaStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Social Media', href: '/social-media' }
  ];

  const platformColors = {
    facebook: 'bg-blue-600',
    instagram: 'bg-gradient-to-r from-purple-500 to-pink-500',
    youtube: 'bg-red-600',
    tiktok: 'bg-black',
    medium: 'bg-gray-800'
  };

  const platformIcons = {
    facebook: '📘',
    instagram: '📷',
    youtube: '📺',
    tiktok: '🎵',
    medium: '📝'
  };

  useEffect(() => {
    fetchSocialMediaData();
  }, []);

  const fetchSocialMediaData = async () => {
    try {
      setLoading(true);
      const [postsResponse, statsResponse] = await Promise.all([
        socialMediaService.getPosts(),
        socialMediaService.getStats()
      ]);
      
      setPosts(postsResponse.data);
      setStats(statsResponse.data);
    } catch (error) {
      console.error('Error fetching social media data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPosts = selectedPlatform === 'all' 
    ? posts 
    : posts.filter(post => post.platform === selectedPlatform);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return <div className="text-2xl">📘</div>;
      case 'instagram':
        return <div className="text-2xl">📷</div>;
      case 'youtube':
        return <PlayIcon className="h-6 w-6 text-red-600" />;
      case 'tiktok':
        return <div className="text-2xl">🎵</div>;
      case 'medium':
        return <div className="text-2xl">📝</div>;
      default:
        return <ShareIcon className="h-6 w-6" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <>
      <SEOHead
        title="Social Media - Follow Nirvana Organics"
        description="Stay connected with Nirvana Organics across all social media platforms. Follow us on Facebook, Instagram, YouTube, TikTok, and Medium for the latest updates, products, and cannabis education."
        canonicalUrl="/social-media"
      />

      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumb items={breadcrumbItems} />
          
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Follow Us on Social Media</h1>
            <p className="text-lg text-gray-600">
              Stay connected with Nirvana Organics across all platforms for the latest updates, 
              product launches, educational content, and community discussions.
            </p>
          </div>

          {/* Social Media Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            {stats.map((stat) => (
              <a
                key={stat.platform}
                href={stat.url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${stat.color} text-white`}>
                    <span className="text-2xl">{stat.icon}</span>
                  </div>
                  <LinkIcon className="h-5 w-5 text-gray-400 group-hover:text-gray-600" />
                </div>
                <h3 className="font-semibold text-gray-900 capitalize mb-2">{stat.platform}</h3>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Followers</span>
                    <span className="font-medium">{formatNumber(stat.followers)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Posts</span>
                    <span className="font-medium">{stat.posts}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Engagement</span>
                    <span className="font-medium">{stat.engagement.toFixed(1)}%</span>
                  </div>
                  {stat.growth !== 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Growth</span>
                      <span className={`font-medium ${stat.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {stat.growth > 0 ? '+' : ''}{stat.growth.toFixed(1)}%
                      </span>
                    </div>
                  )}
                </div>
              </a>
            ))}
          </div>

          {/* Platform Filter */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter by Platform</h3>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setSelectedPlatform('all')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedPlatform === 'all'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                All Platforms
              </button>
              {['facebook', 'instagram', 'youtube', 'tiktok', 'medium'].map((platform) => (
                <button
                  key={platform}
                  onClick={() => setSelectedPlatform(platform)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors capitalize flex items-center space-x-2 ${
                    selectedPlatform === platform
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span>{platformIcons[platform as keyof typeof platformIcons]}</span>
                  <span>{platform}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Social Media Posts */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.length > 0 ? (
              filteredPosts.map((post) => (
                <div key={post.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                  {/* Post Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getPlatformIcon(post.platform)}
                        <div>
                          <h3 className="font-semibold text-gray-900 capitalize">{post.platform}</h3>
                          {post.author && (
                            <p className="text-sm text-gray-600">{post.author}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        {formatDate(post.publishedAt)}
                      </div>
                    </div>
                  </div>

                  {/* Post Media */}
                  {post.imageUrl && (
                    <div className="aspect-w-16 aspect-h-9">
                      <img
                        src={post.imageUrl}
                        alt={post.title}
                        className="w-full h-48 object-cover"
                      />
                    </div>
                  )}

                  {/* Post Content */}
                  <div className="p-4">
                    <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2">{post.title}</h4>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">{post.content}</p>

                    {/* Engagement Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <HeartIcon className="h-4 w-4 mr-1" />
                          {formatNumber(post.engagement.likes)}
                        </div>
                        <div className="flex items-center">
                          <ChatBubbleLeftIcon className="h-4 w-4 mr-1" />
                          {formatNumber(post.engagement.comments)}
                        </div>
                        <div className="flex items-center">
                          <ShareIcon className="h-4 w-4 mr-1" />
                          {formatNumber(post.engagement.shares)}
                        </div>
                        {post.engagement.views && (
                          <div className="flex items-center">
                            <EyeIcon className="h-4 w-4 mr-1" />
                            {formatNumber(post.engagement.views)}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* View Post Button */}
                    <a
                      href={post.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-center block font-medium"
                    >
                      View on {post.platform.charAt(0).toUpperCase() + post.platform.slice(1)}
                    </a>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No posts found</h3>
                <p className="text-gray-600">
                  {selectedPlatform === 'all' 
                    ? 'No social media posts are currently available.'
                    : `No posts found for ${selectedPlatform}.`
                  }
                </p>
              </div>
            )}
          </div>

          {/* Call to Action */}
          <div className="mt-12 bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Stay Connected</h2>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Follow us on your favorite social media platforms to stay up-to-date with the latest 
              products, educational content, and community discussions about cannabis wellness.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              {stats.map((stat) => (
                <a
                  key={stat.platform}
                  href={stat.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white text-primary-600 px-6 py-3 rounded-lg font-medium hover:bg-primary-50 transition-colors flex items-center space-x-2"
                >
                  <span>{stat.icon}</span>
                  <span className="capitalize">Follow on {stat.platform}</span>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SocialMedia;
