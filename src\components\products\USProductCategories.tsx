import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  SparklesIcon, 
  FireIcon, 
  BeakerIcon, 
  CubeIcon,
  StarIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

interface ProductCategory {
  id: string;
  name: string;
  description: string;
  image: string;
  href: string;
  cannabinoid: string;
  legalStatus: 'federally_legal' | 'state_dependent';
  thcContent: string;
  effects: string[];
  icon: React.ComponentType<any>;
  featured: boolean;
  compliance: string[];
}

const USProductCategories: React.FC = () => {
  const categories: ProductCategory[] = [
    {
      id: 'cbd',
      name: 'CBD Products',
      description: 'Premium CBD products for wellness and relaxation. Federally legal with less than 0.3% THC.',
      image: '/images/categories/cbd.jpg',
      href: '/shop?cannabinoid=CBD',
      cannabinoid: 'CBD',
      legalStatus: 'federally_legal',
      thcContent: '< 0.3% Delta-9 THC',
      effects: ['Relaxation', 'Wellness', 'Pain Relief', 'Sleep Support'],
      icon: SparklesIcon,
      featured: true,
      compliance: ['2018 Farm Bill Compliant', 'Third-Party Lab Tested', 'COA Available']
    },
    {
      id: 'thca',
      name: 'THC-A Flowers',
      description: 'High-quality THC-A hemp flowers. Raw, unheated form of THC that\'s federally compliant.',
      image: '/images/categories/thca.jpg',
      href: '/shop?cannabinoid=THC-A',
      cannabinoid: 'THC-A',
      legalStatus: 'federally_legal',
      thcContent: '< 0.3% Delta-9 THC',
      effects: ['Potential Therapeutic Benefits', 'Raw Cannabis Experience'],
      icon: FireIcon,
      featured: true,
      compliance: ['2018 Farm Bill Compliant', 'Raw THC-A Form', 'Lab Verified']
    },
    {
      id: 'delta8',
      name: 'Delta-8 THC',
      description: 'Mild psychoactive effects with Delta-8 THC. Legal in most states, check local laws.',
      image: '/images/categories/delta8.jpg',
      href: '/shop?cannabinoid=Delta-8',
      cannabinoid: 'Delta-8',
      legalStatus: 'state_dependent',
      thcContent: 'Delta-8 THC Derived',
      effects: ['Mild Euphoria', 'Relaxation', 'Appetite Enhancement'],
      icon: BeakerIcon,
      featured: true,
      compliance: ['Hemp-Derived', 'State Law Dependent', 'Lab Tested']
    },
    {
      id: 'delta9',
      name: 'Delta-9 THC',
      description: 'Traditional THC products within federal limits. Premium quality with precise dosing.',
      image: '/images/categories/delta9.jpg',
      href: '/shop?cannabinoid=Delta-9',
      cannabinoid: 'Delta-9',
      legalStatus: 'federally_legal',
      thcContent: '< 0.3% by dry weight',
      effects: ['Classic Cannabis Effects', 'Euphoria', 'Relaxation'],
      icon: CubeIcon,
      featured: true,
      compliance: ['Federal Limit Compliant', 'Precise Dosing', 'Quality Assured']
    }
  ];

  const legalNotice = {
    title: 'Legal Compliance Notice',
    content: 'All products are hemp-derived and contain less than 0.3% Delta-9 THC by dry weight, complying with the 2018 Farm Bill. However, state laws vary. Please verify the legality of these products in your state before purchasing.',
    restrictedStates: ['Idaho', 'Iowa', 'South Dakota']
  };

  return (
    <div className="bg-white py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Premium Hemp Products for the US Market
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our carefully curated selection of hemp-derived cannabis products, 
            all compliant with federal regulations and available for nationwide shipping.
          </p>
        </div>

        {/* Legal Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-12">
          <div className="flex items-start">
            <ShieldCheckIcon className="h-6 w-6 text-blue-600 mt-1 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">{legalNotice.title}</h3>
              <p className="text-blue-800 mb-3">{legalNotice.content}</p>
              <div className="text-sm text-blue-700">
                <strong>Note:</strong> We cannot ship to: {legalNotice.restrictedStates.join(', ')}
              </div>
            </div>
          </div>
        </div>

        {/* Product Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {categories.map((category) => (
            <div key={category.id} className="group relative">
              <Link to={category.href} className="block">
                <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  {/* Category Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={category.image}
                      alt={category.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.currentTarget.src = '/images/placeholder-category.jpg';
                      }}
                    />
                    {category.featured && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center">
                          <StarIcon className="h-3 w-3 mr-1" />
                          Featured
                        </span>
                      </div>
                    )}
                    <div className="absolute top-3 right-3">
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        category.legalStatus === 'federally_legal' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {category.legalStatus === 'federally_legal' ? 'Federally Legal' : 'Check State Laws'}
                      </span>
                    </div>
                  </div>

                  {/* Category Content */}
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <category.icon className="h-6 w-6 text-primary-600 mr-2" />
                      <h3 className="text-xl font-bold text-gray-900">{category.name}</h3>
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {category.description}
                    </p>

                    {/* THC Content */}
                    <div className="mb-4">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        THC Content
                      </span>
                      <p className="text-sm font-medium text-gray-900">{category.thcContent}</p>
                    </div>

                    {/* Effects */}
                    <div className="mb-4">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 block">
                        Effects
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {category.effects.slice(0, 2).map((effect, index) => (
                          <span
                            key={index}
                            className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                          >
                            {effect}
                          </span>
                        ))}
                        {category.effects.length > 2 && (
                          <span className="text-xs text-gray-500">
                            +{category.effects.length - 2} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Compliance Badges */}
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex flex-wrap gap-1">
                        {category.compliance.slice(0, 2).map((item, index) => (
                          <span
                            key={index}
                            className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded flex items-center"
                          >
                            <ShieldCheckIcon className="h-3 w-3 mr-1" />
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* CTA */}
                    <div className="mt-4">
                      <span className="text-primary-600 font-medium text-sm group-hover:text-primary-700 transition-colors">
                        Shop {category.name} →
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* Additional Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <ShieldCheckIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Lab Tested</h3>
            <p className="text-gray-600 text-sm">
              All products undergo rigorous third-party testing for potency, purity, and safety.
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <BeakerIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Federal Compliance</h3>
            <p className="text-gray-600 text-sm">
              Compliant with the 2018 Farm Bill and federal hemp regulations.
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FireIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Premium Quality</h3>
            <p className="text-gray-600 text-sm">
              Sourced from licensed cultivators and manufactured to the highest standards.
            </p>
          </div>
        </div>

        {/* State Law Disclaimer */}
        <div className="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="text-center">
            <h4 className="text-lg font-semibold text-yellow-900 mb-2">
              Important: State Law Compliance
            </h4>
            <p className="text-yellow-800 text-sm max-w-4xl mx-auto">
              While our products comply with federal law, individual state laws may vary. 
              It is your responsibility to verify that these products are legal in your state 
              before making a purchase. We reserve the right to cancel orders to states where 
              these products are prohibited.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default USProductCategories;
