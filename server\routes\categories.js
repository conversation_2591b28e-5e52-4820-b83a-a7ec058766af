const express = require('express');
const router = express.Router();
const categoryController = require('../controllers/categoryController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const { validateCategory, validateId, validatePagination } = require('../middleware/validation');

// @route   GET /api/categories
// @desc    Get all categories
// @access  Public
router.get('/', validatePagination, categoryController.getCategories);

// @route   GET /api/categories/navigation
// @desc    Get navigation categories
// @access  Public
router.get('/navigation', categoryController.getNavigationCategories);

// @route   GET /api/categories/stats
// @desc    Get category statistics
// @access  Private (Admin only)
router.get('/stats', authenticate, requireAdmin, categoryController.getCategoryStats);

// @route   GET /api/categories/:slug
// @desc    Get single category by slug
// @access  Public
router.get('/:slug', categoryController.getCategoryBySlug);

// @route   POST /api/categories
// @desc    Create new category
// @access  Private (Admin only)
router.post('/', authenticate, requireAdmin, validateCategory, categoryController.createCategory);

// @route   PUT /api/categories/:id
// @desc    Update category
// @access  Private (Admin only)
router.put('/:id', authenticate, requireAdmin, validateId('id'), validateCategory, categoryController.updateCategory);

// @route   DELETE /api/categories/:id
// @desc    Delete category
// @access  Private (Admin only)
router.delete('/:id', authenticate, requireAdmin, validateId('id'), categoryController.deleteCategory);

module.exports = router;
