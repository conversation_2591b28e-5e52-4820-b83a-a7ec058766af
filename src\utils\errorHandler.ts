import { AxiosError } from 'axios';

export interface ApiError {
  success: false;
  message: string;
  errors?: Array<{
    field?: string;
    message: string;
    code?: string;
  }>;
  code?: string;
  statusCode?: number;
}

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export class ErrorHandler {
  /**
   * Extract error message from various error types
   */
  static getErrorMessage(error: any): string {
    // Axios error
    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    // Network error
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      return 'Network connection failed. Please check your internet connection and try again.';
    }

    // Timeout error
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return 'Request timed out. Please try again.';
    }

    // Generic error with message
    if (error.message) {
      return error.message;
    }

    // Fallback
    return 'An unexpected error occurred. Please try again.';
  }

  /**
   * Extract validation errors from API response
   */
  static getValidationErrors(error: any): ValidationError[] {
    if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
      return error.response.data.errors.map((err: any) => ({
        field: err.field || err.param || 'general',
        message: err.message || err.msg || 'Invalid value',
        code: err.code
      }));
    }

    return [];
  }

  /**
   * Check if error is a validation error
   */
  static isValidationError(error: any): boolean {
    return error.response?.status === 400 && 
           error.response?.data?.errors && 
           Array.isArray(error.response.data.errors);
  }

  /**
   * Check if error is an authentication error
   */
  static isAuthError(error: any): boolean {
    return error.response?.status === 401;
  }

  /**
   * Check if error is a permission error
   */
  static isPermissionError(error: any): boolean {
    return error.response?.status === 403;
  }

  /**
   * Check if error is a not found error
   */
  static isNotFoundError(error: any): boolean {
    return error.response?.status === 404;
  }

  /**
   * Check if error is a server error
   */
  static isServerError(error: any): boolean {
    return error.response?.status >= 500;
  }

  /**
   * Get user-friendly error message based on error type
   */
  static getUserFriendlyMessage(error: any): string {
    if (this.isAuthError(error)) {
      return 'Your session has expired. Please log in again.';
    }

    if (this.isPermissionError(error)) {
      return 'You do not have permission to perform this action.';
    }

    if (this.isNotFoundError(error)) {
      return 'The requested resource was not found.';
    }

    if (this.isServerError(error)) {
      return 'Server error occurred. Please try again later.';
    }

    if (this.isValidationError(error)) {
      const validationErrors = this.getValidationErrors(error);
      if (validationErrors.length > 0) {
        return validationErrors[0].message;
      }
    }

    return this.getErrorMessage(error);
  }

  /**
   * Handle API errors with toast notifications
   */
  static handleApiError(error: any, dispatch: any, customMessage?: string) {
    const message = customMessage || this.getUserFriendlyMessage(error);
    
    dispatch({
      type: 'ui/addToast',
      payload: {
        type: 'error',
        title: 'Error',
        message,
        duration: 5000
      }
    });

    // Log error for debugging
    console.error('API Error:', error);
  }

  /**
   * Handle form validation errors
   */
  static handleFormErrors(error: any): Record<string, string> {
    const formErrors: Record<string, string> = {};

    if (this.isValidationError(error)) {
      const validationErrors = this.getValidationErrors(error);
      validationErrors.forEach(err => {
        formErrors[err.field] = err.message;
      });
    } else {
      formErrors.general = this.getUserFriendlyMessage(error);
    }

    return formErrors;
  }

  /**
   * Create standardized error response
   */
  static createErrorResponse(
    message: string, 
    statusCode: number = 500, 
    errors?: ValidationError[]
  ): ApiError {
    return {
      success: false,
      message,
      statusCode,
      errors
    };
  }

  /**
   * Handle payment errors specifically
   */
  static handlePaymentError(error: any): string {
    const errorCode = error.response?.data?.code;
    
    switch (errorCode) {
      case 'CARD_DECLINED':
        return 'Your card was declined. Please check your card details or try a different payment method.';
      case 'INSUFFICIENT_FUNDS':
        return 'Insufficient funds. Please check your account balance or try a different payment method.';
      case 'EXPIRED_CARD':
        return 'Your card has expired. Please use a different payment method.';
      case 'INVALID_CARD':
        return 'Invalid card information. Please check your card details and try again.';
      case 'CVV_FAILURE':
        return 'Invalid security code (CVV). Please check and try again.';
      case 'ADDRESS_VERIFICATION_FAILURE':
        return 'Address verification failed. Please check your billing address.';
      case 'GENERIC_DECLINE':
        return 'Payment was declined. Please contact your bank or try a different payment method.';
      default:
        return this.getUserFriendlyMessage(error);
    }
  }

  /**
   * Handle network connectivity issues
   */
  static handleNetworkError(dispatch: any) {
    dispatch({
      type: 'ui/addToast',
      payload: {
        type: 'error',
        title: 'Connection Error',
        message: 'Unable to connect to the server. Please check your internet connection.',
        duration: 8000
      }
    });
  }

  /**
   * Handle timeout errors
   */
  static handleTimeoutError(dispatch: any) {
    dispatch({
      type: 'ui/addToast',
      payload: {
        type: 'error',
        title: 'Request Timeout',
        message: 'The request took too long to complete. Please try again.',
        duration: 5000
      }
    });
  }

  /**
   * Retry mechanism for failed requests
   */
  static async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if (error.response?.status >= 400 && error.response?.status < 500) {
          throw error;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError;
  }

  /**
   * Log errors for monitoring
   */
  static logError(error: any, context?: string) {
    const errorInfo = {
      message: this.getErrorMessage(error),
      statusCode: error.response?.status,
      url: error.config?.url,
      method: error.config?.method,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    };

    // In production, send to error monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to Sentry, LogRocket, etc.
      console.error('Error logged:', errorInfo);
    } else {
      console.error('Development Error:', errorInfo);
    }
  }
}

// Export commonly used error types
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  PAYMENT_ERROR: 'PAYMENT_ERROR'
} as const;

// Export error handler instance
export default ErrorHandler;
