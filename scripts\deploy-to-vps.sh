#!/bin/bash

# ============================================================================
# Nirvana Organics VPS Deployment Script
# ============================================================================
# Deploys the application to a production VPS server
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration - UPDATE THESE VALUES
VPS_HOST="${VPS_HOST:-your-vps-ip}"
VPS_USER="${VPS_USER:-root}"
VPS_PORT="${VPS_PORT:-22}"
DEPLOY_PATH="/var/www/nirvana-organics"
DOMAIN="${DOMAIN:-shopnirvanaorganics.com}"

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔄 $1${NC}"
    echo "=============================================="
}

# Check prerequisites
check_prerequisites() {
    log_step "Checking Prerequisites"
    
    # Check if built files exist
    if [ ! -d "dist" ]; then
        log_error "Frontend build not found. Run 'npm run deploy:fullstack' first."
        exit 1
    fi
    
    if [ ! -d "server" ]; then
        log_error "Server directory not found."
        exit 1
    fi
    
    # Check SSH connection
    log_info "Testing SSH connection to $VPS_USER@$VPS_HOST..."
    if ssh -p $VPS_PORT -o ConnectTimeout=10 $VPS_USER@$VPS_HOST "echo 'SSH connection successful'" > /dev/null 2>&1; then
        log_success "SSH connection verified"
    else
        log_error "Cannot connect to VPS. Check your SSH configuration."
        exit 1
    fi
}

# Create deployment structure on VPS
setup_vps_structure() {
    log_step "Setting Up VPS Directory Structure"
    
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST << EOF
        # Create main directories
        mkdir -p $DEPLOY_PATH/{releases,shared/{uploads,logs},backups}
        
        # Set permissions
        chown -R www-data:www-data $DEPLOY_PATH
        chmod -R 755 $DEPLOY_PATH
        
        echo "VPS directory structure created"
EOF
    
    log_success "VPS directory structure ready"
}

# Upload files to VPS
upload_files() {
    log_step "Uploading Files to VPS"
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    RELEASE_PATH="$DEPLOY_PATH/releases/$TIMESTAMP"
    
    log_info "Creating release directory: $RELEASE_PATH"
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST "mkdir -p $RELEASE_PATH"
    
    # Upload frontend build
    log_info "Uploading frontend build..."
    scp -P $VPS_PORT -r dist/ $VPS_USER@$VPS_HOST:$RELEASE_PATH/
    
    # Upload admin build (if exists)
    if [ -d "dist-admin" ]; then
        log_info "Uploading admin build..."
        scp -P $VPS_PORT -r dist-admin/ $VPS_USER@$VPS_HOST:$RELEASE_PATH/
    fi
    
    # Upload server files
    log_info "Uploading server files..."
    scp -P $VPS_PORT -r server/ $VPS_USER@$VPS_HOST:$RELEASE_PATH/
    
    # Upload configuration files
    log_info "Uploading configuration files..."
    scp -P $VPS_PORT package.json $VPS_USER@$VPS_HOST:$RELEASE_PATH/
    scp -P $VPS_PORT ecosystem.config.js $VPS_USER@$VPS_HOST:$RELEASE_PATH/
    
    # Upload environment file
    if [ -f ".env.production" ]; then
        scp -P $VPS_PORT .env.production $VPS_USER@$VPS_HOST:$RELEASE_PATH/.env
    fi
    
    log_success "Files uploaded to $RELEASE_PATH"
    echo $RELEASE_PATH > .last_release_path
}

# Install dependencies on VPS
install_dependencies() {
    log_step "Installing Dependencies on VPS"
    
    RELEASE_PATH=$(cat .last_release_path)
    
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST << EOF
        cd $RELEASE_PATH
        
        # Install Node.js dependencies
        echo "Installing Node.js dependencies..."
        npm ci --production
        
        # Set proper permissions
        chown -R www-data:www-data $RELEASE_PATH
        chmod -R 755 $RELEASE_PATH
        
        echo "Dependencies installed successfully"
EOF
    
    log_success "Dependencies installed"
}

# Run database migrations
run_migrations() {
    log_step "Running Database Migrations"
    
    RELEASE_PATH=$(cat .last_release_path)
    
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST << EOF
        cd $RELEASE_PATH
        
        # Run database migrations
        echo "Running database migrations..."
        npm run migrate:prod || echo "Migration failed or no migrations to run"
        
        echo "Database migrations completed"
EOF
    
    log_success "Database migrations completed"
}

# Update symlinks and restart services
deploy_release() {
    log_step "Deploying Release"
    
    RELEASE_PATH=$(cat .last_release_path)
    
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST << EOF
        cd $DEPLOY_PATH
        
        # Backup current deployment
        if [ -L current ]; then
            cp -P current previous
        fi
        
        # Update current symlink
        ln -sfn $RELEASE_PATH current
        
        # Restart PM2 processes
        cd current
        pm2 reload ecosystem.config.js --env production || pm2 start ecosystem.config.js --env production
        pm2 save
        
        # Wait for application to start
        sleep 10
        
        echo "Release deployed successfully"
EOF
    
    log_success "Release deployed and services restarted"
}

# Verify deployment
verify_deployment() {
    log_step "Verifying Deployment"
    
    # Test application health
    log_info "Testing application health..."
    if curl -f -s http://$DOMAIN/health > /dev/null 2>&1; then
        log_success "Application health check passed"
    else
        log_warning "Health check failed - application may still be starting"
    fi
    
    # Check PM2 status
    log_info "Checking PM2 status..."
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST "pm2 status"
    
    log_success "Deployment verification completed"
}

# Cleanup old releases
cleanup_old_releases() {
    log_step "Cleaning Up Old Releases"
    
    ssh -p $VPS_PORT $VPS_USER@$VPS_HOST << EOF
        cd $DEPLOY_PATH/releases
        
        # Keep only the last 5 releases
        ls -t | tail -n +6 | xargs rm -rf
        
        echo "Old releases cleaned up"
EOF
    
    log_success "Old releases cleaned up"
}

# Main deployment function
main() {
    log_step "Nirvana Organics VPS Deployment"
    
    echo "Deploying to: $VPS_USER@$VPS_HOST:$DEPLOY_PATH"
    echo "Domain: $DOMAIN"
    echo ""
    
    # Confirmation
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deployment cancelled"
        exit 0
    fi
    
    # Run deployment steps
    check_prerequisites
    setup_vps_structure
    upload_files
    install_dependencies
    run_migrations
    deploy_release
    verify_deployment
    cleanup_old_releases
    
    # Success message
    log_step "Deployment Completed Successfully!"
    
    echo -e "${GREEN}"
    echo "🎉 Nirvana Organics Deployed Successfully!"
    echo "=============================================="
    echo "✅ Files uploaded and deployed"
    echo "✅ Dependencies installed"
    echo "✅ Database migrations run"
    echo "✅ Services restarted"
    echo "✅ Application verified"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Frontend: https://$DOMAIN"
    echo "   Admin:    https://$DOMAIN/admin"
    echo "   API:      https://$DOMAIN/api"
    echo "   Health:   https://$DOMAIN/health"
    echo ""
    echo "📊 Next Steps:"
    echo "   1. Test the application functionality"
    echo "   2. Verify SSL certificates"
    echo "   3. Monitor application logs: pm2 logs"
    echo "   4. Set up monitoring and backups"
    echo -e "${NC}"
    
    # Cleanup
    rm -f .last_release_path
}

# Show usage if no arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0"
    echo ""
    echo "Environment variables:"
    echo "  VPS_HOST     - VPS IP address or hostname"
    echo "  VPS_USER     - SSH username (default: root)"
    echo "  VPS_PORT     - SSH port (default: 22)"
    echo "  DOMAIN       - Your domain name"
    echo ""
    echo "Example:"
    echo "  VPS_HOST=************* VPS_USER=ubuntu DOMAIN=shopnirvanaorganics.com $0"
    exit 0
fi

# Run main function
main
