const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { Sequelize } = require('sequelize');
const { User, Role, sequelize } = require('../models');
require('dotenv').config();

// Authentication Service Class
class AuthService {
  constructor() {
    this.User = User;
    this.Role = Role;
    this.sequelize = sequelize;
  }

  async initialize() {
    try {
      await this.sequelize.authenticate();
      console.log('✅ AuthService database connection established successfully');
      return true;
    } catch (error) {
      console.error('❌ AuthService database connection failed:', error);
      throw error;
    }
  }

  async register(userData) {
    try {
      const { firstName, lastName, email, password, phone, dateOfBirth } = userData;

      // Check if user already exists
      const existingUser = await this.User.findOne({ where: { email } });
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Validate age (must be 21+ for cannabis products)
      if (dateOfBirth) {
        const age = Math.floor((new Date() - new Date(dateOfBirth)) / (365.25 * 24 * 60 * 60 * 1000));
        if (age < 21) {
          throw new Error('You must be at least 21 years old to register');
        }
      }

      // Find or create customer role
      let customerRole = await this.Role.findOne({ where: { name: 'customer' } });
      if (!customerRole) {
        customerRole = await this.Role.create({
          name: 'customer',
          displayName: 'Customer',
          description: 'Regular customer user',
          permissions: {},
          priority: 10
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user
      const user = await this.User.create({
        firstName,
        lastName,
        email,
        password: hashedPassword,
        phone,
        dateOfBirth,
        roleId: customerRole.id
      });

      // Generate email verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const hashedToken = crypto.createHash('sha256').update(verificationToken).digest('hex');
      
      await user.update({
        emailVerificationToken: hashedToken,
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      });

      // Return user without password
      const userResponse = user.toJSON();
      delete userResponse.password;
      delete userResponse.emailVerificationToken;

      return {
        user: userResponse,
        verificationToken
      };
    } catch (error) {
      throw new Error(`Registration failed: ${error.message}`);
    }
  }

  async login(email, password) {
    try {
      // Find user by email with Role association
      const user = await this.User.findOne({ 
        where: { email },
        include: [
          {
            model: this.Role,
            as: 'Role',
            attributes: ['id', 'name', 'displayName']
          }
        ]
      });
      
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if account is locked
      if (user.lockUntil && user.lockUntil > Date.now()) {
        throw new Error('Account is temporarily locked due to too many failed login attempts');
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        // Increment login attempts
        await this.incrementLoginAttempts(user);
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Reset login attempts on successful login
      await this.resetLoginAttempts(user);

      // Generate tokens
      const accessToken = this.generateJWT(user);
      const refreshToken = this.generateRefreshToken(user);

      // Return user without sensitive data
      const userResponse = user.toJSON();
      delete userResponse.password;
      delete userResponse.emailVerificationToken;
      delete userResponse.passwordResetToken;

      return {
        user: userResponse,
        accessToken,
        refreshToken
      };
    } catch (error) {
      throw new Error(`Login failed: ${error.message}`);
    }
  }

  async getUserById(id) {
    try {
      const user = await this.User.findByPk(id, {
        attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] },
        include: [
          {
            model: this.Role,
            as: 'Role',
            attributes: ['id', 'name', 'displayName']
          }
        ]
      });
      
      if (!user) {
        throw new Error('User not found');
      }

      return user;
    } catch (error) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  generateJWT(user) {
    return jwt.sign(
      { 
        id: user.id, 
        email: user.email, 
        role: user.Role ? user.Role.name : user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );
  }

  generateRefreshToken(user) {
    return jwt.sign(
      { 
        id: user.id, 
        type: 'refresh' 
      },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
    );
  }

  async incrementLoginAttempts(user) {
    // If we have a previous lock that has expired, restart at 1
    if (user.lockUntil && user.lockUntil < Date.now()) {
      return user.update({
        loginAttempts: 1,
        lockUntil: null
      });
    }
    
    const updates = { loginAttempts: user.loginAttempts + 1 };
    
    // Lock account after 5 failed attempts for 2 hours
    if (user.loginAttempts + 1 >= 5) {
      updates.lockUntil = Date.now() + 2 * 60 * 60 * 1000; // 2 hours
    }
    
    return user.update(updates);
  }

  async resetLoginAttempts(user) {
    return user.update({
      loginAttempts: 0,
      lockUntil: null,
      lastLoginAt: new Date()
    });
  }

  async verifyEmail(token) {
    try {
      const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
      
      const user = await this.User.findOne({
        where: {
          emailVerificationToken: hashedToken,
          emailVerificationExpires: { [Sequelize.Op.gt]: new Date() }
        }
      });

      if (!user) {
        throw new Error('Invalid or expired verification token');
      }

      await user.update({
        isEmailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null
      });

      return { message: 'Email verified successfully' };
    } catch (error) {
      throw new Error(`Email verification failed: ${error.message}`);
    }
  }

  async refreshToken(refreshToken) {
    try {
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
      
      const user = await this.getUserById(decoded.id);
      if (!user || !user.isActive) {
        throw new Error('Invalid refresh token');
      }

      const newAccessToken = this.generateJWT(user);
      return { accessToken: newAccessToken };
    } catch (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }
}

module.exports = new AuthService();
