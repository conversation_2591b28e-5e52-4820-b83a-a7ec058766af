import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { guestOrderService, GuestOrder } from '../services/guestOrderService';
import { useAppDispatch } from '../hooks/redux';
import { addToast } from '../store/slices/uiSlice';
import LoadingSpinner from '../components/common/LoadingSpinner';
import SEOHead from '../components/seo/SEOHead';
import {
  MagnifyingGlassIcon,
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  EnvelopeIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

const TrackOrder: React.FC = () => {
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();

  const [trackingData, setTrackingData] = useState<{
    order: GuestOrder;
    trackingHistory: Array<{
      status: string;
      description: string;
      timestamp: string;
      location?: string;
    }>;
  } | null>(null);

  const [formData, setFormData] = useState({
    email: '',
    orderNumber: ''
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Check for tracking token in URL params
  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      handleTrackByToken(token);
    }
  }, [searchParams]);

  const handleTrackByToken = async (token: string) => {
    setLoading(true);
    try {
      const response = await guestOrderService.trackGuestOrderByToken(token);
      setTrackingData(response.data.data);
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Tracking Failed',
        message: error.response?.data?.message || 'Failed to track order'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleTrackByEmailAndOrder = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: Record<string, string> = {};
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (!formData.orderNumber.trim()) {
      newErrors.orderNumber = 'Order number is required';
    }
    
    setErrors(newErrors);
    
    if (Object.keys(newErrors).length > 0) {
      return;
    }

    setLoading(true);
    try {
      const response = await guestOrderService.trackGuestOrder(
        formData.email,
        formData.orderNumber
      );
      setTrackingData(response.data.data);
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Order Not Found',
        message: error.response?.data?.message || 'Order not found with the provided information'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      case 'shipped':
        return <TruckIcon className="h-5 w-5 text-purple-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead
        title="Track Your Order - Order Status and Shipping Information"
        description="Track your cannabis product order status and shipping information. Get real-time updates on your hemp product delivery."
        keywords={['track order', 'order status', 'shipping tracking', 'cannabis delivery', 'hemp order tracking']}
        canonicalUrl="/track-order"
      />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <TruckIcon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Track Your Order</h1>
            <p className="text-gray-600">
              Enter your order information below to track your cannabis product delivery
            </p>
          </div>

          {!trackingData ? (
            /* Tracking Form */
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <form onSubmit={handleTrackByEmailAndOrder} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                      errors.email ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter the email used for your order"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Order Number *
                  </label>
                  <input
                    type="text"
                    value={formData.orderNumber}
                    onChange={(e) => handleInputChange('orderNumber', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                      errors.orderNumber ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your order number (e.g., NO-12345)"
                  />
                  {errors.orderNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.orderNumber}</p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 disabled:opacity-50 transition-colors"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="small" />
                      <span className="ml-2">Tracking Order...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
                      Track Order
                    </div>
                  )}
                </button>
              </form>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Need Help?</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center text-gray-600">
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    <span>Email: <EMAIL></span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <PhoneIcon className="h-4 w-4 mr-2" />
                    <span>Phone: (*************</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Order Tracking Results */
            <div className="space-y-6">
              {/* Order Summary */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      Order #{trackingData.order.orderNumber}
                    </h2>
                    <p className="text-gray-600">
                      Placed on {new Date(trackingData.order.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trackingData.order.status)}`}>
                      {getStatusIcon(trackingData.order.status)}
                      <span className="ml-2 capitalize">{trackingData.order.status}</span>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                  <div className="space-y-3">
                    {trackingData.order.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center py-2">
                        <div>
                          <p className="font-medium text-gray-900">{item.productName}</p>
                          {item.variantName && (
                            <p className="text-sm text-gray-600">{item.variantName}</p>
                          )}
                          <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">${item.total.toFixed(2)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="border-t border-gray-200 mt-4 pt-4">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>Total</span>
                      <span>${trackingData.order.total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Shipping Address */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                <div className="text-gray-600">
                  <p>{trackingData.order.shippingAddress.firstName} {trackingData.order.shippingAddress.lastName}</p>
                  <p>{trackingData.order.shippingAddress.address1}</p>
                  {trackingData.order.shippingAddress.address2 && (
                    <p>{trackingData.order.shippingAddress.address2}</p>
                  )}
                  <p>
                    {trackingData.order.shippingAddress.city}, {trackingData.order.shippingAddress.state} {trackingData.order.shippingAddress.zipCode}
                  </p>
                </div>
              </div>

              {/* Tracking History */}
              {trackingData.trackingHistory && trackingData.trackingHistory.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Tracking History</h3>
                  <div className="space-y-4">
                    {trackingData.trackingHistory.map((event, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {getStatusIcon(event.status)}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{event.description}</p>
                          <p className="text-sm text-gray-600">
                            {new Date(event.timestamp).toLocaleString()}
                            {event.location && ` • ${event.location}`}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    setTrackingData(null);
                    setFormData({ email: '', orderNumber: '' });
                  }}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Track Another Order
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrackOrder;
