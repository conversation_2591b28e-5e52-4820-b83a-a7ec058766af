/**
 * Comprehensive Test Suite for Nirvana Organics E-commerce Platform
 * Tests all implemented functionality including real-time monitoring and email notifications
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { Server } from 'socket.io';
import Client from 'socket.io-client';
import http from 'http';

// Import server and services
const app = require('../server/index');
const { models } = require('../server/models');
const TestHelpers = require('./helpers/testHelpers');
const realTimeService = require('../server/services/realTimeService');
const emailNotificationService = require('../server/services/emailNotificationService');

describe('Nirvana Organics E-commerce Platform - Comprehensive Test Suite', () => {
  let server;
  let testUser;
  let testAdmin;
  let testProduct;
  let testOrder;
  let userToken;
  let adminToken;
  let clientSocket;

  beforeAll(async () => {
    // Setup test environment
    await TestHelpers.setupTestEnvironment();

    // Create HTTP server for testing
    server = http.createServer(app);
    
    // Initialize real-time service
    realTimeService.initialize(server);
    
    // Start server
    await new Promise((resolve) => {
      server.listen(0, resolve);
    });

    const port = server.address().port;

    // Create test users
    testUser = await TestHelpers.generateTestUser();
    testAdmin = await TestHelpers.generateTestAdmin();
    
    // Generate tokens
    userToken = TestHelpers.generateTestToken(testUser);
    adminToken = TestHelpers.generateTestToken(testAdmin);

    // Create test product
    testProduct = await TestHelpers.generateTestProduct();

    // Create client socket for testing
    clientSocket = new Client(`http://localhost:${port}`, {
      transports: ['websocket']
    });
  });

  afterAll(async () => {
    if (clientSocket) {
      clientSocket.close();
    }
    if (server) {
      server.close();
    }
    await TestHelpers.teardownTestEnvironment();
  });

  describe('Authentication System', () => {
    test('should register a new user', async () => {
      const userData = {
        firstName: 'New',
        lastName: 'User',
        email: TestHelpers.generateRandomEmail(),
        password: 'NewPassword123!',
        confirmPassword: 'NewPassword123!',
        dateOfBirth: '1990-01-01' // 21+ years old
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user.email.toLowerCase()).toBe(userData.email.toLowerCase());
      expect(response.body.data).toHaveProperty('token');
    });

    test('should login existing user', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'TestPassword123!'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.id).toBe(testUser.id);
    });

    test('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should protect admin routes', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should allow admin access with valid token', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Product Management', () => {
    test('should get all products', async () => {
      const response = await request(app)
        .get('/api/products')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.products)).toBe(true);
    });

    test('should get product by ID', async () => {
      const response = await request(app)
        .get(`/api/products/${testProduct.id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(testProduct.id);
      TestHelpers.validateProductStructure(response.body.data);
    });

    test('should create product as admin', async () => {
      const productData = {
        name: 'New Test Product',
        description: 'A new test product',
        price: 39.99,
        sku: `NEW-${Date.now()}`,
        category: 'supplements',
        subcategory: 'vitamins',
        quantity: 50,
        trackQuantity: true,
        isActive: true
      };

      const response = await request(app)
        .post('/api/admin/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(productData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(productData.name);
      TestHelpers.validateProductStructure(response.body.data);
    });

    test('should update product as admin', async () => {
      const updateData = {
        name: 'Updated Test Product',
        price: 49.99
      };

      const response = await request(app)
        .put(`/api/admin/products/${testProduct.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.price).toBe(updateData.price);
    });
  });

  describe('Cart Management', () => {
    test('should add item to cart', async () => {
      const response = await request(app)
        .post('/api/cart/add')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          productId: testProduct.id,
          quantity: 2
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].productId).toBe(testProduct.id);
      expect(response.body.data.items[0].quantity).toBe(2);
    });

    test('should get user cart', async () => {
      const response = await request(app)
        .get('/api/cart')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('items');
    });

    test('should update cart item quantity', async () => {
      const response = await request(app)
        .put('/api/cart/update')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          productId: testProduct.id,
          quantity: 3
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items[0].quantity).toBe(3);
    });

    test('should remove item from cart', async () => {
      const response = await request(app)
        .delete(`/api/cart/remove/${testProduct.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Order Management', () => {
    beforeEach(async () => {
      // Add item to cart before each order test
      await request(app)
        .post('/api/cart/add')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          productId: testProduct.id,
          quantity: 1
        });
    });

    test('should create order', async () => {
      const orderData = {
        billingAddress: {
          firstName: 'Test',
          lastName: 'User',
          address1: '123 Test St',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345',
          country: 'USA'
        },
        shippingAddress: {
          firstName: 'Test',
          lastName: 'User',
          address1: '123 Test St',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345',
          country: 'USA'
        },
        paymentMethod: 'credit_card',
        shippingMethod: 'regular',
        shippingCost: 9.99,
        sourceId: 'test-source-id'
      };

      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order).toHaveProperty('orderNumber');
      TestHelpers.validateOrderStructure(response.body.data.order);

      testOrder = response.body.data.order;
    });

    test('should get user orders', async () => {
      const response = await request(app)
        .get('/api/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);
    });

    test('should get order by ID', async () => {
      if (!testOrder) {
        testOrder = await TestHelpers.generateTestOrder({ userId: testUser.id });
      }

      const response = await request(app)
        .get(`/api/orders/${testOrder.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(testOrder.id);
    });

    test('should update order status as admin', async () => {
      if (!testOrder) {
        testOrder = await TestHelpers.generateTestOrder({ userId: testUser.id });
      }

      const response = await request(app)
        .put(`/api/admin/orders/${testOrder.id}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ status: 'processing' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('processing');
    });
  });

  describe('Real-Time Order Monitoring', () => {
    test('should connect to WebSocket server', (done) => {
      clientSocket.on('connect', () => {
        expect(clientSocket.connected).toBe(true);
        done();
      });
    });

    test('should authenticate admin user', (done) => {
      clientSocket.emit('authenticate', {
        token: adminToken,
        userRole: 'admin'
      });

      clientSocket.on('initial-orders', (orders) => {
        expect(Array.isArray(orders)).toBe(true);
        done();
      });
    });

    test('should receive real-time metrics', (done) => {
      clientSocket.on('initial-metrics', (metrics) => {
        expect(metrics).toHaveProperty('today');
        expect(metrics).toHaveProperty('week');
        expect(metrics).toHaveProperty('month');
        expect(metrics).toHaveProperty('activeCustomers');
        expect(metrics).toHaveProperty('pendingOrders');
        done();
      });
    });

    test('should broadcast new order', (done) => {
      const orderData = {
        id: 999,
        orderNumber: 'TEST-999',
        customer: {
          name: 'Test Customer',
          email: '<EMAIL>',
          membershipType: 'regular'
        },
        items: [{
          productName: 'Test Product',
          sku: 'TEST-001',
          quantity: 1,
          price: 29.99
        }],
        pricing: {
          total: 29.99
        },
        status: 'pending',
        createdAt: new Date().toISOString(),
        priority: 'normal'
      };

      clientSocket.on('new-order', (receivedOrder) => {
        expect(receivedOrder).toHaveProperty('id');
        expect(receivedOrder).toHaveProperty('orderNumber');
        done();
      });

      realTimeService.broadcastNewOrder(orderData);
    });

    test('should calculate real-time metrics', async () => {
      const metrics = await realTimeService.calculateRealTimeMetrics();

      expect(metrics).toHaveProperty('today');
      expect(metrics).toHaveProperty('week');
      expect(metrics).toHaveProperty('month');
      expect(typeof metrics.today.orders).toBe('number');
      expect(typeof metrics.today.revenue).toBe('number');
      expect(typeof metrics.activeCustomers).toBe('number');
    });
  });

  describe('Email Notification System', () => {
    test('should queue order confirmation email', async () => {
      const orderData = {
        id: testOrder?.id || 1,
        orderNumber: testOrder?.orderNumber || 'TEST-001',
        customer: {
          name: `${testUser.firstName} ${testUser.lastName}`,
          email: testUser.email
        },
        items: [{
          productName: testProduct.name,
          sku: testProduct.sku,
          quantity: 1,
          price: testProduct.price
        }],
        pricing: {
          subtotal: testProduct.price,
          tax: 2.40,
          shipping: 9.99,
          discount: 0,
          total: testProduct.price + 2.40 + 9.99
        },
        shipping: {
          method: 'regular',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'TS',
            zipCode: '12345',
            country: 'USA'
          }
        },
        createdAt: new Date().toISOString(),
        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      };

      // This should not throw an error
      await expect(emailNotificationService.sendOrderConfirmation(orderData))
        .resolves.toBeUndefined();
    });

    test('should queue admin notification email', async () => {
      const orderData = {
        id: testOrder?.id || 1,
        orderNumber: testOrder?.orderNumber || 'TEST-001',
        customer: {
          name: `${testUser.firstName} ${testUser.lastName}`,
          email: testUser.email,
          membershipType: 'regular'
        },
        items: [{
          productName: testProduct.name,
          sku: testProduct.sku,
          quantity: 1,
          price: testProduct.price
        }],
        pricing: {
          total: testProduct.price + 12.39
        },
        shipping: {
          method: 'regular',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'TS',
            zipCode: '12345',
            country: 'USA'
          }
        },
        payment: {
          status: 'completed',
          method: 'credit_card',
          transactionId: 'txn_test123'
        },
        coupons: [],
        priority: 'normal',
        createdAt: new Date().toISOString()
      };

      await expect(emailNotificationService.sendAdminOrderNotification(orderData))
        .resolves.toBeUndefined();
    });

    test('should get email statistics', async () => {
      const stats = await emailNotificationService.getEmailStats();

      expect(stats).toHaveProperty('sent');
      expect(stats).toHaveProperty('failed');
      expect(stats).toHaveProperty('pending');
      expect(typeof stats.sent).toBe('number');
      expect(typeof stats.failed).toBe('number');
      expect(typeof stats.pending).toBe('number');
    });
  });

  describe('Email Management API', () => {
    test('should get email logs as admin', async () => {
      const response = await request(app)
        .get('/api/admin/email-management/logs')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('logs');
      expect(response.body.data).toHaveProperty('pagination');
    });

    test('should get email statistics as admin', async () => {
      const response = await request(app)
        .get('/api/admin/email-management/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
    });

    test('should get admin email settings', async () => {
      const response = await request(app)
        .get('/api/admin/email-management/settings')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('adminNotificationEmails');
      expect(response.body.data).toHaveProperty('emailFromName');
      expect(response.body.data).toHaveProperty('emailFromAddress');
    });

    test('should test email configuration', async () => {
      const response = await request(app)
        .post('/api/admin/email-management/test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Admin Dashboard Features', () => {
    test('should get analytics data', async () => {
      const response = await request(app)
        .get('/api/admin/analytics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
    });

    test('should get all users as admin', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);
    });

    test('should get all orders as admin', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/non-existent-route')
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    test('should handle invalid product ID', async () => {
      const response = await request(app)
        .get('/api/products/999999')
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    test('should handle invalid order ID', async () => {
      const response = await request(app)
        .get('/api/orders/999999')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    test('should handle WebSocket authentication errors', (done) => {
      const invalidSocket = new Client(`http://localhost:${server.address().port}`, {
        transports: ['websocket']
      });

      invalidSocket.on('connect', () => {
        invalidSocket.emit('authenticate', {
          token: 'invalid-token',
          userRole: 'admin'
        });
      });

      invalidSocket.on('auth-error', (error) => {
        expect(error).toHaveProperty('message');
        invalidSocket.close();
        done();
      });
    });
  });

  describe('Performance Tests', () => {
    test('should handle multiple concurrent requests', async () => {
      const requests = [];
      
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .get('/api/products')
            .expect(200)
        );
      }

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.body.success).toBe(true);
      });
    });

    test('should respond to API requests within acceptable time', async () => {
      const startTime = Date.now();
      
      await request(app)
        .get('/api/products')
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      
      // Should respond within 2 seconds
      expect(responseTime).toBeLessThan(2000);
    });

    test('should handle real-time metrics calculation efficiently', async () => {
      const startTime = Date.now();
      
      await realTimeService.calculateRealTimeMetrics();
      
      const responseTime = Date.now() - startTime;
      
      // Should calculate within 1 second
      expect(responseTime).toBeLessThan(1000);
    });
  });

  describe('Data Validation', () => {
    test('should validate user registration data', async () => {
      const invalidUserData = {
        firstName: '',
        lastName: '',
        email: 'invalid-email',
        password: '123'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidUserData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body).toHaveProperty('errors');
    });

    test('should validate product creation data', async () => {
      const invalidProductData = {
        name: '',
        price: -10,
        sku: ''
      };

      const response = await request(app)
        .post('/api/admin/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidProductData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('should validate order creation data', async () => {
      const invalidOrderData = {
        billingAddress: {},
        shippingAddress: {},
        paymentMethod: ''
      };

      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send(invalidOrderData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
