import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
  name: string;
  href?: string;
  current?: boolean;
}

interface AdminBreadcrumbProps {
  items?: BreadcrumbItem[];
}

const AdminBreadcrumb: React.FC<AdminBreadcrumbProps> = ({ items }) => {
  const location = useLocation();

  // Auto-generate breadcrumbs from URL if not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Dashboard
    breadcrumbs.push({
      name: 'Dashboard',
      href: '/admin/dashboard'
    });

    // Skip 'admin' segment and process the rest
    const adminIndex = pathSegments.indexOf('admin');
    const relevantSegments = pathSegments.slice(adminIndex + 1);

    let currentPath = '/admin';
    
    relevantSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === relevantSegments.length - 1;
      
      // Convert segment to readable name
      const name = formatSegmentName(segment, currentPath);
      
      breadcrumbs.push({
        name,
        href: isLast ? undefined : currentPath,
        current: isLast
      });
    });

    return breadcrumbs;
  };

  const formatSegmentName = (segment: string, fullPath: string): string => {
    // Handle special cases and IDs
    if (segment === 'new') return 'Add New';
    if (segment === 'edit') return 'Edit';
    if (segment.match(/^\d+$/)) return `#${segment}`;
    
    // Handle query parameters for status filters
    const urlParams = new URLSearchParams(location.search);
    const status = urlParams.get('status');
    
    if (status && fullPath.includes(segment)) {
      const statusMap: { [key: string]: string } = {
        pending: 'Pending',
        approved: 'Approved',
        rejected: 'Rejected',
        processing: 'Processing',
        shipped: 'Shipped',
        delivered: 'Delivered',
        cancelled: 'Cancelled'
      };
      
      if (statusMap[status]) {
        return `${formatBasicSegment(segment)} - ${statusMap[status]}`;
      }
    }
    
    return formatBasicSegment(segment);
  };

  const formatBasicSegment = (segment: string): string => {
    // Convert kebab-case and snake_case to Title Case
    return segment
      .replace(/[-_]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  return (
    <nav className="flex mb-6" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-4">
        <li>
          <div>
            <Link 
              to="/admin/dashboard" 
              className="text-gray-400 hover:text-gray-500 transition-colors duration-200"
            >
              <HomeIcon className="flex-shrink-0 h-5 w-5" aria-hidden="true" />
              <span className="sr-only">Dashboard</span>
            </Link>
          </div>
        </li>
        
        {breadcrumbItems.slice(1).map((item, index) => (
          <li key={item.name}>
            <div className="flex items-center">
              <ChevronRightIcon 
                className="flex-shrink-0 h-5 w-5 text-gray-300" 
                aria-hidden="true" 
              />
              {item.href && !item.current ? (
                <Link
                  to={item.href}
                  className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors duration-200"
                  aria-current={item.current ? 'page' : undefined}
                >
                  {item.name}
                </Link>
              ) : (
                <span
                  className="ml-4 text-sm font-medium text-gray-900"
                  aria-current={item.current ? 'page' : undefined}
                >
                  {item.name}
                </span>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default AdminBreadcrumb;
