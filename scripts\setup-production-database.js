#!/usr/bin/env node

/**
 * Production Database Setup Script
 * Configures and validates database for production deployment
 */

const { Sequelize } = require('sequelize');
require('dotenv').config();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔧${colors.reset} ${msg}`)
};

/**
 * Validate environment variables
 */
function validateEnvironment() {
  log.step('Validating database environment variables...');

  const requiredVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    log.error(`Missing required environment variables: ${missingVars.join(', ')}`);
    return false;
  }

  // Check for placeholder values
  const placeholderVars = requiredVars.filter(varName => 
    process.env[varName] && (
      process.env[varName].includes('CHANGE_') ||
      process.env[varName].includes('your_') ||
      process.env[varName].includes('placeholder')
    )
  );

  if (placeholderVars.length > 0) {
    log.warning(`Found placeholder values in: ${placeholderVars.join(', ')}`);
    log.warning('Please update these with actual production values');
  }

  log.success('Environment variables validation completed');
  return true;
}

/**
 * Create production database connection
 */
function createProductionSequelize() {
  return new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      dialect: 'mysql',
      timezone: '+00:00',
      logging: false, // Disable logging for production
      pool: {
        max: 20, // Increased for production
        min: 5,
        acquire: 60000, // Increased timeout
        idle: 10000
      },
      dialectOptions: {
        charset: 'utf8mb4',
        supportBigNumbers: true,
        bigNumberStrings: true,
        // SSL configuration for production
        ssl: {
          require: true,
          rejectUnauthorized: false
        },
        // Connection timeout
        connectTimeout: 60000,
        acquireTimeout: 60000,
        timeout: 60000
      },
      define: {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        timestamps: true,
        underscored: false
      },
      // Retry configuration
      retry: {
        match: [
          /ETIMEDOUT/,
          /EHOSTUNREACH/,
          /ECONNRESET/,
          /ECONNREFUSED/,
          /TIMEOUT/,
          /ESOCKETTIMEDOUT/,
          /EHOSTUNREACH/,
          /EPIPE/,
          /EAI_AGAIN/,
          /SequelizeConnectionError/,
          /SequelizeConnectionRefusedError/,
          /SequelizeHostNotFoundError/,
          /SequelizeHostNotReachableError/,
          /SequelizeInvalidConnectionError/,
          /SequelizeConnectionTimedOutError/
        ],
        max: 5
      }
    }
  );
}

/**
 * Test database connection
 */
async function testDatabaseConnection() {
  log.step('Testing database connection...');

  const sequelize = createProductionSequelize();

  try {
    await sequelize.authenticate();
    log.success('Database connection successful');

    // Test basic query
    const [results] = await sequelize.query('SELECT 1 as test');
    if (results && results[0] && results[0].test === 1) {
      log.success('Database query test successful');
    }

    // Get database info
    const [dbInfo] = await sequelize.query(`
      SELECT 
        VERSION() as version,
        DATABASE() as database_name,
        USER() as current_user,
        @@character_set_database as charset,
        @@collation_database as collation
    `);

    log.info('Database Information:');
    console.log(`  Version: ${dbInfo[0].version}`);
    console.log(`  Database: ${dbInfo[0].database_name}`);
    console.log(`  User: ${dbInfo[0].current_user}`);
    console.log(`  Charset: ${dbInfo[0].charset}`);
    console.log(`  Collation: ${dbInfo[0].collation}`);

    await sequelize.close();
    return true;

  } catch (error) {
    log.error(`Database connection failed: ${error.message}`);
    
    // Provide specific error guidance
    if (error.message.includes('ENOTFOUND')) {
      log.error('DNS resolution failed. Check DB_HOST value.');
    } else if (error.message.includes('ECONNREFUSED')) {
      log.error('Connection refused. Check if database server is running and DB_PORT is correct.');
    } else if (error.message.includes('Access denied')) {
      log.error('Authentication failed. Check DB_USER and DB_PASSWORD values.');
    } else if (error.message.includes('Unknown database')) {
      log.error('Database not found. Check DB_NAME value.');
    }

    await sequelize.close().catch(() => {});
    return false;
  }
}

/**
 * Check database permissions
 */
async function checkDatabasePermissions() {
  log.step('Checking database permissions...');

  const sequelize = createProductionSequelize();

  try {
    // Check user privileges
    const [privileges] = await sequelize.query(`
      SHOW GRANTS FOR CURRENT_USER()
    `);

    log.info('Current user privileges:');
    privileges.forEach(privilege => {
      console.log(`  ${Object.values(privilege)[0]}`);
    });

    // Test required operations
    const testTable = 'nirvana_permission_test';
    
    try {
      // Test CREATE
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS ${testTable} (
          id INT AUTO_INCREMENT PRIMARY KEY,
          test_column VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      log.success('CREATE permission verified');

      // Test INSERT
      await sequelize.query(`
        INSERT INTO ${testTable} (test_column) VALUES ('permission_test')
      `);
      log.success('INSERT permission verified');

      // Test SELECT
      await sequelize.query(`SELECT * FROM ${testTable} LIMIT 1`);
      log.success('SELECT permission verified');

      // Test UPDATE
      await sequelize.query(`
        UPDATE ${testTable} SET test_column = 'updated_test' WHERE test_column = 'permission_test'
      `);
      log.success('UPDATE permission verified');

      // Test DELETE
      await sequelize.query(`DELETE FROM ${testTable} WHERE test_column = 'updated_test'`);
      log.success('DELETE permission verified');

      // Test ALTER
      await sequelize.query(`ALTER TABLE ${testTable} ADD COLUMN temp_col VARCHAR(50)`);
      await sequelize.query(`ALTER TABLE ${testTable} DROP COLUMN temp_col`);
      log.success('ALTER permission verified');

      // Test DROP
      await sequelize.query(`DROP TABLE ${testTable}`);
      log.success('DROP permission verified');

    } catch (permError) {
      log.error(`Permission test failed: ${permError.message}`);
      
      // Cleanup test table if it exists
      try {
        await sequelize.query(`DROP TABLE IF EXISTS ${testTable}`);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      return false;
    }

    await sequelize.close();
    return true;

  } catch (error) {
    log.error(`Permission check failed: ${error.message}`);
    await sequelize.close().catch(() => {});
    return false;
  }
}

/**
 * Verify database charset and collation
 */
async function verifyDatabaseCharset() {
  log.step('Verifying database charset and collation...');

  const sequelize = createProductionSequelize();

  try {
    const [charsetInfo] = await sequelize.query(`
      SELECT 
        DEFAULT_CHARACTER_SET_NAME as charset,
        DEFAULT_COLLATION_NAME as collation
      FROM information_schema.SCHEMATA 
      WHERE SCHEMA_NAME = ?
    `, {
      replacements: [process.env.DB_NAME]
    });

    if (charsetInfo.length === 0) {
      log.error('Database not found or no access to information_schema');
      return false;
    }

    const { charset, collation } = charsetInfo[0];
    
    log.info(`Database charset: ${charset}`);
    log.info(`Database collation: ${collation}`);

    if (charset !== 'utf8mb4') {
      log.warning(`Database charset is ${charset}, recommended: utf8mb4`);
    } else {
      log.success('Database charset is correct (utf8mb4)');
    }

    if (!collation.includes('utf8mb4')) {
      log.warning(`Database collation is ${collation}, recommended: utf8mb4_unicode_ci`);
    } else {
      log.success('Database collation is compatible with utf8mb4');
    }

    await sequelize.close();
    return true;

  } catch (error) {
    log.error(`Charset verification failed: ${error.message}`);
    await sequelize.close().catch(() => {});
    return false;
  }
}

/**
 * Main setup function
 */
async function setupProductionDatabase() {
  console.log(`${colors.bright}🗄️ Production Database Setup${colors.reset}`);
  console.log('='.repeat(50));

  try {
    // Step 1: Validate environment
    if (!validateEnvironment()) {
      process.exit(1);
    }

    // Step 2: Test connection
    if (!await testDatabaseConnection()) {
      process.exit(1);
    }

    // Step 3: Check permissions
    if (!await checkDatabasePermissions()) {
      log.warning('Some database permissions may be missing');
      log.warning('Ensure the database user has full privileges on the database');
    }

    // Step 4: Verify charset
    await verifyDatabaseCharset();

    console.log('');
    log.success('🎉 Production database setup completed successfully!');
    
    console.log('');
    log.step('Next steps:');
    console.log('1. Run database migrations: npm run migrate:prod');
    console.log('2. Seed initial data if needed: npm run seed:prod');
    console.log('3. Test application database operations');
    console.log('4. Set up database monitoring and backups');

  } catch (error) {
    log.error(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  setupProductionDatabase();
}

module.exports = {
  setupProductionDatabase,
  testDatabaseConnection,
  checkDatabasePermissions,
  verifyDatabaseCharset,
  createProductionSequelize
};
