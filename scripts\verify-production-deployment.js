#!/usr/bin/env node

/**
 * Production Deployment Verification
 * Comprehensive verification of all production systems after deployment
 */

require('dotenv').config();
const axios = require('axios');
const { Client, Environment } = require('square');
const fs = require('fs');

console.log('🔍 Verifying Production Deployment...\n');

// Configuration
const config = {
  baseUrl: process.env.FRONTEND_URL || 'https://shopnirvanaorganics.com',
  apiUrl: process.env.API_BASE_URL || 'https://shopnirvanaorganics.com/api',
  timeout: 15000,
  maxRetries: 3
};

// Test results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m',
    test: '\x1b[35m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

async function runTest(testName, testFunction, critical = true) {
  log(`🔍 ${testName}`, 'test');
  
  try {
    const startTime = Date.now();
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    if (result.success) {
      log(`✅ ${testName} - PASSED (${duration}ms)`, 'success');
      results.passed++;
    } else if (result.warning) {
      log(`⚠️  ${testName} - WARNING: ${result.message}`, 'warning');
      results.warnings++;
    } else {
      log(`❌ ${testName} - FAILED: ${result.message}`, 'error');
      results.failed++;
      
      if (critical) {
        log(`🚨 Critical test failed: ${testName}`, 'error');
      }
    }
    
    results.tests.push({
      name: testName,
      success: result.success,
      warning: result.warning || false,
      duration,
      message: result.message || null,
      details: result.details || null,
      critical
    });
    
  } catch (error) {
    log(`❌ ${testName} - ERROR: ${error.message}`, 'error');
    results.failed++;
    results.tests.push({
      name: testName,
      success: false,
      duration: 0,
      message: error.message,
      critical
    });
  }
}

// Test 1: Frontend Deployment
async function testFrontendDeployment() {
  try {
    const response = await axios.get(config.baseUrl, { 
      timeout: config.timeout,
      headers: { 'User-Agent': 'Deployment-Verification/1.0' }
    });
    
    if (response.status === 200) {
      const content = response.data;
      const hasReact = content.includes('react') || content.includes('React');
      const hasTitle = content.includes('<title>');
      const hasMetaDescription = content.includes('meta name="description"');
      const hasViewport = content.includes('meta name="viewport"');
      const hasCSP = response.headers['content-security-policy'];
      
      const checks = {
        statusCode: response.status === 200,
        hasTitle,
        hasMetaDescription,
        hasViewport,
        hasCSP: !!hasCSP,
        contentLength: content.length > 1000
      };
      
      const allPassed = Object.values(checks).every(Boolean);
      
      return {
        success: allPassed,
        warning: !allPassed && checks.statusCode,
        message: allPassed ? 'Frontend deployed successfully' : 'Frontend has issues',
        details: checks
      };
    }
    
    return { success: false, message: `Unexpected status: ${response.status}` };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Test 2: API Health and Functionality
async function testApiHealth() {
  try {
    const response = await axios.get(`${config.apiUrl}/health`, { timeout: config.timeout });
    
    if (response.status === 200 && response.data) {
      const health = response.data;
      const isHealthy = health.status === 'healthy';
      const hasChecks = health.checks && typeof health.checks === 'object';
      
      return {
        success: isHealthy,
        warning: !isHealthy && response.status === 200,
        message: isHealthy ? 'API is healthy' : `API status: ${health.status}`,
        details: health
      };
    }
    
    return { success: false, message: 'Invalid health response' };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Test 3: Database Connectivity
async function testDatabaseConnectivity() {
  try {
    const response = await axios.get(`${config.apiUrl}/health`, { timeout: config.timeout });
    
    if (response.data.checks && response.data.checks.database) {
      const dbCheck = response.data.checks.database;
      return {
        success: dbCheck.status === 'healthy',
        message: dbCheck.status === 'healthy' ? 'Database connected' : 'Database connection issues',
        details: dbCheck
      };
    }
    
    return { success: false, message: 'Database check not available' };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Test 4: Square Payment System
async function testSquarePaymentSystem() {
  try {
    const squareClient = new Client({
      accessToken: process.env.SQUARE_ACCESS_TOKEN,
      environment: Environment.Production
    });
    
    const locationsApi = squareClient.locationsApi;
    const response = await locationsApi.listLocations();
    
    if (response.result && response.result.locations) {
      const configuredLocation = response.result.locations.find(
        loc => loc.id === process.env.SQUARE_LOCATION_ID
      );
      
      const isProduction = process.env.SQUARE_ENVIRONMENT === 'production';
      
      return {
        success: !!configuredLocation && isProduction,
        warning: !!configuredLocation && !isProduction,
        message: configuredLocation 
          ? (isProduction ? 'Square production ready' : 'Square in sandbox mode')
          : 'Square location not found',
        details: {
          environment: process.env.SQUARE_ENVIRONMENT,
          locationFound: !!configuredLocation,
          locationCount: response.result.locations.length
        }
      };
    }
    
    return { success: false, message: 'No Square locations found' };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Test 5: SSL Certificate
async function testSSLCertificate() {
  try {
    const https = require('https');
    const url = new URL(config.baseUrl);
    
    return new Promise((resolve) => {
      const req = https.request({
        hostname: url.hostname,
        port: 443,
        path: '/',
        method: 'GET',
        rejectUnauthorized: true
      }, (res) => {
        const cert = res.connection.getPeerCertificate();
        const now = new Date();
        const validTo = new Date(cert.valid_to);
        const daysUntilExpiry = Math.floor((validTo - now) / (1000 * 60 * 60 * 24));
        
        const isValid = now < validTo;
        const isExpiringSoon = daysUntilExpiry < 30;
        
        resolve({
          success: isValid && !isExpiringSoon,
          warning: isValid && isExpiringSoon,
          message: isValid 
            ? (isExpiringSoon ? `SSL expires in ${daysUntilExpiry} days` : 'SSL certificate valid')
            : 'SSL certificate expired',
          details: {
            subject: cert.subject.CN,
            issuer: cert.issuer.CN,
            validTo: cert.valid_to,
            daysUntilExpiry
          }
        });
      });
      
      req.on('error', (error) => {
        resolve({ success: false, message: error.message });
      });
      
      req.end();
    });
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Test 6: Performance Metrics
async function testPerformanceMetrics() {
  const endpoints = [
    { url: config.baseUrl, name: 'Frontend', threshold: 3000 },
    { url: `${config.apiUrl}/health`, name: 'API Health', threshold: 2000 },
    { url: `${config.apiUrl}/products`, name: 'API Products', threshold: 5000 }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const startTime = Date.now();
      const response = await axios.get(endpoint.url, { timeout: config.timeout });
      const responseTime = Date.now() - startTime;
      
      results.push({
        name: endpoint.name,
        responseTime,
        threshold: endpoint.threshold,
        success: responseTime < endpoint.threshold,
        status: response.status
      });
    } catch (error) {
      results.push({
        name: endpoint.name,
        responseTime: config.timeout,
        threshold: endpoint.threshold,
        success: false,
        error: error.message
      });
    }
  }
  
  const allFast = results.every(r => r.success);
  const averageTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  
  return {
    success: allFast,
    warning: !allFast && results.some(r => r.success),
    message: allFast ? 'Performance acceptable' : 'Performance issues detected',
    details: { averageTime, results }
  };
}

// Test 7: Security Headers
async function testSecurityHeaders() {
  try {
    const response = await axios.get(config.baseUrl, { timeout: config.timeout });
    
    const securityHeaders = {
      'strict-transport-security': !!response.headers['strict-transport-security'],
      'x-frame-options': !!response.headers['x-frame-options'],
      'x-content-type-options': !!response.headers['x-content-type-options'],
      'content-security-policy': !!response.headers['content-security-policy'],
      'x-xss-protection': !!response.headers['x-xss-protection']
    };
    
    const headerCount = Object.values(securityHeaders).filter(Boolean).length;
    const totalHeaders = Object.keys(securityHeaders).length;
    
    return {
      success: headerCount >= 4,
      warning: headerCount >= 2 && headerCount < 4,
      message: `${headerCount}/${totalHeaders} security headers present`,
      details: securityHeaders
    };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Test 8: Admin Panel Access
async function testAdminPanelAccess() {
  try {
    const response = await axios.get(`${config.baseUrl}/admin`, { 
      timeout: config.timeout,
      validateStatus: () => true,
      maxRedirects: 0
    });
    
    // Admin should be accessible or redirect to login
    const validStatuses = [200, 302, 401, 403];
    const isAccessible = validStatuses.includes(response.status);
    
    return {
      success: isAccessible,
      message: isAccessible ? 'Admin panel accessible' : `Admin panel returned ${response.status}`,
      details: { statusCode: response.status }
    };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

// Main verification function
async function runVerification() {
  log('🚀 Starting Production Deployment Verification', 'info');
  log('================================================\n');
  
  // Run all tests
  await runTest('Frontend Deployment', testFrontendDeployment, true);
  await runTest('API Health Check', testApiHealth, true);
  await runTest('Database Connectivity', testDatabaseConnectivity, true);
  await runTest('Square Payment System', testSquarePaymentSystem, true);
  await runTest('SSL Certificate', testSSLCertificate, false);
  await runTest('Performance Metrics', testPerformanceMetrics, false);
  await runTest('Security Headers', testSecurityHeaders, false);
  await runTest('Admin Panel Access', testAdminPanelAccess, false);
  
  // Generate final report
  generateFinalReport();
}

// Generate final report
function generateFinalReport() {
  log('\n📊 Deployment Verification Summary', 'info');
  log('==================================');
  
  const total = results.passed + results.failed + results.warnings;
  const criticalTests = results.tests.filter(t => t.critical);
  const criticalFailures = criticalTests.filter(t => !t.success).length;
  
  log(`Total Tests: ${total}`);
  log(`Passed: ${results.passed}`, 'success');
  log(`Warnings: ${results.warnings}`, 'warning');
  log(`Failed: ${results.failed}`, results.failed > 0 ? 'error' : 'info');
  log(`Critical Failures: ${criticalFailures}`, criticalFailures > 0 ? 'error' : 'success');
  
  // Deployment status
  let deploymentStatus;
  if (criticalFailures > 0) {
    deploymentStatus = 'FAILED';
    log('\n🚨 DEPLOYMENT VERIFICATION FAILED', 'error');
    log('Critical issues must be resolved before going live.', 'error');
  } else if (results.failed > 0 || results.warnings > 0) {
    deploymentStatus = 'WARNING';
    log('\n⚠️  DEPLOYMENT VERIFICATION COMPLETED WITH WARNINGS', 'warning');
    log('Non-critical issues detected. Review and fix when possible.', 'warning');
  } else {
    deploymentStatus = 'SUCCESS';
    log('\n🎉 DEPLOYMENT VERIFICATION SUCCESSFUL', 'success');
    log('All tests passed! Production deployment is ready.', 'success');
  }
  
  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    status: deploymentStatus,
    summary: {
      total,
      passed: results.passed,
      warnings: results.warnings,
      failed: results.failed,
      criticalFailures
    },
    tests: results.tests,
    environment: {
      nodeEnv: process.env.NODE_ENV,
      frontendUrl: config.baseUrl,
      apiUrl: config.apiUrl
    }
  };
  
  const reportPath = 'deployment-verification-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n📄 Detailed report saved: ${reportPath}`);
  
  // Exit with appropriate code
  process.exit(criticalFailures > 0 ? 1 : 0);
}

// Run verification
runVerification().catch(error => {
  log(`❌ Verification failed: ${error.message}`, 'error');
  process.exit(1);
});
