import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Product } from '../../types';

interface ProductSchemaProps {
  product: Product;
}

const ProductSchema: React.FC<ProductSchemaProps> = ({ product }) => {
  const siteUrl = 'https://nirvanaorganics.com';
  
  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": product.images.map(img => `${siteUrl}${img.url}`),
    "sku": product.sku,
    "brand": {
      "@type": "Brand",
      "name": "Nirvana Organics"
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "Nirvana Organics"
    },
    "category": product.category?.name || "Hemp Products",
    "offers": {
      "@type": "Offer",
      "price": product.price.toString(),
      "priceCurrency": "USD",
      "availability": product.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
      "seller": {
        "@type": "Organization",
        "name": "Nirvana Organics"
      },
      "url": `${siteUrl}/product/${product.slug}`,
      "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
    },
    "aggregateRating": product.reviewCount > 0 ? {
      "@type": "AggregateRating",
      "ratingValue": product.averageRating.toString(),
      "reviewCount": product.reviewCount.toString(),
      "bestRating": "5",
      "worstRating": "1"
    } : undefined,
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Cannabinoid",
        "value": product.cannabinoid
      },
      ...(product.strain ? [{
        "@type": "PropertyValue",
        "name": "Strain Type",
        "value": product.strain
      }] : []),
      ...(product.potency ? [{
        "@type": "PropertyValue",
        "name": "Potency",
        "value": product.potency
      }] : []),
      {
        "@type": "PropertyValue",
        "name": "Lab Tested",
        "value": "Yes"
      },
      {
        "@type": "PropertyValue",
        "name": "Hemp Derived",
        "value": "Yes"
      }
    ].filter(Boolean)
  };

  // Remove undefined properties
  const cleanSchema = JSON.parse(JSON.stringify(productSchema));

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(cleanSchema)}
      </script>
    </Helmet>
  );
};

export default ProductSchema;
