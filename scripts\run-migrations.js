require('dotenv').config({ path: '.env.production' });
const { sequelize } = require('../server/models');
const fs = require('fs');
const path = require('path');

async function runMigrations() {
  try {
    console.log('🔄 Running Production Database Migrations...');
    console.log('=============================================\n');
    
    // Test database connection
    console.log('1️⃣ Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    console.log(`   Host: ${process.env.DB_HOST}`);
    console.log(`   Database: ${process.env.DB_NAME}`);
    console.log(`   User: ${process.env.DB_USER}`);
    console.log(`   Environment: ${process.env.NODE_ENV}`);
    
    // Sync database models
    console.log('\n2️⃣ Syncing database models...');
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ Database models synced successfully');
    
    // Check if tables exist
    console.log('\n3️⃣ Verifying database tables...');
    const [results] = await sequelize.query("SHOW TABLES");
    console.log(`✅ Found ${results.length} tables:`);
    results.forEach(table => {
      console.log(`   - ${Object.values(table)[0]}`);
    });
    
    // Run custom migrations if they exist
    const migrationsDir = path.join(__dirname, '../server/migrations');
    if (fs.existsSync(migrationsDir)) {
      console.log('\n4️⃣ Running custom migrations...');
      const migrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.js'))
        .sort();
      
      for (const file of migrationFiles) {
        console.log(`   Running migration: ${file}`);
        const migration = require(path.join(migrationsDir, file));
        if (migration.up) {
          await migration.up(sequelize.getQueryInterface(), sequelize.constructor);
          console.log(`   ✅ Migration ${file} completed`);
        }
      }
    } else {
      console.log('\n4️⃣ No custom migrations found, skipping...');
    }
    
    console.log('\n🎉 Database migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migrations if called directly
if (require.main === module) {
  runMigrations();
}

module.exports = runMigrations;
