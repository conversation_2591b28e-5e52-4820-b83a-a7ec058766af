import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import LazyImage from '../common/LazyImage';
// Navigation arrows removed for auto-sliding carousel

interface HeroSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  ctaText: string;
  ctaLink: string;
  backgroundColor: string;
}

const heroSlides: HeroSlide[] = [
  {
    id: 1,
    title: 'Premium Organic Products',
    subtitle: 'Nature\'s Best for Your Wellness',
    description: 'Discover our carefully curated selection of organic CBD products, crafted with the highest quality ingredients for your health and well-being.',
    image: '/images/hero-banner-1.jpg',
    ctaText: 'Shop Now',
    ctaLink: '/shop',
    backgroundColor: 'from-green-600 to-green-800'
  },
  {
    id: 2,
    title: 'Lab-Tested Quality',
    subtitle: 'Purity You Can Trust',
    description: 'Every product is rigorously tested by third-party laboratories to ensure the highest standards of quality, potency, and safety.',
    image: '/images/hero-banner-2.jpg',
    ctaText: 'Learn More',
    ctaLink: '/about',
    backgroundColor: 'from-blue-600 to-blue-800'
  },
  {
    id: 3,
    title: 'Fast & Free Shipping',
    subtitle: 'Delivered to Your Door',
    description: 'Enjoy free shipping on orders over $100 and fast, secure delivery right to your doorstep.',
    image: '/images/hero-banner-3.jpg',
    ctaText: 'Start Shopping',
    ctaLink: '/shop',
    backgroundColor: 'from-purple-600 to-purple-800'
  }
];

const HeroSection: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize component
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // Auto-advance slides with pause support
  useEffect(() => {
    if (!isAutoPlaying || !isInitialized || isPaused) return;

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 4000); // Reduced to 4 seconds for better UX

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAutoPlaying, isInitialized, isPaused]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const goToSlide = (index: number) => {
    if (index === currentSlide) return;

    setCurrentSlide(index);
    setImageLoaded(false);
    setIsAutoPlaying(false);

    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  // Navigation functions removed for auto-sliding carousel

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const currentSlideData = heroSlides[currentSlide];

  // Remove initialization check to prevent loading delays
  // if (!isInitialized) {
  //   return (
  //     <section className="relative h-[600px] overflow-hidden bg-gray-200 animate-pulse">
  //       <div className="absolute inset-0 bg-gradient-to-r from-green-600 to-green-800 opacity-75"></div>
  //     </section>
  //   );
  // }

  return (
    <section
      className="relative h-[400px] sm:h-[500px] lg:h-[600px] overflow-hidden mobile-stable"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
      onTouchStart={() => setIsPaused(true)}
      onTouchEnd={() => setIsPaused(false)}
    >
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <LazyImage
          src={currentSlideData.image}
          alt={currentSlideData.title}
          className="w-full h-full object-cover object-center gpu-accelerated"
          onLoad={handleImageLoad}
          placeholder="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDYwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyMDAiIGhlaWdodD0iNjAwIiBmaWxsPSIjMTZhMzRhIi8+CjxjaXJjbGUgY3g9IjYwMCIgY3k9IjMwMCIgcj0iNTAiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMiIvPgo8L3N2Zz4K"
        />
        <div className={`absolute inset-0 bg-gradient-to-r ${currentSlideData.backgroundColor} opacity-75 transition-all duration-1000 ease-in-out`}></div>
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl text-white transition-all duration-1000 ease-in-out">
            {/* Subtitle */}
            <p className="text-sm sm:text-lg md:text-xl font-medium mb-2 opacity-90 animate-fade-in-up transition-all duration-700">
              {currentSlideData.subtitle}
            </p>

            {/* Title */}
            <h1 className="text-2xl sm:text-4xl md:text-6xl font-bold mb-4 sm:mb-6 leading-tight animate-fade-in-up animation-delay-200 transition-all duration-800">
              {currentSlideData.title}
            </h1>

            {/* Description */}
            <p className="text-sm sm:text-lg md:text-xl mb-6 sm:mb-8 opacity-90 leading-relaxed animate-fade-in-up animation-delay-400 transition-all duration-900">
              {currentSlideData.description}
            </p>
            
            {/* CTA Button */}
            <div className="animate-fade-in-up animation-delay-600">
              <Link
                to={currentSlideData.ctaLink}
                className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 bg-white text-gray-900 font-semibold rounded-lg hover:bg-gray-100 active:bg-gray-200 transition-all duration-300 transform hover:scale-105 shadow-lg touch-target"
                style={{ touchAction: 'manipulation' }}
              >
                {currentSlideData.ctaText}
                <svg
                  className="ml-2 h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation arrows removed for cleaner auto-sliding experience */}

      {/* Subtle Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-500 ease-in-out ${
              index === currentSlide
                ? 'bg-white scale-150 shadow-lg'
                : 'bg-white bg-opacity-40 hover:bg-opacity-70 hover:scale-125'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Progress Bar */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-white bg-opacity-20">
        <div
          className="h-full bg-white transition-all duration-300 ease-linear"
          style={{
            width: `${((currentSlide + 1) / heroSlides.length) * 100}%`
          }}
        />
      </div>
    </section>
  );
};

export default HeroSection;
