#!/bin/bash

# ============================================================================
# Production Backup Setup Script
# Sets up automated backups for Nirvana Organics backend
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${CYAN}🔧 $1${NC}"; }

# Configuration
PROJECT_NAME="nirvana-organics-backend"
PROJECT_PATH="/var/www/$PROJECT_NAME"
BACKUP_PATH="/var/backups/$PROJECT_NAME"
REMOTE_BACKUP_PATH="/opt/remote-backups"

# Database configuration
DB_NAME="u106832845_nirvana"
DB_USER="u106832845_root"

echo "============================================================================"
echo "💾 PRODUCTION BACKUP SETUP"
echo "============================================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: Create backup directories
log_step "Step 1: Creating backup directories"

mkdir -p "$BACKUP_PATH"/{database,files,logs,config}
mkdir -p "$REMOTE_BACKUP_PATH"

log_success "Backup directories created"

# Step 2: Create database backup script
log_step "Step 2: Creating database backup script"

cat > "$BACKUP_PATH/scripts/backup-database.sh" << 'EOF'
#!/bin/bash

# Database backup script
BACKUP_DIR="/var/backups/nirvana-organics-backend/database"
LOG_FILE="/var/backups/nirvana-organics-backend/logs/database-backup.log"
RETENTION_DAYS=30

# Database configuration
DB_NAME="u106832845_nirvana"
DB_USER="u106832845_root"

# Read database password from environment
if [ -f "/var/www/nirvana-organics-backend/current/.env" ]; then
    DB_PASSWORD=$(grep "^DB_PASSWORD=" /var/www/nirvana-organics-backend/current/.env | cut -d'=' -f2)
else
    echo "$(date): ERROR - Environment file not found" >> "$LOG_FILE"
    exit 1
fi

# Create backup filename with timestamp
BACKUP_FILE="$BACKUP_DIR/nirvana_db_$(date +%Y%m%d_%H%M%S).sql"
COMPRESSED_FILE="$BACKUP_FILE.gz"

# Function to log with timestamp
log_backup() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_backup "Starting database backup"

# Create database backup
if mysqldump -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_FILE" 2>/dev/null; then
    # Compress backup
    gzip "$BACKUP_FILE"
    
    # Get file size
    BACKUP_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
    
    log_backup "Database backup completed successfully: $(basename "$COMPRESSED_FILE") ($BACKUP_SIZE)"
    
    # Remove old backups
    find "$BACKUP_DIR" -name "nirvana_db_*.sql.gz" -mtime +$RETENTION_DAYS -delete
    log_backup "Old backups cleaned up (retention: $RETENTION_DAYS days)"
    
    # Verify backup integrity
    if gunzip -t "$COMPRESSED_FILE" 2>/dev/null; then
        log_backup "Backup integrity verified"
    else
        log_backup "WARNING: Backup integrity check failed"
    fi
    
else
    log_backup "ERROR: Database backup failed"
    exit 1
fi

log_backup "Database backup process completed"
EOF

mkdir -p "$BACKUP_PATH/scripts"
chmod +x "$BACKUP_PATH/scripts/backup-database.sh"

log_success "Database backup script created"

# Step 3: Create files backup script
log_step "Step 3: Creating files backup script"

cat > "$BACKUP_PATH/scripts/backup-files.sh" << 'EOF'
#!/bin/bash

# Files backup script
BACKUP_DIR="/var/backups/nirvana-organics-backend/files"
LOG_FILE="/var/backups/nirvana-organics-backend/logs/files-backup.log"
PROJECT_PATH="/var/www/nirvana-organics-backend"
RETENTION_DAYS=30

# Function to log with timestamp
log_backup() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_backup "Starting files backup"

# Create backup filename with timestamp
BACKUP_FILE="$BACKUP_DIR/nirvana_files_$(date +%Y%m%d_%H%M%S).tar.gz"

# Create backup excluding unnecessary files
if tar -czf "$BACKUP_FILE" \
    --exclude="node_modules" \
    --exclude="*.log" \
    --exclude="logs" \
    --exclude=".git" \
    --exclude="tmp" \
    --exclude="temp" \
    -C "$PROJECT_PATH" \
    current/server \
    current/scripts \
    current/package.json \
    current/ecosystem.config.js \
    current/.env \
    shared/uploads \
    shared/config 2>/dev/null; then
    
    # Get file size
    BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    
    log_backup "Files backup completed successfully: $(basename "$BACKUP_FILE") ($BACKUP_SIZE)"
    
    # Remove old backups
    find "$BACKUP_DIR" -name "nirvana_files_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    log_backup "Old file backups cleaned up (retention: $RETENTION_DAYS days)"
    
    # Verify backup integrity
    if tar -tzf "$BACKUP_FILE" >/dev/null 2>&1; then
        log_backup "Backup integrity verified"
    else
        log_backup "WARNING: Backup integrity check failed"
    fi
    
else
    log_backup "ERROR: Files backup failed"
    exit 1
fi

log_backup "Files backup process completed"
EOF

chmod +x "$BACKUP_PATH/scripts/backup-files.sh"

log_success "Files backup script created"

# Step 4: Create configuration backup script
log_step "Step 4: Creating configuration backup script"

cat > "$BACKUP_PATH/scripts/backup-config.sh" << 'EOF'
#!/bin/bash

# Configuration backup script
BACKUP_DIR="/var/backups/nirvana-organics-backend/config"
LOG_FILE="/var/backups/nirvana-organics-backend/logs/config-backup.log"
RETENTION_DAYS=90

# Function to log with timestamp
log_backup() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_backup "Starting configuration backup"

# Create backup filename with timestamp
BACKUP_FILE="$BACKUP_DIR/nirvana_config_$(date +%Y%m%d_%H%M%S).tar.gz"

# Backup configuration files
if tar -czf "$BACKUP_FILE" \
    /etc/nginx/sites-available/nirvana-organics-backend \
    /etc/nginx/sites-enabled/nirvana-organics-backend \
    /var/www/nirvana-organics-backend/current/.env \
    /var/www/nirvana-organics-backend/current/ecosystem.config.js \
    /etc/letsencrypt/live/shopnirvanaorganics.com/ \
    /etc/crontab \
    /opt/monitoring/ 2>/dev/null; then
    
    # Get file size
    BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    
    log_backup "Configuration backup completed successfully: $(basename "$BACKUP_FILE") ($BACKUP_SIZE)"
    
    # Remove old backups
    find "$BACKUP_DIR" -name "nirvana_config_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    log_backup "Old configuration backups cleaned up (retention: $RETENTION_DAYS days)"
    
else
    log_backup "ERROR: Configuration backup failed"
    exit 1
fi

log_backup "Configuration backup process completed"
EOF

chmod +x "$BACKUP_PATH/scripts/backup-config.sh"

log_success "Configuration backup script created"

# Step 5: Create master backup script
log_step "Step 5: Creating master backup script"

cat > "$BACKUP_PATH/scripts/backup-all.sh" << 'EOF'
#!/bin/bash

# Master backup script - runs all backup procedures
LOG_FILE="/var/backups/nirvana-organics-backend/logs/master-backup.log"

# Function to log with timestamp
log_backup() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_backup "Starting master backup process"

# Run database backup
log_backup "Running database backup..."
if /var/backups/nirvana-organics-backend/scripts/backup-database.sh; then
    log_backup "Database backup completed successfully"
else
    log_backup "ERROR: Database backup failed"
fi

# Run files backup
log_backup "Running files backup..."
if /var/backups/nirvana-organics-backend/scripts/backup-files.sh; then
    log_backup "Files backup completed successfully"
else
    log_backup "ERROR: Files backup failed"
fi

# Run configuration backup
log_backup "Running configuration backup..."
if /var/backups/nirvana-organics-backend/scripts/backup-config.sh; then
    log_backup "Configuration backup completed successfully"
else
    log_backup "ERROR: Configuration backup failed"
fi

# Generate backup report
TOTAL_BACKUPS=$(find /var/backups/nirvana-organics-backend -name "*.tar.gz" -o -name "*.sql.gz" | wc -l)
TOTAL_SIZE=$(du -sh /var/backups/nirvana-organics-backend | cut -f1)

log_backup "Master backup process completed"
log_backup "Total backups: $TOTAL_BACKUPS files"
log_backup "Total backup size: $TOTAL_SIZE"

# Send backup report (if email is configured)
# echo "Backup completed at $(date). Total: $TOTAL_BACKUPS files ($TOTAL_SIZE)" | mail -s "Nirvana Organics Backup Report" <EMAIL>
EOF

chmod +x "$BACKUP_PATH/scripts/backup-all.sh"

log_success "Master backup script created"

# Step 6: Create restore script
log_step "Step 6: Creating restore script"

cat > "$BACKUP_PATH/scripts/restore-database.sh" << 'EOF'
#!/bin/bash

# Database restore script
BACKUP_DIR="/var/backups/nirvana-organics-backend/database"
LOG_FILE="/var/backups/nirvana-organics-backend/logs/restore.log"

# Database configuration
DB_NAME="u106832845_nirvana"
DB_USER="u106832845_root"

# Function to log with timestamp
log_restore() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check if backup file is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <backup_file.sql.gz>"
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/*.sql.gz 2>/dev/null || echo "No backups found"
    exit 1
fi

BACKUP_FILE="$1"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Error: Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Read database password from environment
if [ -f "/var/www/nirvana-organics-backend/current/.env" ]; then
    DB_PASSWORD=$(grep "^DB_PASSWORD=" /var/www/nirvana-organics-backend/current/.env | cut -d'=' -f2)
else
    echo "ERROR - Environment file not found"
    exit 1
fi

log_restore "Starting database restore from: $BACKUP_FILE"

# Confirm restore operation
read -p "This will overwrite the current database. Are you sure? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_restore "Database restore cancelled by user"
    exit 1
fi

# Stop application during restore
log_restore "Stopping application..."
sudo -u nirvana pm2 stop all

# Restore database
log_restore "Restoring database..."
if gunzip -c "$BACKUP_FILE" | mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"; then
    log_restore "Database restore completed successfully"
    
    # Start application
    log_restore "Starting application..."
    sudo -u nirvana pm2 start all
    
    log_restore "Database restore process completed successfully"
else
    log_restore "ERROR: Database restore failed"
    
    # Try to start application anyway
    sudo -u nirvana pm2 start all
    exit 1
fi
EOF

chmod +x "$BACKUP_PATH/scripts/restore-database.sh"

log_success "Restore script created"

# Step 7: Setup backup cron jobs
log_step "Step 7: Setting up backup cron jobs"

# Create backup cron jobs
cat > /tmp/backup-cron << EOF
# Nirvana Organics Backend Backup Schedule

# Database backup every 6 hours
0 */6 * * * /var/backups/nirvana-organics-backend/scripts/backup-database.sh

# Files backup daily at 2 AM
0 2 * * * /var/backups/nirvana-organics-backend/scripts/backup-files.sh

# Configuration backup weekly on Sunday at 3 AM
0 3 * * 0 /var/backups/nirvana-organics-backend/scripts/backup-config.sh

# Master backup daily at 1 AM
0 1 * * * /var/backups/nirvana-organics-backend/scripts/backup-all.sh

# Cleanup old logs monthly
0 4 1 * * find /var/backups/nirvana-organics-backend/logs -name "*.log" -mtime +90 -delete
EOF

# Add to existing crontab
(crontab -l 2>/dev/null; cat /tmp/backup-cron) | crontab -
rm /tmp/backup-cron

log_success "Backup cron jobs configured"

# Step 8: Create backup monitoring script
log_step "Step 8: Creating backup monitoring script"

cat > "$BACKUP_PATH/scripts/backup-status.sh" << 'EOF'
#!/bin/bash

# Backup status monitoring script
echo "============================================================================"
echo "💾 BACKUP STATUS REPORT"
echo "============================================================================"
echo "Generated: $(date)"
echo ""

# Database backups
echo "📊 DATABASE BACKUPS"
echo "------------------"
DB_BACKUP_COUNT=$(find /var/backups/nirvana-organics-backend/database -name "*.sql.gz" | wc -l)
if [ "$DB_BACKUP_COUNT" -gt 0 ]; then
    echo "Total database backups: $DB_BACKUP_COUNT"
    echo "Latest backup: $(ls -t /var/backups/nirvana-organics-backend/database/*.sql.gz | head -1 | xargs basename)"
    echo "Latest backup size: $(ls -lh /var/backups/nirvana-organics-backend/database/*.sql.gz | head -1 | awk '{print $5}')"
    echo "Latest backup date: $(ls -lt /var/backups/nirvana-organics-backend/database/*.sql.gz | head -1 | awk '{print $6, $7, $8}')"
else
    echo "❌ No database backups found"
fi

echo ""

# Files backups
echo "📁 FILES BACKUPS"
echo "---------------"
FILES_BACKUP_COUNT=$(find /var/backups/nirvana-organics-backend/files -name "*.tar.gz" | wc -l)
if [ "$FILES_BACKUP_COUNT" -gt 0 ]; then
    echo "Total files backups: $FILES_BACKUP_COUNT"
    echo "Latest backup: $(ls -t /var/backups/nirvana-organics-backend/files/*.tar.gz | head -1 | xargs basename)"
    echo "Latest backup size: $(ls -lh /var/backups/nirvana-organics-backend/files/*.tar.gz | head -1 | awk '{print $5}')"
    echo "Latest backup date: $(ls -lt /var/backups/nirvana-organics-backend/files/*.tar.gz | head -1 | awk '{print $6, $7, $8}')"
else
    echo "❌ No files backups found"
fi

echo ""

# Configuration backups
echo "⚙️  CONFIGURATION BACKUPS"
echo "------------------------"
CONFIG_BACKUP_COUNT=$(find /var/backups/nirvana-organics-backend/config -name "*.tar.gz" | wc -l)
if [ "$CONFIG_BACKUP_COUNT" -gt 0 ]; then
    echo "Total config backups: $CONFIG_BACKUP_COUNT"
    echo "Latest backup: $(ls -t /var/backups/nirvana-organics-backend/config/*.tar.gz | head -1 | xargs basename)"
    echo "Latest backup size: $(ls -lh /var/backups/nirvana-organics-backend/config/*.tar.gz | head -1 | awk '{print $5}')"
    echo "Latest backup date: $(ls -lt /var/backups/nirvana-organics-backend/config/*.tar.gz | head -1 | awk '{print $6, $7, $8}')"
else
    echo "❌ No configuration backups found"
fi

echo ""

# Total backup size
echo "💽 STORAGE USAGE"
echo "---------------"
TOTAL_SIZE=$(du -sh /var/backups/nirvana-organics-backend | cut -f1)
echo "Total backup storage used: $TOTAL_SIZE"

# Disk space
AVAILABLE_SPACE=$(df -h /var/backups | awk 'NR==2 {print $4}')
echo "Available disk space: $AVAILABLE_SPACE"

echo ""

# Recent backup logs
echo "📋 RECENT BACKUP ACTIVITY"
echo "------------------------"
if [ -f "/var/backups/nirvana-organics-backend/logs/master-backup.log" ]; then
    echo "Last 5 backup log entries:"
    tail -5 /var/backups/nirvana-organics-backend/logs/master-backup.log
else
    echo "No backup logs found"
fi

echo ""
echo "============================================================================"
echo "Commands:"
echo "• Manual backup: /var/backups/nirvana-organics-backend/scripts/backup-all.sh"
echo "• Restore database: /var/backups/nirvana-organics-backend/scripts/restore-database.sh <backup_file>"
echo "• View logs: tail -f /var/backups/nirvana-organics-backend/logs/master-backup.log"
echo "============================================================================"
EOF

chmod +x "$BACKUP_PATH/scripts/backup-status.sh"

log_success "Backup monitoring script created"

# Step 9: Set permissions
log_step "Step 9: Setting permissions"

chown -R root:root "$BACKUP_PATH"
chmod -R 755 "$BACKUP_PATH/scripts"
chmod -R 644 "$BACKUP_PATH/logs" 2>/dev/null || true

# Create log directory
mkdir -p "$BACKUP_PATH/logs"

log_success "Permissions set"

# Step 10: Test backup system
log_step "Step 10: Testing backup system"

# Run initial backup test
log_info "Running initial backup test..."

if "$BACKUP_PATH/scripts/backup-database.sh"; then
    log_success "Database backup test passed"
else
    log_warning "Database backup test failed - check configuration"
fi

if "$BACKUP_PATH/scripts/backup-files.sh"; then
    log_success "Files backup test passed"
else
    log_warning "Files backup test failed - check configuration"
fi

# Final status
echo ""
echo "============================================================================"
log_success "🎉 BACKUP SETUP COMPLETED!"
echo "============================================================================"

log_info "Backup Components:"
echo "• Database backups: Every 6 hours"
echo "• Files backups: Daily at 2 AM"
echo "• Configuration backups: Weekly on Sunday"
echo "• Master backup: Daily at 1 AM"

echo ""
log_info "Backup Locations:"
echo "• Database: $BACKUP_PATH/database/"
echo "• Files: $BACKUP_PATH/files/"
echo "• Configuration: $BACKUP_PATH/config/"
echo "• Logs: $BACKUP_PATH/logs/"

echo ""
log_info "Useful Commands:"
echo "• Backup status: $BACKUP_PATH/scripts/backup-status.sh"
echo "• Manual backup: $BACKUP_PATH/scripts/backup-all.sh"
echo "• Restore database: $BACKUP_PATH/scripts/restore-database.sh <backup_file>"
echo "• View cron jobs: crontab -l"

echo ""
log_warning "Next Steps:"
echo "1. Test restore procedures"
echo "2. Configure remote backup storage"
echo "3. Set up backup monitoring alerts"
echo "4. Document disaster recovery procedures"

log_success "Backup setup completed successfully! 💾"
