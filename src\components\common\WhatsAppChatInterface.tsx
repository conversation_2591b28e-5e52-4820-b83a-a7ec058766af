import React, { useState, useEffect, useRef } from 'react';
import { XMarkIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon } from '@heroicons/react/24/outline';

interface Message {
  id: string;
  text: string;
  timestamp: Date;
  isFromBusiness: boolean;
  status: 'sending' | 'sent' | 'delivered' | 'read';
}

interface WhatsAppChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  businessName?: string;
  businessPhone?: string;
}

const WhatsAppChatInterface: React.FC<WhatsAppChatInterfaceProps> = ({
  isOpen,
  onClose,
  businessName = 'Nirvana Organics',
  businessPhone = '+1234567890'
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isBusinessOnline, setIsBusinessOnline] = useState(true);
  const [conversationContext, setConversationContext] = useState<string[]>([]);
  const [userPreferences, setUserPreferences] = useState<{[key: string]: any}>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize with welcome messages
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessages: Message[] = [
        {
          id: '1',
          text: `🌿 Welcome to ${businessName}! 👋\n\nI'm your AI assistant, ready to help you 24/7!`,
          timestamp: new Date(Date.now() - 90000),
          isFromBusiness: true,
          status: 'read'
        },
        {
          id: '2',
          text: `🤖 **I can instantly help you with:**\n• 🛍️ Product recommendations\n• 💰 Current prices & deals\n• 📦 Order tracking\n• 📊 Stock availability\n• 🚚 Shipping info\n• 📋 Store policies\n\n💬 Need human support? Just ask!`,
          timestamp: new Date(Date.now() - 60000),
          isFromBusiness: true,
          status: 'read'
        },
        {
          id: '3',
          text: `🎯 **Popular right now:**\n• 🌿 CBD Sleep Gummies - $45\n• 🟡 Delta-8 Vape Starter Kit - $35\n• 🔴 Delta-9 Chocolate Bars - $25\n• 💎 Premium THC-A Flowers - $65\n\n✨ Use code WELCOME15 for 15% off your first order!\n\nWhat can I help you find today? 😊`,
          timestamp: new Date(Date.now() - 30000),
          isFromBusiness: true,
          status: 'read'
        }
      ];
      setMessages(welcomeMessages);
    }
  }, [isOpen, businessName, messages.length]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const quickReplies = [
    '🌿 Browse Products',
    '💰 Check Prices',
    '📦 Order Status',
    '🚚 Shipping Info',
    '📊 Check Stock',
    '📋 Store Policies',
    '🔍 Search Products',
    '💬 Human Support'
  ];

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const handleSendMessage = (messageText?: string) => {
    const text = messageText || newMessage.trim();
    if (!text) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text,
      timestamp: new Date(),
      isFromBusiness: false,
      status: 'sending'
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');

    // Update conversation context
    setConversationContext(prev => [...prev.slice(-4), text]); // Keep last 5 messages for context

    // Simulate message status updates
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: 'sent' as const }
            : msg
        )
      );
    }, 500);

    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === userMessage.id 
            ? { ...msg, status: 'delivered' as const }
            : msg
        )
      );
    }, 1000);

    // Simulate business typing and response
    setTimeout(() => {
      setIsTyping(true);
    }, 1500);

    setTimeout(() => {
      setIsTyping(false);
      const businessResponse = generateBusinessResponse(text, conversationContext);
      const responseMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: businessResponse,
        timestamp: new Date(),
        isFromBusiness: true,
        status: 'read'
      };
      setMessages(prev => [...prev, responseMessage]);

      // Mark user message as read
      setMessages(prev =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: 'read' as const }
            : msg
        )
      );

      // Add follow-up suggestions after certain responses
      if (text.toLowerCase().includes('product') || text.toLowerCase().includes('browse')) {
        setTimeout(() => {
          const suggestionMessage: Message = {
            id: (Date.now() + 2).toString(),
            text: "💡 **Quick tip:** You can also ask me:\n• \"What's your best CBD for sleep?\"\n• \"Show me Delta-8 under $50\"\n• \"What's the difference between Delta-8 and Delta-9?\"\n\nI'm here to help you find exactly what you need! 🎯",
            timestamp: new Date(),
            isFromBusiness: true,
            status: 'read'
          };
          setMessages(prev => [...prev, suggestionMessage]);
        }, 2000);
      }
    }, 3000);
  };

  // Natural Language Processing Helper
  const analyzeIntent = (message: string, context: string[]): string => {
    const lowerMessage = message.toLowerCase();

    // Intent patterns
    const intents = {
      product_inquiry: ['product', 'browse', 'show me', 'what do you have', 'categories', 'selection'],
      pricing: ['price', 'cost', 'how much', 'expensive', 'cheap', 'deal', 'sale', 'discount'],
      order_status: ['order', 'track', 'status', 'shipped', 'delivery', 'when will'],
      stock_check: ['stock', 'available', 'in stock', 'out of stock', 'inventory'],
      shipping: ['shipping', 'delivery', 'ship', 'arrive', 'fast', 'express'],
      policies: ['return', 'policy', 'warranty', 'guarantee', 'refund'],
      search: ['find', 'search', 'looking for', 'need', 'want'],
      support: ['help', 'support', 'human', 'agent', 'talk to someone']
    };

    for (const [intent, keywords] of Object.entries(intents)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        return intent;
      }
    }

    return 'general';
  };

  // Intelligent automated response system with context awareness
  const generateBusinessResponse = (userMessage: string, context: string[] = []): string => {
    const lowerMessage = userMessage.toLowerCase();
    const contextString = context.join(' ').toLowerCase();
    const intent = analyzeIntent(userMessage, context);

    // Context-aware responses
    if (contextString.includes('price') && (lowerMessage.includes('yes') || lowerMessage.includes('sure'))) {
      return `💰 **Here are our current prices:**\n\n🌿 **CBD Products:**\n• Gummies (30ct): $35-$65\n• Tinctures (30ml): $45-$85\n• Topicals: $25-$55\n\n🟡 **Delta-8:**\n• Vapes: $30-$50\n• Gummies: $35-$60\n• Flowers: $40-$80\n\n🔴 **Delta-9:**\n• Edibles: $35-$70\n• Beverages: $15-$25\n• Chocolates: $45-$85\n\n💎 **THC-A:**\n• Premium Flowers: $45-$120\n• Concentrates: $60-$150\n\nWant details on any specific product? 🛍️`;
    }

    // Product Information & Categories
    if (intent === 'product_inquiry' || lowerMessage.includes('🌿')) {
      return `🌿 **Our Product Categories:**\n\n🟢 **CBD Products** - Wellness & relaxation\n• Tinctures, gummies, topicals\n• Starting from $25\n• Shop: /shop?category=cbd\n\n🟡 **Delta-8** - Mild psychoactive effects\n• Vapes, edibles, flowers\n• Starting from $30\n• Shop: /shop?category=delta-8\n\n🔴 **Delta-9** - Traditional THC\n• Gummies, chocolates, beverages\n• Starting from $35\n• Shop: /shop?category=delta-9\n\n💎 **THC-A** - Premium concentrates\n• Flowers, diamonds, live resin\n• Starting from $45\n• Shop: /shop?category=thc-a\n\nWhich category interests you most? 🤔`;
    }

    // Pricing Information
    else if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('💰') || lowerMessage.includes('how much')) {
      return `💰 **Current Pricing & Deals:**\n\n🌿 **CBD Products:** $25-$120\n🟡 **Delta-8:** $30-$85\n🔴 **Delta-9:** $35-$95\n💎 **THC-A:** $45-$150\n\n🎉 **Active Promotions:**\n• 🆓 FREE shipping over $75\n• 💸 15% off first order (code: WELCOME15)\n• 🛒 Buy 2 get 1 50% off on select items\n• 💎 VIP members get 20% off everything\n\n💳 We accept all major credit cards and crypto!\n\nWant to see specific product prices? Just ask! 🛍️`;
    }

    // Order Status
    else if (lowerMessage.includes('order status') || lowerMessage.includes('track') || lowerMessage.includes('📦') || lowerMessage.includes('my order')) {
      return `📦 **Order Status Check:**\n\nTo check your order status, I'll need:\n• 📧 Your email address, OR\n• 🔢 Your order number\n\n**Order Statuses:**\n• ⏳ Processing (0-24 hours)\n• 📋 Preparing (1-2 days)\n• 🚚 Shipped (tracking provided)\n• 📬 Delivered\n\n**Quick Links:**\n• 👤 Login to account: /account\n• 📞 Call us: (*************\n• 📧 Email: <EMAIL>\n\nPlease share your order details and I'll help you track it! 🔍`;
    }

    // Stock/Inventory Check
    else if (lowerMessage.includes('stock') || lowerMessage.includes('available') || lowerMessage.includes('📊') || lowerMessage.includes('in stock')) {
      return `📊 **Stock Availability:**\n\n✅ **Currently In Stock:**\n• 🌿 Most CBD products\n• 🟡 Delta-8 vapes & gummies\n• 🔴 Delta-9 edibles\n• 💎 THC-A flowers\n\n⚠️ **Limited Stock:**\n• Premium live resin carts\n• Specialty chocolate bars\n• Limited edition flavors\n\n❌ **Out of Stock:**\n• Some seasonal items\n• Bulk wholesale options\n\n🔍 **Check Specific Product:**\nTell me the product name and I'll check real-time availability!\n\n📱 **Get Notified:** Sign up for restock alerts on product pages.`;
    }

    return generateAdvancedResponse(lowerMessage);
  };

  // Advanced response handler for complex queries
  const generateAdvancedResponse = (lowerMessage: string): string => {
    // Shipping Information
    if (lowerMessage.includes('shipping') || lowerMessage.includes('delivery') || lowerMessage.includes('🚚')) {
      return `🚚 **Shipping & Delivery:**\n\n📦 **Shipping Options:**\n• 🆓 FREE Standard (3-5 days) - Orders $75+\n• 📦 Standard ($8.99) - 3-5 business days\n• ⚡ Express ($19.99) - 1-2 business days\n• 🏃 Same Day ($39.99) - Select cities only\n\n🌍 **Coverage:**\n• 🇺🇸 All 50 states (where legal)\n• 📍 No PO Boxes for THC products\n• 🏠 Signature required for orders $200+\n\n📱 **Tracking:**\n• 📧 Email notifications\n• 📱 SMS updates available\n• 🔍 Real-time tracking link\n\n📫 **Discreet Packaging:** All orders shipped in plain, unmarked boxes.`;
    }

    // Store Policies
    else if (lowerMessage.includes('policy') || lowerMessage.includes('return') || lowerMessage.includes('warranty') || lowerMessage.includes('📋')) {
      return `📋 **Store Policies:**\n\n🔄 **Return Policy:**\n• ✅ 30-day return window\n• 📦 Unopened products only\n• 💰 Full refund or store credit\n• 🚚 Free return shipping on defects\n\n🛡️ **Quality Guarantee:**\n• 🧪 Lab-tested products\n• 📜 COA (Certificate of Analysis) available\n• 💯 100% satisfaction guarantee\n• 🔄 Replace defective items free\n\n🔒 **Privacy Policy:**\n• 🛡️ Your data is secure\n• 📧 No spam emails\n• 🤐 Discreet billing & shipping\n\n📞 **Need Help?** Contact us anytime!\n• 💬 Live chat: Available now\n• 📧 Email: <EMAIL>`;
    }

    // Search Assistance
    else if (lowerMessage.includes('search') || lowerMessage.includes('find') || lowerMessage.includes('🔍') || lowerMessage.includes('looking for')) {
      return `🔍 **Product Search Assistant:**\n\n**Tell me what you're looking for:**\n• 💪 Pain relief → CBD topicals\n• 😴 Better sleep → CBN gummies\n• 🎉 Party vibes → Delta-9 edibles\n• 🧘 Relaxation → Delta-8 vapes\n• 💎 Premium experience → THC-A flowers\n\n**Search by:**\n• 🏷️ Product type (gummies, vapes, etc.)\n• 💰 Price range ($25-$150)\n• 💪 Potency (low, medium, high)\n• 🌿 Cannabinoid (CBD, Delta-8, etc.)\n• ⭐ Customer ratings\n\n**Quick Links:**\n• 🔍 Advanced search: /search\n• 🏆 Best sellers: /bestsellers\n• 🆕 New arrivals: /new\n\nWhat specific product are you searching for? 🤔`;
    }

    // Human Support Escalation
    else if (lowerMessage.includes('human') || lowerMessage.includes('agent') || lowerMessage.includes('support') || lowerMessage.includes('💬')) {
      return `💬 **Connect with Human Support:**\n\n👥 **Our team is here to help!**\n\n📞 **Call Us:**\n• Phone: (*************\n• Hours: Mon-Fri 9AM-6PM PST\n• Average wait: < 2 minutes\n\n💬 **Live Chat:**\n• Available: Mon-Fri 9AM-8PM PST\n• Response time: < 30 seconds\n• Click "Live Chat" on our website\n\n📧 **Email Support:**\n• <EMAIL>\n• Response: Within 2 hours\n• Include order # for faster help\n\n🚨 **Urgent Issues:**\n• Text "URGENT" to (*************\n• We'll call you back ASAP\n\nWould you like me to connect you now? 📞`;
    }

    // Default helpful response
    else {
      return `🤖 **I'm here to help!**\n\nI can assist you with:\n• 🌿 Product information & recommendations\n• 💰 Pricing & current deals\n• 📦 Order status & tracking\n• 📊 Stock availability\n• 🚚 Shipping & delivery info\n• 📋 Store policies & returns\n• 🔍 Product search & navigation\n\n**Popular Questions:**\n• "What's the difference between Delta-8 and Delta-9?"\n• "Do you have any sales right now?"\n• "When will my order arrive?"\n• "What's your strongest CBD product?"\n\n💬 **Need human help?** Just say "human support"!\n\nWhat would you like to know? 😊`;
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const openWhatsApp = () => {
    const message = messages.length > 2 
      ? `Hi! I was chatting on your website. ${newMessage || 'I have a question about your products.'}`
      : 'Hi! I found you through your website and would like to know more about your products.';
    
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${businessPhone.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        style={{ zIndex: 10001 }}
        onClick={onClose}
      />

      {/* Chat Interface */}
      <div
        className="whatsapp-chat-interface fixed inset-2 sm:inset-auto sm:bottom-6 sm:right-6 sm:w-96 sm:h-[600px] w-full h-full sm:w-96 sm:h-[600px] bg-white rounded-none sm:rounded-lg shadow-2xl flex flex-col overflow-hidden"
        style={{ zIndex: 10001 }}
      >
        {/* Header */}
        <div className="bg-[#075E54] text-white p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden">
              <img 
                src="/Nirvana_logo.png" 
                alt={businessName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
                }}
              />
              <div className="w-full h-full bg-green-600 flex items-center justify-center text-white font-bold text-sm" style={{ display: 'none' }}>
                {businessName.charAt(0)}
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-sm">{businessName}</h3>
              <p className="text-xs opacity-90">
                {isBusinessOnline ? (
                  <span className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                    online
                  </span>
                ) : (
                  'last seen recently'
                )}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors p-1"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Messages Area */}
        <div className="whatsapp-messages flex-1 overflow-y-auto p-4 bg-[#E5DDD5] bg-opacity-30" style={{ backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="whatsapp-bg" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"%3E%3Cpath d="M0 0h100v100H0z" fill="%23E5DDD5" fill-opacity="0.1"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100" height="100" fill="url(%23whatsapp-bg)"/%3E%3C/svg%3E")' }}>
          <div className="space-y-3">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isFromBusiness ? 'justify-start' : 'justify-end'}`}
              >
                <div
                  className={`whatsapp-message-bubble whatsapp-message-animate max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                    message.isFromBusiness
                      ? 'whatsapp-message-received bg-white text-gray-800'
                      : 'whatsapp-message-sent bg-[#DCF8C6] text-gray-800'
                  } shadow-sm`}
                >
                  <p className="text-sm">{message.text}</p>
                  <div className={`flex items-center justify-end mt-1 space-x-1 ${message.isFromBusiness ? 'justify-start' : 'justify-end'}`}>
                    <span className="text-xs text-gray-500">
                      {formatTime(message.timestamp)}
                    </span>
                    {!message.isFromBusiness && (
                      <div className="flex">
                        {message.status === 'sending' && (
                          <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        )}
                        {message.status === 'sent' && (
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                        {message.status === 'delivered' && (
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            <path fillRule="evenodd" d="M19.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-1-1a1 1 0 111.414-1.414l.293.293 7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                        {message.status === 'read' && (
                          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            <path fillRule="evenodd" d="M19.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-1-1a1 1 0 111.414-1.414l.293.293 7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="whatsapp-message-bubble whatsapp-message-received bg-white text-gray-800 px-3 py-2 rounded-lg shadow-sm">
                  <div className="flex space-x-1">
                    <div className="whatsapp-typing-dot w-2 h-2 bg-gray-400 rounded-full"></div>
                    <div className="whatsapp-typing-dot w-2 h-2 bg-gray-400 rounded-full"></div>
                    <div className="whatsapp-typing-dot w-2 h-2 bg-gray-400 rounded-full"></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Quick Replies */}
        {messages.length <= 3 && (
          <div className="p-3 bg-gray-50 border-t">
            <p className="text-xs text-gray-600 mb-2">🚀 Quick actions:</p>
            <div className="flex flex-wrap gap-2">
              {quickReplies.slice(0, 4).map((reply, index) => (
                <button
                  key={index}
                  onClick={() => handleSendMessage(reply)}
                  className="text-xs bg-white border border-gray-200 rounded-full px-3 py-1 hover:bg-green-50 hover:border-green-300 transition-colors"
                >
                  {reply}
                </button>
              ))}
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {quickReplies.slice(4).map((reply, index) => (
                <button
                  key={index + 4}
                  onClick={() => handleSendMessage(reply)}
                  className="text-xs bg-white border border-gray-200 rounded-full px-3 py-1 hover:bg-green-50 hover:border-green-300 transition-colors"
                >
                  {reply}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="p-4 bg-white border-t flex items-center space-x-2">
          <button className="text-gray-500 hover:text-gray-700 transition-colors">
            <PaperClipIcon className="h-5 w-5" />
          </button>
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <button className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors">
              <FaceSmileIcon className="h-5 w-5" />
            </button>
          </div>
          <button
            onClick={() => handleSendMessage()}
            disabled={!newMessage.trim()}
            className="bg-[#075E54] text-white p-2 rounded-full hover:bg-[#064e42] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Continue on WhatsApp Button */}
        <div className="p-3 bg-green-50 border-t">
          <button
            onClick={openWhatsApp}
            className="w-full bg-[#25D366] text-white py-2 px-4 rounded-lg hover:bg-[#20b358] transition-colors text-sm font-medium flex items-center justify-center space-x-2"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
            <span>Continue on WhatsApp</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default WhatsAppChatInterface;
