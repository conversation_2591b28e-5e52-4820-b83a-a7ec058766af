#!/usr/bin/env node

/**
 * Comprehensive Sample Data Generator for Nirvana Organics E-commerce
 * Generates realistic sample data for all database models
 */

const { sequelize, models, initializeDatabase } = require('../server/models/database');
const bcrypt = require('bcrypt');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`),
  highlight: (msg) => console.log(`${colors.magenta}🌟 ${msg}${colors.reset}`)
};

// Sample Users Data
const sampleUsers = [
  {
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    phone: '15550101',
    dateOfBirth: '1985-03-15',
    role: 'customer',
    isEmailVerified: true,
    isActive: true,
    preferences: {
      newsletter: true,
      smsNotifications: false,
      emailNotifications: true
    }
  },
  {
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    phone: '15550102',
    dateOfBirth: '1990-07-22',
    role: 'customer',
    isEmailVerified: true,
    isActive: true,
    preferences: {
      newsletter: true,
      smsNotifications: true,
      emailNotifications: true
    }
  },
  {
    firstName: 'Michael',
    lastName: 'Brown',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    phone: '15550103',
    dateOfBirth: '1988-11-08',
    role: 'customer',
    isEmailVerified: true,
    isActive: true,
    preferences: {
      newsletter: false,
      smsNotifications: false,
      emailNotifications: true
    }
  },
  {
    firstName: 'Emily',
    lastName: 'Davis',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    phone: '15550104',
    dateOfBirth: '1992-05-18',
    role: 'customer',
    isEmailVerified: true,
    isActive: true,
    preferences: {
      newsletter: true,
      smsNotifications: true,
      emailNotifications: false
    }
  },
  {
    firstName: 'David',
    lastName: 'Wilson',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    phone: '15550105',
    dateOfBirth: '1987-12-03',
    role: 'customer',
    isEmailVerified: false,
    isActive: true,
    preferences: {
      newsletter: true,
      smsNotifications: false,
      emailNotifications: true
    }
  },
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'AdminPass123!',
    phone: '15550001',
    dateOfBirth: '1980-01-01',
    role: 'admin',
    isEmailVerified: true,
    isActive: true,
    preferences: {
      newsletter: false,
      smsNotifications: true,
      emailNotifications: true
    }
  },
  {
    firstName: 'Store',
    lastName: 'Manager',
    email: '<EMAIL>',
    password: 'ManagerPass123!',
    phone: '15550002',
    dateOfBirth: '1985-06-15',
    role: 'manager',
    isEmailVerified: true,
    isActive: true,
    preferences: {
      newsletter: false,
      smsNotifications: true,
      emailNotifications: true
    }
  }
];

// Sample Categories Data
const sampleCategories = [
  {
    name: 'CBD Flower',
    slug: 'cbd-flower',
    description: 'Premium CBD flower products with various strains and potencies',
    image: '/images/categories/cbd-flower.jpg',
    isActive: true,
    sortOrder: 1,
    seoTitle: 'Premium CBD Flower | Nirvana Organics',
    seoDescription: 'Shop high-quality CBD flower products. Premium strains with lab-tested potency and purity.',
    seoKeywords: 'CBD flower, hemp flower, cannabis flower, CBD buds'
  },
  {
    name: 'CBD Edibles',
    slug: 'cbd-edibles',
    description: 'Delicious CBD-infused edibles including gummies, chocolates, and baked goods',
    image: '/images/categories/cbd-edibles.jpg',
    isActive: true,
    sortOrder: 2,
    seoTitle: 'CBD Edibles & Gummies | Nirvana Organics',
    seoDescription: 'Delicious CBD edibles, gummies, and treats. Precise dosing and great taste.',
    seoKeywords: 'CBD edibles, CBD gummies, CBD chocolates, CBD treats'
  },
  {
    name: 'CBD Oils & Tinctures',
    slug: 'cbd-oils-tinctures',
    description: 'High-quality CBD oils and tinctures for sublingual use',
    image: '/images/categories/cbd-oils.jpg',
    isActive: true,
    sortOrder: 3,
    seoTitle: 'CBD Oils & Tinctures | Nirvana Organics',
    seoDescription: 'Premium CBD oils and tinctures. Fast-acting and precisely dosed.',
    seoKeywords: 'CBD oil, CBD tincture, hemp oil, CBD drops'
  },
  {
    name: 'CBD Topicals',
    slug: 'cbd-topicals',
    description: 'CBD creams, balms, and topical products for targeted relief',
    image: '/images/categories/cbd-topicals.jpg',
    isActive: true,
    sortOrder: 4,
    seoTitle: 'CBD Topicals & Creams | Nirvana Organics',
    seoDescription: 'CBD topicals for targeted relief. Creams, balms, and lotions.',
    seoKeywords: 'CBD topicals, CBD cream, CBD balm, CBD lotion'
  },
  {
    name: 'Accessories',
    slug: 'accessories',
    description: 'Smoking accessories, vaporizers, and tools',
    image: '/images/categories/accessories.jpg',
    isActive: true,
    sortOrder: 5,
    seoTitle: 'Cannabis Accessories | Nirvana Organics',
    seoDescription: 'Quality cannabis accessories, vaporizers, and smoking tools.',
    seoKeywords: 'cannabis accessories, vaporizers, smoking accessories, hemp tools'
  }
];

// Sample Products Data
const sampleProducts = [
  {
    name: 'Premium CBD Flower - Sour Diesel',
    slug: 'premium-cbd-flower-sour-diesel',
    description: 'High-quality CBD flower with energizing Sour Diesel genetics. Lab-tested for potency and purity. Perfect for daytime use with uplifting effects.',
    shortDescription: 'Energizing Sour Diesel CBD flower with uplifting effects',
    price: 29.99,
    comparePrice: 39.99,
    costPrice: 15.00,
    sku: 'CBD-FLOWER-SD-001',
    barcode: '123456789001',
    trackQuantity: true,
    quantity: 100,
    lowStockThreshold: 10,
    weight: 3.5,
    weightUnit: 'g',
    dimensions: { length: 10, width: 8, height: 2 },
    brand: 'Nirvana Organics',
    vendor: 'Premium Hemp Co.',
    tags: ['cbd', 'flower', 'sativa', 'energizing', 'premium'],
    images: [
      {
        url: '/images/products/cbd-flower-sour-diesel-1.jpg',
        alt: 'Premium CBD Flower - Sour Diesel',
        position: 0
      },
      {
        url: '/images/products/cbd-flower-sour-diesel-2.jpg',
        alt: 'Premium CBD Flower - Sour Diesel Close-up',
        position: 1
      }
    ],
    variants: [
      { name: '1g', price: 12.99, sku: 'CBD-FLOWER-SD-001-1G' },
      { name: '3.5g', price: 29.99, sku: 'CBD-FLOWER-SD-001-3.5G' },
      { name: '7g', price: 54.99, sku: 'CBD-FLOWER-SD-001-7G' }
    ],
    attributes: {
      strain: 'Sour Diesel',
      type: 'Sativa Dominant',
      cbdContent: '18-22%',
      thcContent: '<0.3%',
      terpenes: ['Limonene', 'Myrcene', 'Caryophyllene'],
      effects: ['Energizing', 'Uplifting', 'Focus'],
      labTested: true
    },
    isActive: true,
    isFeatured: true,
    isDigital: false,
    requiresShipping: true,
    taxable: true,
    seoTitle: 'Premium CBD Flower - Sour Diesel | Nirvana Organics',
    seoDescription: 'High-quality Sour Diesel CBD flower with energizing effects. Lab-tested for potency and purity.',
    seoKeywords: 'CBD flower, Sour Diesel, hemp flower, energizing CBD',
    rating: 4.8,
    reviewCount: 24,
    viewCount: 156,
    salesCount: 45
  },
  {
    name: 'CBD Gummies - Mixed Berry',
    slug: 'cbd-gummies-mixed-berry',
    description: 'Delicious mixed berry CBD gummies with 25mg CBD per gummy. Made with natural fruit flavors and organic ingredients.',
    shortDescription: 'Mixed berry CBD gummies, 25mg per piece',
    price: 39.99,
    comparePrice: 49.99,
    costPrice: 20.00,
    sku: 'CBD-GUMMIES-MB-001',
    barcode: '123456789002',
    trackQuantity: true,
    quantity: 75,
    lowStockThreshold: 15,
    weight: 120,
    weightUnit: 'g',
    dimensions: { length: 12, width: 8, height: 3 },
    brand: 'Nirvana Organics',
    vendor: 'Sweet Relief Co.',
    tags: ['cbd', 'edibles', 'gummies', 'berry', 'organic'],
    images: [
      {
        url: '/images/products/cbd-gummies-mixed-berry-1.jpg',
        alt: 'CBD Gummies - Mixed Berry',
        position: 0
      }
    ],
    variants: [
      { name: '10 count', price: 19.99, sku: 'CBD-GUMMIES-MB-001-10' },
      { name: '20 count', price: 39.99, sku: 'CBD-GUMMIES-MB-001-20' },
      { name: '30 count', price: 54.99, sku: 'CBD-GUMMIES-MB-001-30' }
    ],
    attributes: {
      cbdPerServing: '25mg',
      servingsPerContainer: '20',
      totalCbd: '500mg',
      ingredients: ['Organic Cane Sugar', 'CBD Extract', 'Natural Flavors'],
      vegan: true,
      glutenFree: true,
      labTested: true
    },
    isActive: true,
    isFeatured: true,
    isDigital: false,
    requiresShipping: true,
    taxable: true,
    seoTitle: 'CBD Gummies - Mixed Berry | Nirvana Organics',
    seoDescription: 'Delicious mixed berry CBD gummies with 25mg CBD per piece. Organic and lab-tested.',
    seoKeywords: 'CBD gummies, mixed berry, CBD edibles, organic gummies',
    rating: 4.6,
    reviewCount: 18,
    viewCount: 89,
    salesCount: 32
  }
];

module.exports = {
  sampleUsers,
  sampleCategories,
  sampleProducts,
  log,
  colors
};
