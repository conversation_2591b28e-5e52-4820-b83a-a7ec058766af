import React, { useState, useEffect } from 'react';
import { MapPinIcon, PhoneIcon, ClockIcon, CubeIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { useAppSelector } from '../hooks/redux';
import SEOHead from '../components/seo/SEOHead';
import Breadcrumb from '../components/common/Breadcrumb';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { shopFinderService } from '../services/shopFinderService';

interface StoreLocation {
  id: string;
  name: string;
  address: {
    addressLine1: string;
    addressLine2?: string;
    locality: string;
    administrativeDistrictLevel1: string;
    postalCode: string;
    country: string;
  };
  phoneNumber?: string;
  businessHours?: {
    periods: Array<{
      dayOfWeek: string;
      startLocalTime: string;
      endLocalTime: string;
    }>;
  };
  status: 'ACTIVE' | 'INACTIVE';
  inventory?: ProductInventory[];
}

interface ProductInventory {
  productId: number;
  productName: string;
  sku: string;
  quantity: number;
  price: number;
  squareItemId?: string;
  lastUpdated: string;
}

interface AdminSettings {
  showLiveInventory: boolean;
  autoRefreshInterval: number;
  displayMode: 'live' | 'manual';
}

const ShopFinder: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const [locations, setLocations] = useState<StoreLocation[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<StoreLocation | null>(null);
  const [inventory, setInventory] = useState<ProductInventory[]>([]);
  const [loading, setLoading] = useState(true);
  const [inventoryLoading, setInventoryLoading] = useState(false);
  const [adminSettings, setAdminSettings] = useState<AdminSettings>({
    showLiveInventory: true,
    autoRefreshInterval: 300000, // 5 minutes
    displayMode: 'live'
  });
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Shop Finder', href: '/shop-finder' }
  ];

  useEffect(() => {
    fetchLocations();
    if (user?.role === 'admin') {
      fetchAdminSettings();
    }
  }, [user]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (adminSettings.showLiveInventory && adminSettings.autoRefreshInterval > 0) {
      interval = setInterval(() => {
        if (selectedLocation) {
          refreshInventory(selectedLocation.id);
        }
      }, adminSettings.autoRefreshInterval);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [adminSettings, selectedLocation]);

  const fetchLocations = async () => {
    try {
      setLoading(true);
      const response = await shopFinderService.getLocations();
      setLocations(response.data);
      if (response.data.length > 0) {
        setSelectedLocation(response.data[0]);
        await fetchInventory(response.data[0].id);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAdminSettings = async () => {
    try {
      const response = await shopFinderService.getAdminSettings();
      setAdminSettings(response.data);
    } catch (error) {
      console.error('Error fetching admin settings:', error);
    }
  };

  const fetchInventory = async (locationId: string) => {
    try {
      setInventoryLoading(true);
      const response = await shopFinderService.getLocationInventory(locationId, {
        displayMode: adminSettings.displayMode
      });
      setInventory(response.data);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error fetching inventory:', error);
    } finally {
      setInventoryLoading(false);
    }
  };

  const refreshInventory = async (locationId: string) => {
    if (adminSettings.displayMode === 'live') {
      await fetchInventory(locationId);
    }
  };

  const handleLocationSelect = async (location: StoreLocation) => {
    setSelectedLocation(location);
    await fetchInventory(location.id);
  };

  const handleManualRefresh = () => {
    if (selectedLocation) {
      refreshInventory(selectedLocation.id);
    }
  };

  const updateAdminSettings = async (newSettings: Partial<AdminSettings>) => {
    try {
      const updatedSettings = { ...adminSettings, ...newSettings };
      await shopFinderService.updateAdminSettings(updatedSettings);
      setAdminSettings(updatedSettings);
    } catch (error) {
      console.error('Error updating admin settings:', error);
    }
  };

  const formatBusinessHours = (businessHours?: StoreLocation['businessHours']) => {
    if (!businessHours?.periods) return 'Hours not available';
    
    const dayOrder = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];
    const groupedHours = businessHours.periods.reduce((acc, period) => {
      const day = period.dayOfWeek;
      if (!acc[day]) acc[day] = [];
      acc[day].push(`${period.startLocalTime} - ${period.endLocalTime}`);
      return acc;
    }, {} as Record<string, string[]>);

    return dayOrder.map(day => {
      const hours = groupedHours[day];
      if (!hours) return `${day.charAt(0) + day.slice(1).toLowerCase()}: Closed`;
      return `${day.charAt(0) + day.slice(1).toLowerCase()}: ${hours.join(', ')}`;
    }).join('\n');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <>
      <SEOHead
        title="Shop Finder - Find Nirvana Organics Locations"
        description="Find Nirvana Organics store locations near you. Check real-time inventory, store hours, and contact information for all our retail locations."
        canonicalUrl="/shop-finder"
      />

      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumb items={breadcrumbItems} />
          
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Find Our Stores</h1>
            <p className="text-lg text-gray-600">
              Locate Nirvana Organics stores near you and check real-time product availability.
            </p>
          </div>

          {/* Admin Controls */}
          {user?.role === 'admin' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Controls</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Inventory Display Mode
                  </label>
                  <select
                    value={adminSettings.displayMode}
                    onChange={(e) => updateAdminSettings({ displayMode: e.target.value as 'live' | 'manual' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="live">Live Square Inventory</option>
                    <option value="manual">Manual Inventory</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Auto Refresh (minutes)
                  </label>
                  <select
                    value={adminSettings.autoRefreshInterval / 60000}
                    onChange={(e) => updateAdminSettings({ autoRefreshInterval: parseInt(e.target.value) * 60000 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="0">Disabled</option>
                    <option value="1">1 minute</option>
                    <option value="5">5 minutes</option>
                    <option value="10">10 minutes</option>
                    <option value="30">30 minutes</option>
                  </select>
                </div>
                <div className="flex items-end">
                  <button
                    onClick={handleManualRefresh}
                    disabled={inventoryLoading}
                    className="w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    <ArrowPathIcon className={`h-4 w-4 mr-2 ${inventoryLoading ? 'animate-spin' : ''}`} />
                    Refresh Now
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Store Locations List */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Store Locations</h2>
                </div>
                <div className="divide-y divide-gray-200">
                  {locations.map((location) => (
                    <div
                      key={location.id}
                      onClick={() => handleLocationSelect(location)}
                      className={`p-6 cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedLocation?.id === location.id ? 'bg-primary-50 border-r-4 border-primary-500' : ''
                      }`}
                    >
                      <h3 className="font-semibold text-gray-900 mb-2">{location.name}</h3>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-start">
                          <MapPinIcon className="h-4 w-4 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <div>{location.address.addressLine1}</div>
                            {location.address.addressLine2 && <div>{location.address.addressLine2}</div>}
                            <div>
                              {location.address.locality}, {location.address.administrativeDistrictLevel1} {location.address.postalCode}
                            </div>
                          </div>
                        </div>
                        {location.phoneNumber && (
                          <div className="flex items-center">
                            <PhoneIcon className="h-4 w-4 mr-2" />
                            {location.phoneNumber}
                          </div>
                        )}
                        <div className="flex items-center">
                          <div className={`h-2 w-2 rounded-full mr-2 ${
                            location.status === 'ACTIVE' ? 'bg-green-500' : 'bg-red-500'
                          }`} />
                          {location.status === 'ACTIVE' ? 'Open' : 'Closed'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Selected Store Details and Inventory */}
            <div className="lg:col-span-2">
              {selectedLocation ? (
                <div className="space-y-6">
                  {/* Store Details */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">{selectedLocation.name}</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                          <MapPinIcon className="h-5 w-5 mr-2" />
                          Address
                        </h3>
                        <div className="text-gray-600">
                          <div>{selectedLocation.address.addressLine1}</div>
                          {selectedLocation.address.addressLine2 && <div>{selectedLocation.address.addressLine2}</div>}
                          <div>
                            {selectedLocation.address.locality}, {selectedLocation.address.administrativeDistrictLevel1} {selectedLocation.address.postalCode}
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                          <ClockIcon className="h-5 w-5 mr-2" />
                          Business Hours
                        </h3>
                        <div className="text-gray-600 whitespace-pre-line text-sm">
                          {formatBusinessHours(selectedLocation.businessHours)}
                        </div>
                      </div>
                    </div>
                    {selectedLocation.phoneNumber && (
                      <div className="mt-4">
                        <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                          <PhoneIcon className="h-5 w-5 mr-2" />
                          Phone
                        </h3>
                        <a
                          href={`tel:${selectedLocation.phoneNumber}`}
                          className="text-primary-600 hover:text-primary-700"
                        >
                          {selectedLocation.phoneNumber}
                        </a>
                      </div>
                    )}
                  </div>

                  {/* Inventory */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                          <CubeIcon className="h-6 w-6 mr-2" />
                          Current Inventory
                        </h3>
                        <div className="flex items-center space-x-4">
                          {lastRefresh && (
                            <span className="text-sm text-gray-500">
                              Last updated: {lastRefresh.toLocaleTimeString()}
                            </span>
                          )}
                          {adminSettings.displayMode === 'live' && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Live
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      {inventoryLoading ? (
                        <div className="flex items-center justify-center py-8">
                          <LoadingSpinner />
                        </div>
                      ) : inventory.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Product
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  SKU
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Price
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Stock
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Status
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {inventory.map((item) => (
                                <tr key={item.productId} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-500">{item.sku}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">${item.price.toFixed(2)}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{item.quantity}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      item.quantity > 0
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                    }`}>
                                      {item.quantity > 0 ? 'In Stock' : 'Out of Stock'}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <CubeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-500">No inventory data available for this location.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                  <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Select a store location to view details and inventory.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ShopFinder;
