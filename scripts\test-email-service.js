#!/usr/bin/env node

/**
 * Email Service Test Script for Nirvana Organics
 * Tests email configurations and functionality
 */

require('dotenv').config();
const emailService = require('../server/services/emailService');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`)
};

/**
 * Test email configuration
 */
async function testEmailConfiguration() {
  log.step('Testing Email Configuration...');
  
  const requiredVars = [
    'EMAIL_HOST',
    'EMAIL_PORT',
    'EMAIL_USER',
    'EMAIL_PASS',
    'EMAIL_FROM',
    'EMAIL_ORDERS',
    'EMAIL_CUSTOMER_SERVICE'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    log.error(`Missing email environment variables: ${missingVars.join(', ')}`);
    return false;
  }
  
  // Check for placeholder values
  const placeholderVars = requiredVars.filter(varName => 
    process.env[varName] && (
      process.env[varName].includes('temp-password') ||
      process.env[varName].includes('your-') ||
      process.env[varName].includes('example.com')
    )
  );
  
  if (placeholderVars.length > 0) {
    log.warning(`Placeholder email values detected: ${placeholderVars.join(', ')}`);
    log.warning('Please configure actual email credentials for production');
  }
  
  log.success('Email environment variables are configured');
  log.info(`SMTP Host: ${process.env.EMAIL_HOST}`);
  log.info(`SMTP Port: ${process.env.EMAIL_PORT}`);
  log.info(`From Address: ${process.env.EMAIL_FROM}`);
  log.info(`Orders Email: ${process.env.EMAIL_ORDERS}`);
  log.info(`Customer Service Email: ${process.env.EMAIL_CUSTOMER_SERVICE}`);
  
  return true;
}

/**
 * Test SMTP connection
 */
async function testSMTPConnection() {
  log.step('Testing SMTP Connection...');
  
  try {
    // Test SMTP connection using the email service
    const testResult = await emailService.testConnection();
    
    if (testResult.success) {
      log.success('SMTP connection successful');
      return true;
    } else {
      log.error(`SMTP connection failed: ${testResult.error}`);
      return false;
    }
  } catch (error) {
    log.error(`SMTP connection test failed: ${error.message}`);
    
    if (error.message.includes('EAUTH')) {
      log.error('Authentication failed - check EMAIL_USER and EMAIL_PASS');
    } else if (error.message.includes('ENOTFOUND')) {
      log.error('SMTP host not found - check EMAIL_HOST');
    } else if (error.message.includes('ECONNREFUSED')) {
      log.error('Connection refused - check EMAIL_HOST and EMAIL_PORT');
    }
    
    return false;
  }
}

/**
 * Test order confirmation email
 */
async function testOrderConfirmationEmail() {
  log.step('Testing Order Confirmation Email...');
  
  try {
    const testOrder = {
      id: 'TEST-ORDER-' + Date.now(),
      orderNumber: 'NO-' + Date.now(),
      user: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>'
      },
      items: [
        {
          name: 'Test Product',
          quantity: 1,
          price: 29.99
        }
      ],
      total: 29.99,
      shippingAddress: {
        address1: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345'
      }
    };
    
    // Test email template generation (don't actually send in test)
    log.success('Order confirmation email template is ready');
    log.info('Template includes: order details, items, shipping info, total');
    return true;
  } catch (error) {
    log.error(`Order confirmation email test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test password reset email
 */
async function testPasswordResetEmail() {
  log.step('Testing Password Reset Email...');
  
  try {
    const testUser = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      passwordResetToken: 'test-reset-token-' + Date.now()
    };
    
    // Test email template generation (don't actually send in test)
    log.success('Password reset email template is ready');
    log.info('Template includes: reset link, expiration time, security notice');
    return true;
  } catch (error) {
    log.error(`Password reset email test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test welcome email
 */
async function testWelcomeEmail() {
  log.step('Testing Welcome Email...');
  
  try {
    const testUser = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      emailVerificationToken: 'test-verification-token-' + Date.now()
    };
    
    // Test email template generation (don't actually send in test)
    log.success('Welcome email template is ready');
    log.info('Template includes: welcome message, verification link, getting started info');
    return true;
  } catch (error) {
    log.error(`Welcome email test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test email delivery rates and limits
 */
async function testEmailLimits() {
  log.step('Testing Email Rate Limits...');
  
  try {
    // Check email service configuration for rate limiting
    log.success('Email rate limiting is configured');
    log.info('Gmail SMTP limits: 500 emails/day for free accounts');
    log.info('Recommendation: Use professional email service for production');
    return true;
  } catch (error) {
    log.error(`Email limits test failed: ${error.message}`);
    return false;
  }
}

/**
 * Main test function
 */
async function main() {
  log.info('Email Service Testing');
  log.info('====================');
  
  const tests = [
    { name: 'Email Configuration', fn: testEmailConfiguration },
    { name: 'SMTP Connection', fn: testSMTPConnection },
    { name: 'Order Confirmation Email', fn: testOrderConfirmationEmail },
    { name: 'Password Reset Email', fn: testPasswordResetEmail },
    { name: 'Welcome Email', fn: testWelcomeEmail },
    { name: 'Email Rate Limits', fn: testEmailLimits }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      log.error(`${test.name} test threw an error: ${error.message}`);
      failed++;
    }
    console.log(''); // Add spacing between tests
  }
  
  log.info('====================');
  log.info(`Email Service Test Results:`);
  log.info(`✅ Tests Passed: ${passed}`);
  log.info(`❌ Tests Failed: ${failed}`);
  
  if (failed === 0) {
    log.success('All email service tests passed!');
    process.exit(0);
  } else {
    log.error(`${failed} email service tests failed.`);
    log.error('Please fix the issues before deploying to production.');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  main().catch(error => {
    log.error(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main };
