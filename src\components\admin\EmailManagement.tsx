import React, { useState, useEffect } from 'react';
import {
  EnvelopeIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import LoadingSpinner from '../common/LoadingSpinner';
import AdminDataTable, { TableColumn, TableAction } from './AdminDataTable';

interface EmailTemplate {
  id: number;
  name: string;
  description: string;
  category: 'marketing' | 'transactional' | 'notification' | 'welcome' | 'abandoned_cart' | 'order_confirmation';
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  creator: {
    firstName: string;
    lastName: string;
  };
}

interface EmailLog {
  id: number;
  orderId: number;
  emailType: string;
  recipient: string;
  subject: string;
  status: 'queued' | 'sending' | 'sent' | 'failed' | 'retry';
  messageId: string;
  errorMessage: string;
  attempts: number;
  sentAt: string;
  createdAt: string;
}

interface EmailStats {
  sent: number;
  failed: number;
  pending: number;
  deliveryRate: number;
}

interface AdminEmailSettings {
  adminNotificationEmails: string[];
  emailFromName: string;
  emailFromAddress: string;
  enableOrderNotifications: boolean;
  enableStatusUpdates: boolean;
  enableMarketingEmails: boolean;
}

const EmailManagement: React.FC = () => {
  const dispatch = useAppDispatch();
  const [activeTab, setActiveTab] = useState<'templates' | 'logs' | 'settings' | 'stats'>('templates');
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [emailLogs, setEmailLogs] = useState<EmailLog[]>([]);
  const [emailStats, setEmailStats] = useState<EmailStats | null>(null);
  const [adminSettings, setAdminSettings] = useState<AdminEmailSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  useEffect(() => {
    fetchEmailData();
  }, [activeTab]);

  const fetchEmailData = async () => {
    try {
      setLoading(true);
      
      switch (activeTab) {
        case 'templates':
          await fetchEmailTemplates();
          break;
        case 'logs':
          await fetchEmailLogs();
          break;
        case 'settings':
          await fetchAdminSettings();
          break;
        case 'stats':
          await fetchEmailStats();
          break;
      }
    } catch (error) {
      console.error('Error fetching email data:', error);
      dispatch(addToast({
        type: 'error',
        message: 'Failed to fetch email data'
      }));
    } finally {
      setLoading(false);
    }
  };

  const fetchEmailTemplates = async () => {
    // Mock API call - replace with actual API
    const mockTemplates: EmailTemplate[] = [
      {
        id: 1,
        name: 'Order Confirmation',
        description: 'Sent to customers when they place an order',
        category: 'order_confirmation',
        subject: 'Order Confirmation - {{orderNumber}}',
        htmlContent: '<html>...</html>',
        textContent: 'Thank you for your order...',
        variables: ['orderNumber', 'customerName', 'orderTotal'],
        isActive: true,
        createdAt: '2024-01-15T10:00:00Z',
        creator: {
          firstName: 'Admin',
          lastName: 'User'
        }
      },
      {
        id: 2,
        name: 'Welcome Email',
        description: 'Welcome new customers to Nirvana Organics',
        category: 'welcome',
        subject: 'Welcome to Nirvana Organics, {{firstName}}!',
        htmlContent: '<html>...</html>',
        textContent: 'Welcome to Nirvana Organics...',
        variables: ['firstName', 'lastName'],
        isActive: true,
        createdAt: '2024-01-10T09:00:00Z',
        creator: {
          firstName: 'Marketing',
          lastName: 'Team'
        }
      }
    ];
    
    setTemplates(mockTemplates);
  };

  const fetchEmailLogs = async () => {
    // Mock API call - replace with actual API
    const mockLogs: EmailLog[] = [
      {
        id: 1,
        orderId: 1001,
        emailType: 'order-confirmation',
        recipient: '<EMAIL>',
        subject: 'Order Confirmation - #ORD-001',
        status: 'sent',
        messageId: 'msg-123456',
        errorMessage: '',
        attempts: 1,
        sentAt: '2024-01-20T14:30:00Z',
        createdAt: '2024-01-20T14:29:00Z'
      },
      {
        id: 2,
        orderId: 1002,
        emailType: 'admin-notification',
        recipient: '<EMAIL>',
        subject: 'New Order Alert - #ORD-002',
        status: 'failed',
        messageId: '',
        errorMessage: 'SMTP connection timeout',
        attempts: 3,
        sentAt: '',
        createdAt: '2024-01-20T15:00:00Z'
      }
    ];
    
    setEmailLogs(mockLogs);
  };

  const fetchEmailStats = async () => {
    // Mock API call - replace with actual API
    const mockStats: EmailStats = {
      sent: 1247,
      failed: 23,
      pending: 5,
      deliveryRate: 98.2
    };
    
    setEmailStats(mockStats);
  };

  const fetchAdminSettings = async () => {
    // Mock API call - replace with actual API
    const mockSettings: AdminEmailSettings = {
      adminNotificationEmails: [
        '<EMAIL>',
        '<EMAIL>'
      ],
      emailFromName: 'Nirvana Organics',
      emailFromAddress: '<EMAIL>',
      enableOrderNotifications: true,
      enableStatusUpdates: true,
      enableMarketingEmails: true
    };
    
    setAdminSettings(mockSettings);
  };

  const handleTemplateAction = async (action: string, template: EmailTemplate) => {
    try {
      switch (action) {
        case 'edit':
          setSelectedTemplate(template);
          setShowTemplateModal(true);
          break;
        case 'view':
          setSelectedTemplate(template);
          // Show preview modal
          break;
        case 'toggle':
          // Toggle active status
          dispatch(addToast({
            type: 'success',
            message: `Template ${template.isActive ? 'deactivated' : 'activated'} successfully`
          }));
          fetchEmailTemplates();
          break;
        case 'delete':
          if (window.confirm('Are you sure you want to delete this template?')) {
            dispatch(addToast({
              type: 'success',
              message: 'Template deleted successfully'
            }));
            fetchEmailTemplates();
          }
          break;
      }
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        message: 'Action failed. Please try again.'
      }));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
      case 'sending':
      case 'retry':
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'sending':
      case 'retry':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const templateColumns: TableColumn<EmailTemplate>[] = [
    {
      key: 'name',
      title: 'Template',
      render: (_, record) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{record.name}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
          <div className="flex items-center space-x-2 mt-1">
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
              {record.category.replace('_', ' ')}
            </span>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              record.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {record.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'subject',
      title: 'Subject',
      render: (value) => (
        <div className="text-sm text-gray-900 max-w-xs truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'variables',
      title: 'Variables',
      render: (value) => (
        <div className="text-sm text-gray-600">
          {value.length} variables
        </div>
      )
    },
    {
      key: 'creator',
      title: 'Created By',
      render: (_, record) => (
        <div className="text-sm">
          <div className="text-gray-900">
            {record.creator.firstName} {record.creator.lastName}
          </div>
          <div className="text-gray-500">
            {new Date(record.createdAt).toLocaleDateString()}
          </div>
        </div>
      )
    }
  ];

  const templateActions: TableAction<EmailTemplate>[] = [
    {
      label: 'View Template',
      onClick: (template) => handleTemplateAction('view', template),
      icon: EyeIcon
    },
    {
      label: 'Edit Template',
      onClick: (template) => handleTemplateAction('edit', template),
      icon: PencilIcon
    },
    {
      label: 'Toggle Status',
      onClick: (template) => handleTemplateAction('toggle', template),
      icon: Cog6ToothIcon
    },
    {
      label: 'Delete Template',
      onClick: (template) => handleTemplateAction('delete', template),
      icon: TrashIcon,
      variant: 'danger'
    }
  ];

  const logColumns: TableColumn<EmailLog>[] = [
    {
      key: 'emailType',
      title: 'Type',
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
          {value.replace('-', ' ')}
        </span>
      )
    },
    {
      key: 'recipient',
      title: 'Recipient',
      render: (value) => (
        <div className="text-sm text-gray-900">{value}</div>
      )
    },
    {
      key: 'subject',
      title: 'Subject',
      render: (value) => (
        <div className="text-sm text-gray-900 max-w-xs truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (value, record) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(value)}
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(value)}`}>
            {value}
          </span>
          {record.attempts > 1 && (
            <span className="text-xs text-gray-500">({record.attempts} attempts)</span>
          )}
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'Created',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {new Date(value).toLocaleString()}
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Email Management</h2>
          <p className="text-gray-600">Manage email templates, delivery logs, and notification settings</p>
        </div>
        <button
          onClick={() => setShowTemplateModal(true)}
          className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Template
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'templates', label: 'Templates', icon: EnvelopeIcon },
            { key: 'logs', label: 'Delivery Logs', icon: ChartBarIcon },
            { key: 'stats', label: 'Statistics', icon: ChartBarIcon },
            { key: 'settings', label: 'Settings', icon: Cog6ToothIcon }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : (
          <>
            {activeTab === 'templates' && (
              <AdminDataTable
                columns={templateColumns}
                data={templates}
                actions={templateActions}
                loading={loading}
                emptyText="No email templates found"
              />
            )}

            {activeTab === 'logs' && (
              <AdminDataTable
                columns={logColumns}
                data={emailLogs}
                loading={loading}
                emptyText="No email logs found"
              />
            )}

            {activeTab === 'stats' && emailStats && (
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">{emailStats.sent}</div>
                    <div className="text-sm text-gray-600">Emails Sent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600">{emailStats.failed}</div>
                    <div className="text-sm text-gray-600">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-600">{emailStats.pending}</div>
                    <div className="text-sm text-gray-600">Pending</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{emailStats.deliveryRate}%</div>
                    <div className="text-sm text-gray-600">Delivery Rate</div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'settings' && adminSettings && (
              <div className="p-6 space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Admin Notification Emails</h3>
                  <div className="space-y-2">
                    {adminSettings.adminNotificationEmails.map((email, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <span className="text-sm text-gray-900">{email}</span>
                        <button className="text-red-600 hover:text-red-800">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <button className="mt-3 text-sm text-primary-600 hover:text-primary-800">
                    + Add Email Address
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      From Name
                    </label>
                    <input
                      type="text"
                      value={adminSettings.emailFromName}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      From Email
                    </label>
                    <input
                      type="email"
                      value={adminSettings.emailFromAddress}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      readOnly
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Email Notifications</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium text-gray-900">Order Notifications</div>
                        <div className="text-xs text-gray-500">Send admin alerts for new orders</div>
                      </div>
                      <button
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          adminSettings.enableOrderNotifications ? 'bg-primary-600' : 'bg-gray-200'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            adminSettings.enableOrderNotifications ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium text-gray-900">Status Updates</div>
                        <div className="text-xs text-gray-500">Send customer order status updates</div>
                      </div>
                      <button
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          adminSettings.enableStatusUpdates ? 'bg-primary-600' : 'bg-gray-200'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            adminSettings.enableStatusUpdates ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default EmailManagement;
