import React, { useEffect, useState } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchOrder, cancelOrder, clearError } from '../store/slices/orderSlice';
import { addToast } from '../store/slices/uiSlice';
import LoadingSpinner from '../components/common/LoadingSpinner';
import {
  ArrowLeftIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CreditCardIcon,
  ArrowPathIcon,
  PrinterIcon,
  ChatBubbleLeftEllipsisIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import SEOHead from '../components/seo/SEOHead';
import Breadcrumb from '../components/common/Breadcrumb';

const OrderDetail: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [cancelling, setCancelling] = useState(false);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { currentOrder: order, loading, error } = useAppSelector((state) => state.orders);
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: { pathname: `/orders/${orderId}` } } });
      return;
    }

    if (orderId) {
      dispatch(fetchOrder(orderId));
    }
  }, [dispatch, orderId, isAuthenticated, navigate]);

  useEffect(() => {
    if (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error Loading Order',
        message: error,
      }));
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-6 w-6 text-yellow-500" />;
      case 'processing':
        return <ArrowPathIcon className="h-6 w-6 text-blue-500" />;
      case 'shipped':
        return <TruckIcon className="h-6 w-6 text-purple-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      case 'refunded':
        return <CreditCardIcon className="h-6 w-6 text-gray-500" />;
      default:
        return <ClockIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'refunded':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleCancelOrder = async () => {
    if (!order || !cancelReason.trim()) {
      dispatch(addToast({
        type: 'error',
        title: 'Validation Error',
        message: 'Please provide a reason for cancellation',
      }));
      return;
    }

    setCancelling(true);
    try {
      await dispatch(cancelOrder({
        orderId: order.id.toString(),
        reason: cancelReason.trim()
      })).unwrap();

      dispatch(addToast({
        type: 'success',
        title: 'Order Cancelled',
        message: 'Your order has been successfully cancelled',
      }));
      setShowCancelModal(false);
      setCancelReason('');
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Cancellation Failed',
        message: 'Failed to cancel order. Please try again or contact support.',
      }));
    } finally {
      setCancelling(false);
    }
  };

  const handlePrintOrder = () => {
    window.print();
  };

  const handleContactSupport = () => {
    // This could open a chat widget or navigate to contact page
    navigate('/contact', {
      state: {
        subject: `Order Support - #${order?.orderNumber}`,
        message: `I need help with my order #${order?.orderNumber}. Please assist me.`
      }
    });
  };

  const canCancelOrder = (order: any) => {
    return order && ['pending', 'processing'].includes(order.status);
  };

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <LoadingSpinner size="large" />
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-24 w-24 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Order not found</h3>
          <p className="text-gray-600 mb-6">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link
            to="/orders"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Orders
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <SEOHead
        title={`Order #${order?.orderNumber || 'Details'} - Track Your Cannabis Order`}
        description="View detailed information about your cannabis product order including status, tracking, items, and shipping details. Manage your order and contact support."
        keywords={['order details', 'order tracking', 'cannabis order', 'order status', 'track shipment']}
        canonicalUrl={`/orders/${orderId}`}
        noIndex={true}
      />

      {/* Breadcrumb */}
      <Breadcrumb
        items={[
          { label: 'My Account', href: '/profile' },
          { label: 'My Orders', href: '/orders' },
          { label: `Order #${order?.orderNumber || 'Details'}`, current: true }
        ]}
        className="mb-6"
      />

      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Link
            to="/orders"
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Orders
          </Link>
          <div className="h-6 border-l border-gray-300" />
          <h1 className="text-3xl font-bold text-gray-900">
            Order #{order.orderNumber}
          </h1>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={handlePrintOrder}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <PrinterIcon className="h-4 w-4 mr-1" />
            Print
          </button>

          <button
            onClick={handleContactSupport}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <ChatBubbleLeftEllipsisIcon className="h-4 w-4 mr-1" />
            Contact Support
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Order Status */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Order Status</h2>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${getStatusColor(order.status)}`}>
                {getStatusIcon(order.status)}
                <span className="font-medium">
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </span>
              </div>
            </div>

            {/* Status Timeline */}
            <div className="space-y-4">
              {order.statusHistory?.map((status, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-primary-600 rounded-full mt-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                    </p>
                    <p className="text-sm text-gray-600">{formatDate(status.date)}</p>
                    {status.note && (
                      <p className="text-sm text-gray-600 mt-1">{status.note}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Tracking Information */}
            {order.trackingNumber && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <TruckIcon className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Tracking Information</span>
                </div>
                <p className="text-sm text-blue-800">
                  <span className="font-medium">Tracking Number:</span> {order.trackingNumber}
                </p>
                {order.shippingCarrier && (
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">Carrier:</span> {order.shippingCarrier}
                  </p>
                )}
                {order.estimatedDelivery && (
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">Estimated Delivery:</span> {formatDate(order.estimatedDelivery)}
                  </p>
                )}
              </div>
            )}

            {/* Cancel Order Button */}
            {canCancelOrder(order) && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                >
                  <XCircleIcon className="h-4 w-4 mr-2" />
                  Cancel Order
                </button>
              </div>
            )}
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Items</h2>
            <div className="space-y-4">
              {order.items.map((item, index) => (
                <div key={index} className="flex items-center space-x-4 py-4 border-b border-gray-200 last:border-b-0">
                  <div className="flex-shrink-0 w-20 h-20 bg-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={item.product?.images?.[0]?.url || '/images/placeholder-product.jpg'}
                      alt={item.product?.name || 'Product'}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <Link
                      to={`/product/${item.product?.slug}`}
                      className="text-lg font-medium text-gray-900 hover:text-primary-600"
                    >
                      {item.product?.name || 'Product'}
                    </Link>
                    {item.product?.description && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {item.product.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                      <span>Quantity: {item.quantity}</span>
                      <span>Price: {formatCurrency(item.price)}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(item.quantity * item.price)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Subtotal</span>
                <span className="text-gray-900">{formatCurrency(order.subtotal)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Shipping</span>
                <span className="text-gray-900">{formatCurrency(order.shipping)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tax</span>
                <span className="text-gray-900">{formatCurrency(order.tax)}</span>
              </div>
              {order.discount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Discount</span>
                  <span className="text-green-600">-{formatCurrency(order.discount)}</span>
                </div>
              )}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total</span>
                  <span className="text-lg font-semibold text-gray-900">{formatCurrency(order.total)}</span>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                <p><span className="font-medium">Order Date:</span> {formatDate(order.createdAt)}</p>
                <p><span className="font-medium">Payment Method:</span> {order.paymentMethod}</p>
                <p><span className="font-medium">Payment Status:</span>
                  <span className={`ml-1 ${order.paymentStatus === 'paid' ? 'text-green-600' : 'text-yellow-600'}`}>
                    {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                  </span>
                </p>
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Shipping Address</h2>
            <div className="text-sm text-gray-600">
              <p className="font-medium text-gray-900">
                {order.shippingAddress.firstName} {order.shippingAddress.lastName}
              </p>
              {order.shippingAddress.company && (
                <p>{order.shippingAddress.company}</p>
              )}
              <p>{order.shippingAddress.address1}</p>
              {order.shippingAddress.address2 && (
                <p>{order.shippingAddress.address2}</p>
              )}
              <p>
                {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
              </p>
              <p>{order.shippingAddress.country}</p>
              {order.shippingAddress.phone && (
                <p className="mt-2">Phone: {order.shippingAddress.phone}</p>
              )}
            </div>
          </div>

          {/* Billing Address */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Billing Address</h2>
            <div className="text-sm text-gray-600">
              <p className="font-medium text-gray-900">
                {order.billingAddress.firstName} {order.billingAddress.lastName}
              </p>
              {order.billingAddress.company && (
                <p>{order.billingAddress.company}</p>
              )}
              <p>{order.billingAddress.address1}</p>
              {order.billingAddress.address2 && (
                <p>{order.billingAddress.address2}</p>
              )}
              <p>
                {order.billingAddress.city}, {order.billingAddress.state} {order.billingAddress.zipCode}
              </p>
              <p>{order.billingAddress.country}</p>
              {order.billingAddress.phone && (
                <p className="mt-2">Phone: {order.billingAddress.phone}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Cancel Order Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Cancel Order</h3>
                <button
                  onClick={() => setShowCancelModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="h-6 w-6" />
                </button>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Are you sure you want to cancel this order? This action cannot be undone.
              </p>

              <div className="mb-4">
                <label htmlFor="cancelReason" className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for cancellation *
                </label>
                <textarea
                  id="cancelReason"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Please provide a reason for cancelling this order..."
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                />
              </div>

              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowCancelModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Keep Order
                </button>
                <button
                  onClick={handleCancelOrder}
                  disabled={cancelling || !cancelReason.trim()}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {cancelling ? (
                    <div className="flex items-center">
                      <LoadingSpinner size="small" color="white" />
                      <span className="ml-2">Cancelling...</span>
                    </div>
                  ) : (
                    'Cancel Order'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderDetail;
