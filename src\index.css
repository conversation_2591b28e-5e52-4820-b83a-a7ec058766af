@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #f9fafb;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    width: 100%;
  }

  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  .whatsapp-widget-container {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 10000 !important;
    pointer-events: auto !important;
    transition: transform 0.3s ease !important;
  }

  @media (max-width: 640px) {
    .whatsapp-widget-container {
      bottom: 15px !important;
      right: 15px !important;
    }

    .whatsapp-widget-container button {
      width: 56px !important;
      height: 56px !important;
      touch-action: manipulation !important;
    }
  }
}

@layer components {
  .mobile-viewport-fix {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  .no-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }

  .mobile-stable {
    position: relative;
    width: 100%;
    overflow-x: hidden;
  }

  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .no-zoom {
    font-size: 16px;
  }

  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

@media (max-width: 320px) {
  .container {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
  
  .whatsapp-widget-container {
    bottom: 10px !important;
    right: 10px !important;
  }
  
  .whatsapp-widget-container button {
    width: 48px !important;
    height: 48px !important;
  }
}

@media (max-width: 375px) {
  body {
    font-size: 14px;
  }
  
  .hero-section {
    height: 350px !important;
  }
}

@media (max-width: 768px) {
  * {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }
  
  .container, .mx-auto {
    overflow-x: hidden !important;
  }
  
  input, textarea, select {
    font-size: 16px !important;
    padding: 12px !important;
  }
  
  button, .btn, a[role="button"] {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 12px 16px !important;
    touch-action: manipulation !important;
  }
}
