import React from 'react';
import { Link } from 'react-router-dom';
import AdminBreadcrumb from './AdminBreadcrumb';

interface BreadcrumbItem {
  name: string;
  href?: string;
  current?: boolean;
}

interface ActionButton {
  label: string;
  href?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  icon?: React.ComponentType<any>;
  disabled?: boolean;
}

interface AdminPageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: ActionButton[];
  stats?: Array<{
    label: string;
    value: string | number;
    change?: {
      value: number;
      type: 'increase' | 'decrease';
    };
  }>;
}

const AdminPageHeader: React.FC<AdminPageHeaderProps> = ({
  title,
  subtitle,
  breadcrumbs,
  actions = [],
  stats = []
}) => {
  const getButtonClasses = (variant: ActionButton['variant'] = 'primary', disabled = false) => {
    const baseClasses = 'inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200';
    
    if (disabled) {
      return `${baseClasses} border-gray-300 text-gray-400 bg-gray-100 cursor-not-allowed`;
    }
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} border-transparent text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500`;
      case 'secondary':
        return `${baseClasses} border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500`;
      case 'danger':
        return `${baseClasses} border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500`;
      default:
        return `${baseClasses} border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500`;
    }
  };

  const renderActionButton = (action: ActionButton, index: number) => {
    const classes = getButtonClasses(action.variant, action.disabled);
    
    const buttonContent = (
      <>
        {action.icon && (
          <action.icon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
        )}
        {action.label}
      </>
    );

    if (action.href && !action.disabled) {
      return (
        <Link key={index} to={action.href} className={classes}>
          {buttonContent}
        </Link>
      );
    }

    return (
      <button
        key={index}
        onClick={action.onClick}
        disabled={action.disabled}
        className={classes}
      >
        {buttonContent}
      </button>
    );
  };

  return (
    <div className="mb-8">
      {/* Breadcrumbs */}
      <AdminBreadcrumb items={breadcrumbs} />
      
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            {title}
          </h1>
          {subtitle && (
            <p className="mt-1 text-sm text-gray-500">
              {subtitle}
            </p>
          )}
        </div>
        
        {/* Actions */}
        {actions.length > 0 && (
          <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
            {actions.map(renderActionButton)}
          </div>
        )}
      </div>

      {/* Stats */}
      {stats.length > 0 && (
        <div className="mt-6">
          <dl className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
              >
                <dt>
                  <p className="text-sm font-medium text-gray-500 truncate">
                    {stat.label}
                  </p>
                </dt>
                <dd className="flex items-baseline pb-6 sm:pb-7">
                  <p className="text-2xl font-semibold text-gray-900">
                    {stat.value}
                  </p>
                  {stat.change && (
                    <p
                      className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.change.type === 'increase'
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}
                    >
                      {stat.change.type === 'increase' ? (
                        <svg
                          className="self-center flex-shrink-0 h-5 w-5 text-green-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="self-center flex-shrink-0 h-5 w-5 text-red-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span className="sr-only">
                        {stat.change.type === 'increase' ? 'Increased' : 'Decreased'} by
                      </span>
                      {Math.abs(stat.change.value)}%
                    </p>
                  )}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      )}
    </div>
  );
};

export default AdminPageHeader;
