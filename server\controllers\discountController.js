const { models } = require('../models');
const { validationResult } = require('express-validator');
const DiscountService = require('../services/discountService');
const { Op } = require('sequelize');

/**
 * Discount Controller
 * Handles coupon validation, referrals, and social sharing
 */
class DiscountController {
  /**
   * Validate coupon code
   * @route POST /api/coupons/validate
   * @access Public
   */
  static async validateCoupon(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { couponCode, orderData } = req.body;
      const userId = req.user?.id || null;
      const guestEmail = req.body.guestEmail || null;

      const result = await DiscountService.validateAndApplyCoupon(
        couponCode,
        orderData,
        userId,
        guestEmail
      );

      if (result.success) {
        res.json({
          success: true,
          data: result,
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message
        });
      }

    } catch (error) {
      console.error('Validate coupon error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate coupon',
        error: error.message
      });
    }
  }

  /**
   * Get available coupons
   * @route GET /api/coupons/available
   * @access Public
   */
  static async getAvailableCoupons(req, res) {
    try {
      const userId = req.user?.id || null;
      const result = await DiscountService.getAvailableCoupons(userId);

      res.json(result);

    } catch (error) {
      console.error('Get available coupons error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch available coupons',
        error: error.message
      });
    }
  }

  /**
   * Generate referral code for user
   * @route POST /api/referrals/generate
   * @access Private
   */
  static async generateReferralCode(req, res) {
    try {
      const userId = req.user.id;
      const result = await DiscountService.generateReferralCode(userId);

      if (result.success) {
        res.json({
          success: true,
          data: {
            referralCode: result.referralCode,
            shareUrl: `${process.env.FRONTEND_URL}/shop?ref=${result.referralCode}`
          },
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message
        });
      }

    } catch (error) {
      console.error('Generate referral code error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate referral code',
        error: error.message
      });
    }
  }

  /**
   * Get user's referral statistics
   * @route GET /api/referrals/stats
   * @access Private
   */
  static async getReferralStats(req, res) {
    try {
      const userId = req.user.id;
      const result = await DiscountService.getUserReferralStats(userId);

      res.json(result);

    } catch (error) {
      console.error('Get referral stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch referral statistics',
        error: error.message
      });
    }
  }

  /**
   * Record social media share
   * @route POST /api/social-shares
   * @access Private
   */
  static async recordSocialShare(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { platform, shareUrl, couponId } = req.body;
      const userId = req.user.id;

      const result = await DiscountService.recordSocialShare(
        userId,
        platform,
        shareUrl,
        couponId
      );

      res.json(result);

    } catch (error) {
      console.error('Record social share error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to record social share',
        error: error.message
      });
    }
  }

  /**
   * Get user's coupon usage history
   * @route GET /api/coupons/usage-history
   * @access Private
   */
  static async getCouponUsageHistory(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20 } = req.query;

      const offset = (page - 1) * limit;

      const { count, rows: usages } = await models.CouponUsage.findAndCountAll({
        where: { userId },
        include: [
          {
            model: models.Coupon,
            as: 'coupon',
            attributes: ['code', 'name', 'type', 'value']
          },
          {
            model: models.Order,
            as: 'order',
            attributes: ['orderNumber', 'total', 'createdAt']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: {
          usages,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalUsages: count,
            limit: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('Get coupon usage history error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch coupon usage history',
        error: error.message
      });
    }
  }

  /**
   * Get user's social sharing history
   * @route GET /api/social-shares/history
   * @access Private
   */
  static async getSocialShareHistory(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20 } = req.query;

      const offset = (page - 1) * limit;

      const { count, rows: shares } = await models.SocialShare.findAndCountAll({
        where: { userId },
        include: [
          {
            model: models.Coupon,
            as: 'coupon',
            attributes: ['code', 'name'],
            required: false
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalBonusEarned = await models.SocialShare.sum('bonusEarned', {
        where: { userId, isVerified: true }
      });

      res.json({
        success: true,
        data: {
          shares,
          totalBonusEarned: totalBonusEarned || 0,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalShares: count,
            limit: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('Get social share history error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch social share history',
        error: error.message
      });
    }
  }

  /**
   * Apply referral code during checkout
   * @route POST /api/referrals/apply
   * @access Public
   */
  static async applyReferralCode(req, res) {
    try {
      const { referralCode } = req.body;
      const userId = req.user?.id;

      if (!referralCode) {
        return res.status(400).json({
          success: false,
          message: 'Referral code is required'
        });
      }

      // Find the referral
      const referral = await models.Referral.findOne({
        where: {
          referralCode: referralCode.toUpperCase(),
          status: 'pending'
        },
        include: [
          {
            model: models.User,
            as: 'referrer',
            attributes: ['firstName', 'lastName']
          }
        ]
      });

      if (!referral) {
        return res.status(404).json({
          success: false,
          message: 'Invalid or expired referral code'
        });
      }

      // Check if user is trying to use their own referral code
      if (userId && referral.referrerId === userId) {
        return res.status(400).json({
          success: false,
          message: 'You cannot use your own referral code'
        });
      }

      // Check if user has already been referred
      if (userId) {
        const existingReferral = await models.Referral.findOne({
          where: {
            refereeId: userId,
            status: { [Op.in]: ['completed', 'rewarded'] }
          }
        });

        if (existingReferral) {
          return res.status(400).json({
            success: false,
            message: 'You have already been referred by another user'
          });
        }
      }

      res.json({
        success: true,
        data: {
          referralCode: referral.referralCode,
          referrerName: `${referral.referrer.firstName} ${referral.referrer.lastName}`,
          discount: 5.00 // $5 discount for new customers
        },
        message: `Referral code applied! You'll get $5 off your first order, and ${referral.referrer.firstName} will get a reward too!`
      });

    } catch (error) {
      console.error('Apply referral code error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to apply referral code',
        error: error.message
      });
    }
  }
}

module.exports = DiscountController;
