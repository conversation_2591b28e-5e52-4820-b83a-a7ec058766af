#!/bin/bash

# ============================================================================
# SSL Certificate Setup Script
# Configures SSL certificates and HTTPS for production deployment
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${CYAN}🔧 $1${NC}"
}

# Configuration
DOMAIN="shopnirvanaorganics.com"
WWW_DOMAIN="www.shopnirvanaorganics.com"
EMAIL="<EMAIL>"
NGINX_AVAILABLE="/etc/nginx/sites-available"
NGINX_ENABLED="/etc/nginx/sites-enabled"
SITE_CONFIG="nirvana-organics"

echo "============================================================================"
echo "🔒 SSL CERTIFICATE SETUP - NIRVANA ORGANICS"
echo "============================================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: Install required packages
log_step "Step 1: Installing required packages"

# Update package list
apt update

# Install Nginx if not already installed
if ! command -v nginx &> /dev/null; then
    log_info "Installing Nginx..."
    apt install -y nginx
    systemctl enable nginx
    log_success "Nginx installed"
else
    log_info "Nginx already installed"
fi

# Install Certbot if not already installed
if ! command -v certbot &> /dev/null; then
    log_info "Installing Certbot..."
    apt install -y certbot python3-certbot-nginx
    log_success "Certbot installed"
else
    log_info "Certbot already installed"
fi

# Step 2: Configure Nginx for HTTP (before SSL)
log_step "Step 2: Configuring Nginx for HTTP"

cat > $NGINX_AVAILABLE/$SITE_CONFIG << EOF
# Nirvana Organics - Initial HTTP Configuration
server {
    listen 80;
    server_name $DOMAIN $WWW_DOMAIN;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    # Main API
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files and uploads
    location /uploads/ {
        alias /var/www/nirvana-backend/shared/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://localhost:5000/health;
    }
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
}
EOF

# Enable the site
ln -sf $NGINX_AVAILABLE/$SITE_CONFIG $NGINX_ENABLED/
rm -f $NGINX_ENABLED/default

# Test Nginx configuration
nginx -t
if [ $? -eq 0 ]; then
    log_success "Nginx configuration is valid"
    systemctl reload nginx
else
    log_error "Nginx configuration is invalid"
    exit 1
fi

# Step 3: Obtain SSL certificates
log_step "Step 3: Obtaining SSL certificates with Let's Encrypt"

# Check if certificates already exist
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    log_warning "SSL certificates already exist for $DOMAIN"
    log_info "Checking certificate expiration..."
    
    # Check certificate expiration
    EXPIRY_DATE=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/$DOMAIN/cert.pem | cut -d= -f2)
    EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_EPOCH=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))
    
    log_info "Certificate expires in $DAYS_UNTIL_EXPIRY days"
    
    if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
        log_warning "Certificate expires soon, renewing..."
        certbot renew --nginx --non-interactive
    else
        log_success "Certificate is valid and not expiring soon"
    fi
else
    log_info "Obtaining new SSL certificates..."
    
    # Obtain certificates
    certbot --nginx -d $DOMAIN -d $WWW_DOMAIN \
        --non-interactive \
        --agree-tos \
        --email $EMAIL \
        --redirect
    
    if [ $? -eq 0 ]; then
        log_success "SSL certificates obtained successfully"
    else
        log_error "Failed to obtain SSL certificates"
        exit 1
    fi
fi

# Step 4: Configure automatic renewal
log_step "Step 4: Setting up automatic certificate renewal"

# Create renewal cron job
CRON_JOB="0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx"

# Check if cron job already exists
if ! crontab -l 2>/dev/null | grep -q "certbot renew"; then
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    log_success "Automatic renewal cron job created"
else
    log_info "Automatic renewal cron job already exists"
fi

# Test renewal process
log_info "Testing certificate renewal process..."
certbot renew --dry-run
if [ $? -eq 0 ]; then
    log_success "Certificate renewal test passed"
else
    log_warning "Certificate renewal test failed - check configuration"
fi

# Step 5: Update Nginx configuration for enhanced security
log_step "Step 5: Updating Nginx configuration for enhanced security"

# Backup current configuration
cp $NGINX_AVAILABLE/$SITE_CONFIG $NGINX_AVAILABLE/$SITE_CONFIG.backup

# Create enhanced HTTPS configuration
cat > $NGINX_AVAILABLE/$SITE_CONFIG << EOF
# Nirvana Organics - Production HTTPS Configuration

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name $DOMAIN $WWW_DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    server_name $DOMAIN $WWW_DOMAIN;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://js.squareup.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://connect.squareup.com;" always;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    # Main API
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files and uploads
    location /uploads/ {
        alias /var/www/nirvana-backend/shared/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://localhost:5000/health;
    }
}
EOF

# Test and reload Nginx
nginx -t
if [ $? -eq 0 ]; then
    log_success "Enhanced Nginx configuration is valid"
    systemctl reload nginx
else
    log_error "Enhanced Nginx configuration is invalid"
    # Restore backup
    cp $NGINX_AVAILABLE/$SITE_CONFIG.backup $NGINX_AVAILABLE/$SITE_CONFIG
    systemctl reload nginx
    exit 1
fi

# Step 6: Verify SSL configuration
log_step "Step 6: Verifying SSL configuration"

# Test HTTPS connection
if curl -f -s https://$DOMAIN/health >/dev/null; then
    log_success "HTTPS connection test passed"
else
    log_warning "HTTPS connection test failed - check application status"
fi

# Test SSL certificate
log_info "SSL Certificate Information:"
echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates

# Final status
echo ""
echo "============================================================================"
log_success "🎉 SSL CERTIFICATE SETUP COMPLETED SUCCESSFULLY!"
echo "============================================================================"

log_info "SSL Configuration Summary:"
echo "• Domain: $DOMAIN"
echo "• WWW Domain: $WWW_DOMAIN"
echo "• Certificate Authority: Let's Encrypt"
echo "• Auto-renewal: Enabled (daily check at 12:00)"
echo "• HTTPS Redirect: Enabled"
echo "• Security Headers: Configured"

echo ""
log_info "Next Steps:"
echo "1. Test your website: https://$DOMAIN"
echo "2. Verify SSL rating: https://www.ssllabs.com/ssltest/"
echo "3. Update application environment variables to use HTTPS"
echo "4. Test all application functionality over HTTPS"

echo ""
log_warning "Important Reminders:"
echo "• Monitor certificate expiration dates"
echo "• Test renewal process periodically"
echo "• Keep Nginx and Certbot updated"
echo "• Monitor SSL security best practices"
