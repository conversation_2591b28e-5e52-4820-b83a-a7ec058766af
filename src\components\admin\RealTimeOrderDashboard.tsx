import React, { useState, useEffect, useRef } from 'react';
import {
  ShoppingBagIcon,
  UserIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  EyeIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import io, { Socket } from 'socket.io-client';

interface OrderItem {
  productName: string;
  sku: string;
  quantity: number;
  price: number;
  total: number;
}

interface Customer {
  name: string;
  email: string;
  membershipType: 'guest' | 'first-time' | 'regular' | 'premium';
}

interface RealTimeOrder {
  id: number;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];
  pricing: {
    subtotal: number;
    tax: number;
    shipping: number;
    discount: number;
    total: number;
  };
  coupons: Array<{
    code: string;
    name: string;
    discount: number;
  }>;
  shipping: {
    method: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  payment: {
    status: string;
    method: string;
    transactionId: string;
  };
  status: string;
  createdAt: string;
  estimatedDelivery: string;
  priority: 'normal' | 'high';
}

interface RealTimeMetrics {
  today: {
    orders: number;
    revenue: number;
    averageOrderValue: number;
  };
  week: {
    orders: number;
    revenue: number;
  };
  month: {
    orders: number;
    revenue: number;
  };
  activeCustomers: number;
  pendingOrders: number;
  timestamp: string;
}

const RealTimeOrderDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [orders, setOrders] = useState<RealTimeOrder[]>([]);
  const [metrics, setMetrics] = useState<RealTimeMetrics | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<RealTimeOrder | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');

  useEffect(() => {
    initializeWebSocket();
    
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  const initializeWebSocket = () => {
    const token = localStorage.getItem('adminToken'); // Get admin token
    
    socketRef.current = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    const socket = socketRef.current;

    socket.on('connect', () => {
      console.log('Connected to WebSocket server');
      setIsConnected(true);
      setConnectionStatus('connected');
      
      // Authenticate as admin
      socket.emit('authenticate', {
        token,
        userRole: 'admin'
      });
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setIsConnected(false);
      setConnectionStatus('disconnected');
    });

    socket.on('auth-error', (data) => {
      console.error('Authentication error:', data);
      dispatch(addToast({
        type: 'error',
        message: 'Failed to authenticate for real-time updates'
      }));
    });

    socket.on('initial-orders', (initialOrders: RealTimeOrder[]) => {
      console.log('Received initial orders:', initialOrders.length);
      setOrders(initialOrders);
    });

    socket.on('initial-metrics', (initialMetrics: RealTimeMetrics) => {
      console.log('Received initial metrics');
      setMetrics(initialMetrics);
    });

    socket.on('new-order', (newOrder: RealTimeOrder) => {
      console.log('New order received:', newOrder.orderNumber);
      setOrders(prevOrders => [newOrder, ...prevOrders.slice(0, 49)]); // Keep last 50 orders
      
      // Show notification for new order
      dispatch(addToast({
        type: 'success',
        message: `New order received: ${newOrder.orderNumber} - $${newOrder.pricing.total.toFixed(2)}`
      }));

      // Play notification sound (optional)
      playNotificationSound();
    });

    socket.on('metrics-update', (updatedMetrics: RealTimeMetrics) => {
      setMetrics(updatedMetrics);
    });

    socket.on('order-status-update', (updateData) => {
      console.log('Order status update:', updateData);
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === updateData.orderId 
            ? { ...order, status: updateData.newStatus }
            : order
        )
      );
    });

    socket.on('system-alert', (alertData) => {
      dispatch(addToast({
        type: 'warning',
        message: alertData.message
      }));
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      setConnectionStatus('disconnected');
      dispatch(addToast({
        type: 'error',
        message: 'Failed to connect to real-time server'
      }));
    });

    // Ping server every 30 seconds to maintain connection
    const pingInterval = setInterval(() => {
      if (socket.connected) {
        socket.emit('ping');
      }
    }, 30000);

    socket.on('pong', () => {
      // Connection is healthy
    });

    return () => {
      clearInterval(pingInterval);
    };
  };

  const playNotificationSound = () => {
    // Create a simple notification sound
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = 800;
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    return priority === 'high' ? 'border-l-4 border-red-500' : 'border-l-4 border-gray-300';
  };

  const getMembershipBadge = (membershipType: string) => {
    const colors = {
      premium: 'bg-purple-100 text-purple-800',
      regular: 'bg-blue-100 text-blue-800',
      'first-time': 'bg-green-100 text-green-800',
      guest: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${colors[membershipType as keyof typeof colors]}`}>
        {membershipType}
      </span>
    );
  };

  const getConnectionStatusIndicator = () => {
    const statusConfig = {
      connecting: { color: 'bg-yellow-500', text: 'Connecting...' },
      connected: { color: 'bg-green-500', text: 'Connected' },
      disconnected: { color: 'bg-red-500', text: 'Disconnected' }
    };

    const config = statusConfig[connectionStatus];

    return (
      <div className="flex items-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${config.color}`}></div>
        <span className="text-sm text-gray-600">{config.text}</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Real-Time Order Monitoring</h2>
          <p className="text-gray-600">Live order tracking and customer purchase activity</p>
        </div>
        <div className="flex items-center space-x-4">
          {getConnectionStatusIndicator()}
          <button
            onClick={() => window.location.reload()}
            className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Real-Time Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShoppingBagIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Orders</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.today.orders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.today.revenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Customers</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.activeCustomers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Orders</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.pendingOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TruckIcon className="h-8 w-8 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Order Value</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.today.averageOrderValue)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Live Order Feed */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Live Order Feed</h3>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">Live</span>
            </div>
          </div>
        </div>

        <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
          {orders.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <ShoppingBagIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No orders yet. Waiting for new orders...</p>
            </div>
          ) : (
            orders.map((order) => (
              <div
                key={order.id}
                className={`p-6 hover:bg-gray-50 cursor-pointer ${getPriorityColor(order.priority)}`}
                onClick={() => setSelectedOrder(order)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Order #{order.orderNumber}
                        </p>
                        <p className="text-sm text-gray-600">
                          {order.customer.name} • {order.customer.email}
                        </p>
                      </div>
                      {getMembershipBadge(order.customer.membershipType)}
                    </div>
                    
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                      <span>{order.items.length} item{order.items.length !== 1 ? 's' : ''}</span>
                      <span>•</span>
                      <span>{formatCurrency(order.pricing.total)}</span>
                      <span>•</span>
                      <span>{formatDateTime(order.createdAt)}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedOrder(order);
                      }}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Order Details - #{selectedOrder.orderNumber}
                </h3>
                <button
                  onClick={() => setSelectedOrder(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Customer Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Customer Information</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Name</p>
                      <p className="font-medium">{selectedOrder.customer.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium">{selectedOrder.customer.email}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Order Items</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-3">
                    {selectedOrder.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{item.productName}</p>
                          <p className="text-sm text-gray-600">SKU: {item.sku}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{item.quantity} × {formatCurrency(item.price)}</p>
                          <p className="text-sm text-gray-600">{formatCurrency(item.total)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Pricing Summary */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Pricing Summary</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>{formatCurrency(selectedOrder.pricing.subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax</span>
                      <span>{formatCurrency(selectedOrder.pricing.tax)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Shipping</span>
                      <span>{formatCurrency(selectedOrder.pricing.shipping)}</span>
                    </div>
                    {selectedOrder.pricing.discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount</span>
                        <span>-{formatCurrency(selectedOrder.pricing.discount)}</span>
                      </div>
                    )}
                    <div className="border-t pt-2 flex justify-between font-bold">
                      <span>Total</span>
                      <span>{formatCurrency(selectedOrder.pricing.total)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Shipping Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Shipping Information</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Method</p>
                      <p className="font-medium capitalize">{selectedOrder.shipping.method}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Estimated Delivery</p>
                      <p className="font-medium">{formatDateTime(selectedOrder.estimatedDelivery)}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">Address</p>
                    <p className="font-medium">
                      {selectedOrder.shipping.address.street}<br />
                      {selectedOrder.shipping.address.city}, {selectedOrder.shipping.address.state} {selectedOrder.shipping.address.zipCode}<br />
                      {selectedOrder.shipping.address.country}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RealTimeOrderDashboard;
