require('dotenv').config();
const { User, Role } = require('../server/models');
const { connectDB } = require('../server/config/database');

/**
 * Setup script for Role-Based Access Control System
 * Ensures roles are properly configured and admin users exist
 */

async function setupRBAC() {
  console.log('🔧 Setting up RBAC System for Nirvana Organics...\n');

  try {
    // Connect to database
    await connectDB();
    console.log('✅ Database connected successfully\n');

    // Step 1: Ensure roles exist with correct permissions
    console.log('📋 Step 1: Setting up roles and permissions...');
    await setupRoles();

    // Step 2: Check for admin users
    console.log('\n👑 Step 2: Checking admin users...');
    await checkAdminUsers();

    // Step 3: Verify RBAC configuration
    console.log('\n🔍 Step 3: Verifying RBAC configuration...');
    await verifyRBACSetup();

    console.log('\n🎉 RBAC setup completed successfully!');
    console.log('\n📖 Next steps:');
    console.log('   1. Run: node scripts/test-rbac-system.js');
    console.log('   2. Test manager invitation system');
    console.log('   3. Verify audit logging is working');
    console.log('   4. Update frontend to use permission-based UI');

  } catch (error) {
    console.error('❌ RBAC setup failed:', error);
    process.exit(1);
  }
}

async function setupRoles() {
  try {
    const roles = ['admin', 'manager', 'customer'];
    
    for (const roleName of roles) {
      let role = await Role.findOne({ where: { name: roleName } });
      
      if (!role) {
        console.log(`   Creating ${roleName} role...`);
        
        const roleData = {
          name: roleName,
          displayName: roleName.charAt(0).toUpperCase() + roleName.slice(1),
          description: getRoleDescription(roleName),
          permissions: Role.getDefaultPermissions(roleName),
          isActive: true
        };
        
        role = await Role.create(roleData);
        console.log(`   ✅ Created ${roleName} role`);
      } else {
        // Update permissions if they've changed
        const defaultPermissions = Role.getDefaultPermissions(roleName);
        if (JSON.stringify(role.permissions) !== JSON.stringify(defaultPermissions)) {
          await role.update({ permissions: defaultPermissions });
          console.log(`   🔄 Updated ${roleName} role permissions`);
        } else {
          console.log(`   ✅ ${roleName} role already exists with correct permissions`);
        }
      }
    }

    console.log('✅ All roles configured successfully');
  } catch (error) {
    console.error('❌ Role setup failed:', error);
    throw error;
  }
}

async function checkAdminUsers() {
  try {
    const adminRole = await Role.getByName('admin');
    if (!adminRole) {
      throw new Error('Admin role not found');
    }

    const adminUsers = await User.findAll({
      where: { roleId: adminRole.id, isActive: true },
      attributes: ['id', 'email', 'firstName', 'lastName', 'createdAt']
    });

    console.log(`   Found ${adminUsers.length} active admin users:`);
    
    if (adminUsers.length === 0) {
      console.log('   ⚠️ No admin users found!');
      console.log('   📝 To create an admin user, run:');
      console.log('      node scripts/create-admin-user.js');
    } else {
      adminUsers.forEach((admin, index) => {
        console.log(`   ${index + 1}. ${admin.firstName} ${admin.lastName} (${admin.email})`);
      });
      console.log('   ✅ Admin users are available');
    }

  } catch (error) {
    console.error('❌ Admin user check failed:', error);
    throw error;
  }
}

async function verifyRBACSetup() {
  try {
    // Check if all required roles exist
    const requiredRoles = ['admin', 'manager', 'customer'];
    const existingRoles = await Role.findAll({
      where: { name: requiredRoles, isActive: true }
    });

    if (existingRoles.length !== requiredRoles.length) {
      throw new Error('Not all required roles are configured');
    }

    // Verify role permissions
    for (const role of existingRoles) {
      const permissions = role.permissions;
      
      // Check if role has required permission categories
      const requiredCategories = ['products', 'users', 'orders', 'analytics', 'system'];
      const hasAllCategories = requiredCategories.every(category => 
        permissions.hasOwnProperty(category)
      );

      if (!hasAllCategories) {
        console.log(`   ⚠️ ${role.name} role missing some permission categories`);
      } else {
        console.log(`   ✅ ${role.name} role permissions verified`);
      }
    }

    // Check user-role associations
    const totalUsers = await User.count({ where: { isActive: true } });
    const usersWithRoles = await User.count({
      where: { isActive: true, roleId: { [require('sequelize').Op.not]: null } }
    });

    console.log(`   📊 Users: ${usersWithRoles}/${totalUsers} have assigned roles`);

    if (usersWithRoles < totalUsers) {
      console.log('   ⚠️ Some users do not have assigned roles');
    }

    console.log('✅ RBAC configuration verified');

  } catch (error) {
    console.error('❌ RBAC verification failed:', error);
    throw error;
  }
}

function getRoleDescription(roleName) {
  const descriptions = {
    admin: 'Full system access with all administrative privileges including user management, product management, and system configuration.',
    manager: 'Operational access with read-only product permissions and full customer service capabilities. Can manage orders and handle customer inquiries.',
    customer: 'Standard user access for shopping and account management. Can view products and manage own orders.'
  };
  
  return descriptions[roleName] || 'Standard user role';
}

// Helper function to create a sample admin user (if needed)
async function createSampleAdmin() {
  try {
    const adminRole = await Role.getByName('admin');
    if (!adminRole) {
      throw new Error('Admin role not found');
    }

    const sampleAdmin = {
      email: '<EMAIL>',
      password: 'Admin123!@#', // This will be hashed by the model
      firstName: 'System',
      lastName: 'Administrator',
      roleId: adminRole.id,
      isVerified: true,
      isActive: true,
      dateOfBirth: new Date('1990-01-01') // Meets 21+ requirement
    };

    const existingAdmin = await User.findOne({ where: { email: sampleAdmin.email } });
    if (existingAdmin) {
      console.log('   ℹ️ Sample admin user already exists');
      return existingAdmin;
    }

    const newAdmin = await User.create(sampleAdmin);
    console.log('   ✅ Sample admin user created');
    console.log(`   📧 Email: ${sampleAdmin.email}`);
    console.log(`   🔑 Password: ${sampleAdmin.password}`);
    console.log('   ⚠️ Please change the password after first login!');

    return newAdmin;

  } catch (error) {
    console.error('❌ Failed to create sample admin:', error);
    throw error;
  }
}

// Run the setup
if (require.main === module) {
  setupRBAC()
    .then(() => {
      console.log('\n🚀 RBAC system is ready!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 RBAC setup failed:', error.message);
      process.exit(1);
    });
}

module.exports = { 
  setupRBAC, 
  setupRoles, 
  checkAdminUsers, 
  verifyRBACSetup,
  createSampleAdmin 
};
