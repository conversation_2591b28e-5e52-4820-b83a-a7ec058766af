import React from 'react';
import { TruckIcon, ClockIcon, MapPinIcon, ShieldCheckIcon, CurrencyDollarIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const Shipping: React.FC = () => {
  const shippingOptions = [
    {
      name: 'Standard Shipping',
      time: '3-5 Business Days',
      cost: '$6.99',
      description: 'Reliable delivery via USPS Ground Advantage',
      icon: TruckIcon
    },
    {
      name: 'Express Shipping',
      time: '1-2 Business Days',
      cost: '$14.99',
      description: 'Fast delivery via USPS Priority Express',
      icon: ClockIcon
    },
    {
      name: 'Free Shipping',
      time: '3-5 Business Days',
      cost: 'FREE',
      description: 'On orders over $100 (Continental US only)',
      icon: CurrencyDollarIcon
    }
  ];

  const shippingZones = [
    {
      zone: 'Continental US',
      states: 'All 48 contiguous states',
      availability: 'Available',
      notes: 'Standard and Express shipping available'
    },
    {
      zone: 'Alaska & Hawaii',
      states: 'AK, HI',
      availability: 'Available',
      notes: 'Extended delivery times may apply'
    },
    {
      zone: 'US Territories',
      states: 'PR, VI, GU, AS, MP',
      availability: 'Contact Us',
      notes: 'Special arrangements required'
    },
    {
      zone: 'International',
      states: 'Outside US',
      availability: 'Not Available',
      notes: 'We currently only ship within the US'
    }
  ];

  const restrictedStates = [
    'Idaho (ID)', 'Iowa (IA)', 'South Dakota (SD)'
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Shipping Information
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              Fast, secure, and discreet shipping nationwide. Free shipping on orders over $100.
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Shipping Options */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Shipping Options</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {shippingOptions.map((option, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <div className="w-16 h-16 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <option.icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{option.name}</h3>
                  <p className="text-2xl font-bold text-primary-600 mb-2">{option.cost}</p>
                  <p className="text-gray-600 font-medium mb-3">{option.time}</p>
                  <p className="text-gray-600 text-sm">{option.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Processing Time */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <ClockIcon className="h-6 w-6 text-primary-600 mr-3" />
              Processing Time
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Standard Processing</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Orders placed before 2 PM EST ship same day</li>
                  <li>• Orders placed after 2 PM EST ship next business day</li>
                  <li>• Processing time: 1-2 business days</li>
                  <li>• No processing on weekends or holidays</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Express Processing</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Available for Express shipping orders</li>
                  <li>• Same-day processing for orders before 12 PM EST</li>
                  <li>• Priority handling and packaging</li>
                  <li>• Weekend processing available (additional fee)</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Shipping Zones */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <MapPinIcon className="h-6 w-6 text-primary-600 mr-3" />
              Shipping Zones
            </h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Zone</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Coverage</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Availability</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {shippingZones.map((zone, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-3 px-4 font-medium text-gray-900">{zone.zone}</td>
                      <td className="py-3 px-4 text-gray-600">{zone.states}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          zone.availability === 'Available' 
                            ? 'bg-green-100 text-green-800'
                            : zone.availability === 'Contact Us'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {zone.availability}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600 text-sm">{zone.notes}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Restricted States */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-red-900 mb-4 flex items-center">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
              Shipping Restrictions
            </h2>
            <p className="text-red-800 mb-4">
              Due to varying state laws regarding hemp-derived products, we cannot ship to the following states:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              {restrictedStates.map((state, index) => (
                <div key={index} className="bg-red-100 text-red-800 px-3 py-2 rounded text-center font-medium">
                  {state}
                </div>
              ))}
            </div>
            <p className="text-red-800 text-sm mt-4">
              <strong>Note:</strong> State laws are subject to change. Please verify the legality of hemp-derived 
              products in your state before ordering. We reserve the right to cancel orders to restricted areas.
            </p>
          </div>

          {/* Packaging & Security */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <ShieldCheckIcon className="h-6 w-6 text-primary-600 mr-3" />
                Discreet Packaging
              </h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Plain, unmarked packaging with no product references
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Secure, tamper-evident sealing
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Odor-proof and moisture-resistant materials
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Professional appearance and handling
                </li>
              </ul>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <TruckIcon className="h-6 w-6 text-primary-600 mr-3" />
                Tracking & Delivery
              </h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Real-time tracking information provided
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Email notifications for shipping updates
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Signature required for orders over $200
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Insurance included on all shipments
                </li>
              </ul>
            </div>
          </div>

          {/* Shipping FAQ */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">What if my package is lost or damaged?</h4>
                <p className="text-gray-600 text-sm">
                  All shipments are insured. If your package is lost or damaged during transit, 
                  contact us immediately. We'll work with the carrier to resolve the issue and 
                  ensure you receive your order.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Can I change my shipping address after ordering?</h4>
                <p className="text-gray-600 text-sm">
                  Address changes are possible if your order hasn't been processed yet. Contact 
                  us <NAME_EMAIL> or call +1 (*************.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Do you ship to PO Boxes?</h4>
                <p className="text-gray-600 text-sm">
                  Yes, we ship to PO Boxes via USPS. However, Express shipping may not be 
                  available for PO Box addresses depending on your location.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">What happens if I'm not home for delivery?</h4>
                <p className="text-gray-600 text-sm">
                  For standard deliveries, USPS will leave a notice and attempt redelivery. 
                  For signature-required packages, you'll need to arrange pickup or redelivery 
                  through USPS.
                </p>
              </div>
            </div>
          </div>

          {/* Contact for Shipping Questions */}
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-8 text-center">
            <h3 className="text-xl font-bold text-primary-900 mb-4">Have Shipping Questions?</h3>
            <p className="text-primary-800 mb-6">
              Our customer service team is here to help with any shipping-related questions or concerns.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="btn-primary px-6 py-3"
              >
                Contact Support
              </a>
              <a
                href="tel:+15551234567"
                className="btn-secondary px-6 py-3"
              >
                Call: (*************
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Shipping;
