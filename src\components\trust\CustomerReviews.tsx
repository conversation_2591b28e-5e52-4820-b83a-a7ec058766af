import React, { useState } from 'react';
import { StarIcon, UserCircleIcon, CheckBadgeIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';

interface Review {
  id: string;
  customerName: string;
  customerInitials: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  verified: boolean;
  productName: string;
  helpful: number;
  location: string;
}

interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: { [key: number]: number };
}

const CustomerReviews: React.FC<{ variant?: 'full' | 'compact' | 'testimonials' }> = ({ 
  variant = 'full' 
}) => {
  const [selectedRating, setSelectedRating] = useState<number | null>(null);

  const reviewStats: ReviewStats = {
    averageRating: 4.8,
    totalReviews: 2847,
    ratingDistribution: {
      5: 2156,
      4: 512,
      3: 134,
      2: 32,
      1: 13
    }
  };

  const featuredReviews: Review[] = [
    {
      id: '1',
      customerName: '<PERSON>',
      customerInitials: 'SM',
      rating: 5,
      title: 'Excellent Quality and Fast Shipping',
      content: 'I\'ve been ordering from Nirvana Organics for over a year now, and they consistently deliver high-quality products. The CBD flowers are potent and fresh, and the lab reports give me confidence in what I\'m purchasing. Shipping is always fast and discreet.',
      date: '2024-01-15',
      verified: true,
      productName: 'Premium CBD Flower - Sour Space Candy',
      helpful: 23,
      location: 'California'
    },
    {
      id: '2',
      customerName: 'Mike R.',
      customerInitials: 'MR',
      rating: 5,
      title: 'Best THC-A Products I\'ve Found',
      content: 'The THC-A flowers are incredible. Great effects, amazing taste, and you can tell they\'re grown with care. Customer service is also top-notch - they answered all my questions about legality and shipping. Will definitely order again.',
      date: '2024-01-12',
      verified: true,
      productName: 'THC-A Flower - Purple Haze',
      helpful: 18,
      location: 'Texas'
    },
    {
      id: '3',
      customerName: 'Jennifer L.',
      customerInitials: 'JL',
      rating: 4,
      title: 'Great Products, Helpful Support',
      content: 'Love the variety of products available. The chocolates are delicious and effective. Had a question about dosing and their support team was very helpful and knowledgeable. Only minor complaint is that shipping took a day longer than expected.',
      date: '2024-01-10',
      verified: true,
      productName: 'Delta-9 Chocolate Squares',
      helpful: 15,
      location: 'Florida'
    },
    {
      id: '4',
      customerName: 'David K.',
      customerInitials: 'DK',
      rating: 5,
      title: 'Trustworthy and Reliable',
      content: 'I appreciate the transparency with lab testing and the detailed product information. Everything arrives exactly as described, and the quality is consistent. The pre-rolls are perfectly rolled and burn evenly. Highly recommend!',
      date: '2024-01-08',
      verified: true,
      productName: 'CBD Pre-Rolls - Lifter',
      helpful: 21,
      location: 'Colorado'
    },
    {
      id: '5',
      customerName: 'Amanda T.',
      customerInitials: 'AT',
      rating: 5,
      title: 'Outstanding Customer Experience',
      content: 'From browsing the website to receiving my order, everything was smooth and professional. The packaging is discreet and secure, and the products exceeded my expectations. The diamond sauce is particularly impressive - very clean and potent.',
      date: '2024-01-05',
      verified: true,
      productName: 'THC-A Diamond Sauce',
      helpful: 19,
      location: 'New York'
    },
    {
      id: '6',
      customerName: 'Robert H.',
      customerInitials: 'RH',
      rating: 4,
      title: 'Quality Products, Fair Prices',
      content: 'Been shopping around for quality hemp products and Nirvana Organics offers the best value. Products are lab-tested, prices are reasonable, and customer service is responsive. The vapes have great flavor and effects.',
      date: '2024-01-03',
      verified: true,
      productName: 'Delta-8 Vape Cartridge - Blue Dream',
      helpful: 12,
      location: 'Arizona'
    }
  ];

  const renderStars = (rating: number, size: 'small' | 'medium' | 'large' = 'medium') => {
    const sizeClasses = {
      small: 'h-4 w-4',
      medium: 'h-5 w-5',
      large: 'h-6 w-6'
    };

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (variant === 'compact') {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center mb-4">
          <div className="flex items-center justify-center mb-2">
            {renderStars(reviewStats.averageRating, 'large')}
            <span className="ml-2 text-2xl font-bold text-gray-900">
              {reviewStats.averageRating}
            </span>
          </div>
          <p className="text-gray-600">
            Based on {reviewStats.totalReviews.toLocaleString()} verified reviews
          </p>
        </div>
        
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center">
              <span className="text-sm text-gray-600 w-8">{rating}★</span>
              <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full"
                  style={{
                    width: `${(reviewStats.ratingDistribution[rating] / reviewStats.totalReviews) * 100}%`
                  }}
                />
              </div>
              <span className="text-sm text-gray-600 w-12 text-right">
                {reviewStats.ratingDistribution[rating]}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'testimonials') {
    return (
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
            <div className="flex items-center justify-center mb-4">
              {renderStars(reviewStats.averageRating, 'large')}
              <span className="ml-2 text-xl font-bold text-gray-900">
                {reviewStats.averageRating} out of 5
              </span>
            </div>
            <p className="text-gray-600">
              Based on {reviewStats.totalReviews.toLocaleString()} verified customer reviews
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredReviews.slice(0, 6).map((review) => (
              <div key={review.id} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center font-semibold">
                    {review.customerInitials}
                  </div>
                  <div className="ml-3">
                    <div className="flex items-center">
                      <p className="font-semibold text-gray-900">{review.customerName}</p>
                      {review.verified && (
                        <CheckBadgeIcon className="h-4 w-4 text-green-500 ml-1" />
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{review.location}</p>
                  </div>
                </div>

                <div className="mb-3">
                  {renderStars(review.rating, 'small')}
                </div>

                <h4 className="font-semibold text-gray-900 mb-2">{review.title}</h4>
                <p className="text-gray-600 text-sm mb-3 line-clamp-4">{review.content}</p>
                
                <div className="text-xs text-gray-500 border-t pt-3">
                  <p className="font-medium">{review.productName}</p>
                  <p>{new Date(review.date).toLocaleDateString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Customer Reviews & Testimonials
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. See what our satisfied customers have to say 
            about their experience with Nirvana Organics.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Review Stats */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-lg p-8 sticky top-4">
              <div className="text-center mb-6">
                <div className="text-5xl font-bold text-gray-900 mb-2">
                  {reviewStats.averageRating}
                </div>
                <div className="flex items-center justify-center mb-2">
                  {renderStars(reviewStats.averageRating, 'large')}
                </div>
                <p className="text-gray-600">
                  Based on {reviewStats.totalReviews.toLocaleString()} reviews
                </p>
              </div>

              <div className="space-y-3">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center">
                    <button
                      onClick={() => setSelectedRating(selectedRating === rating ? null : rating)}
                      className="flex items-center w-full hover:bg-gray-100 rounded p-2 transition-colors"
                    >
                      <span className="text-sm text-gray-600 w-8">{rating}</span>
                      <StarIcon className="h-4 w-4 text-yellow-400 mr-2" />
                      <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${(reviewStats.ratingDistribution[rating] / reviewStats.totalReviews) * 100}%`
                          }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 w-16 text-right">
                        {reviewStats.ratingDistribution[rating]}
                      </span>
                    </button>
                  </div>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-3">Review Highlights</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <CheckBadgeIcon className="h-4 w-4 text-green-500 mr-2" />
                    <span>98% would recommend</span>
                  </div>
                  <div className="flex items-center">
                    <CheckBadgeIcon className="h-4 w-4 text-green-500 mr-2" />
                    <span>Fast shipping praised</span>
                  </div>
                  <div className="flex items-center">
                    <CheckBadgeIcon className="h-4 w-4 text-green-500 mr-2" />
                    <span>Quality consistently rated 5★</span>
                  </div>
                  <div className="flex items-center">
                    <CheckBadgeIcon className="h-4 w-4 text-green-500 mr-2" />
                    <span>Excellent customer service</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews List */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {featuredReviews
                .filter(review => !selectedRating || review.rating === selectedRating)
                .map((review) => (
                <div key={review.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center font-semibold text-lg">
                        {review.customerInitials}
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center">
                          <h4 className="font-semibold text-gray-900">{review.customerName}</h4>
                          {review.verified && (
                            <CheckBadgeIcon className="h-5 w-5 text-green-500 ml-2" />
                          )}
                        </div>
                        <p className="text-sm text-gray-500">{review.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      {renderStars(review.rating)}
                      <p className="text-sm text-gray-500 mt-1">
                        {new Date(review.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{review.title}</h3>
                  <p className="text-gray-700 mb-4 leading-relaxed">{review.content}</p>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{review.productName}</p>
                      <p className="text-xs text-gray-500">Verified Purchase</p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {review.helpful} people found this helpful
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More Button */}
            <div className="text-center mt-12">
              <button className="btn-secondary px-8 py-3">
                Load More Reviews
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerReviews;
