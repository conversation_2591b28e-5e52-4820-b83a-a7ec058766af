import React, { useEffect, useState } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { verifyEmail, clearError } from '../store/slices/authSlice';
import { Helmet } from 'react-helmet-async';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ArrowPathIcon,
  EnvelopeIcon 
} from '@heroicons/react/24/outline';

const VerifyEmail: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { loading, error } = useAppSelector((state) => state.auth);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [errorMessage, setErrorMessage] = useState('');

  const token = searchParams.get('token');

  useEffect(() => {
    const verifyEmailToken = async () => {
      if (!token) {
        setVerificationStatus('error');
        setErrorMessage('Invalid or missing verification token.');
        return;
      }

      try {
        dispatch(clearError());
        await dispatch(verifyEmail(token)).unwrap();
        setVerificationStatus('success');
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigate('/login', { 
            state: { message: 'Email verified successfully! You can now log in.' }
          });
        }, 3000);
      } catch (error: any) {
        setVerificationStatus('error');
        setErrorMessage(error || 'Failed to verify email. The link may have expired.');
      }
    };

    verifyEmailToken();
  }, [token, dispatch, navigate]);

  const handleRetryVerification = () => {
    if (token) {
      setVerificationStatus('pending');
      setErrorMessage('');
      dispatch(verifyEmail(token));
    }
  };

  const renderContent = () => {
    switch (verificationStatus) {
      case 'pending':
        return (
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
              <ArrowPathIcon className="h-8 w-8 text-blue-600 animate-spin" />
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Verifying your email...
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please wait while we verify your email address.
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Email Verified Successfully!
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Your email address has been verified. You will be redirected to the login page shortly.
            </p>
            
            <div className="mt-8 bg-white py-6 px-6 shadow rounded-lg">
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  <p className="mb-2">
                    🎉 Welcome to Nirvana Organics! Your account is now fully activated.
                  </p>
                  <p>
                    You can now access all features including:
                  </p>
                  <ul className="mt-2 list-disc list-inside text-left space-y-1">
                    <li>Browse our premium organic products</li>
                    <li>Add items to your cart and wishlist</li>
                    <li>Track your orders</li>
                    <li>Manage your profile and addresses</li>
                  </ul>
                </div>

                <div className="pt-4">
                  <Link
                    to="/login"
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    Continue to Login
                  </Link>
                </div>
              </div>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center">
              <XCircleIcon className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Email Verification Failed
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              {errorMessage}
            </p>

            <div className="mt-8 bg-white py-6 px-6 shadow rounded-lg">
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  <p className="mb-2">
                    This could happen if:
                  </p>
                  <ul className="text-left list-disc list-inside space-y-1">
                    <li>The verification link has expired (links expire after 24 hours)</li>
                    <li>The link has already been used</li>
                    <li>The link was corrupted or incomplete</li>
                  </ul>
                </div>

                <div className="flex flex-col space-y-3">
                  {token && (
                    <button
                      onClick={handleRetryVerification}
                      disabled={loading}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? (
                        <>
                          <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                          Retrying...
                        </>
                      ) : (
                        'Try Again'
                      )}
                    </button>
                  )}

                  <Link
                    to="/register"
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    Request New Verification Email
                  </Link>

                  <Link
                    to="/login"
                    className="text-sm font-medium text-green-600 hover:text-green-500"
                  >
                    Back to Login
                  </Link>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Helmet>
        <title>
          {verificationStatus === 'success' 
            ? 'Email Verified - Nirvana Organics'
            : verificationStatus === 'error'
            ? 'Verification Failed - Nirvana Organics'
            : 'Verifying Email - Nirvana Organics'
          }
        </title>
        <meta 
          name="description" 
          content={
            verificationStatus === 'success'
              ? 'Your email has been successfully verified.'
              : 'Email verification page for Nirvana Organics account.'
          }
        />
      </Helmet>
      
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <Link to="/" className="flex justify-center">
              <img
                className="h-12 w-auto"
                src="/Nirvana_logo.png"
                alt="Nirvana Organics"
              />
            </Link>
          </div>

          {renderContent()}

          {/* Additional error display */}
          {error && verificationStatus === 'error' && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default VerifyEmail;
