import React, { useState, useEffect } from 'react';
import { 
  ExclamationTriangleIcon, 
  ClockIcon, 
  ArrowPathIcon,
  XMarkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface ProductUpdateNotificationProps {
  isVisible: boolean;
  onDismiss?: () => void;
  type?: 'updating' | 'maintenance' | 'slow-connection' | 'error';
  message?: string;
  estimatedTime?: string;
  showRefreshButton?: boolean;
  onRefresh?: () => void;
}

const ProductUpdateNotification: React.FC<ProductUpdateNotificationProps> = ({
  isVisible,
  onDismiss,
  type = 'updating',
  message,
  estimatedTime = '10-15 minutes',
  showRefreshButton = true,
  onRefresh
}) => {
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [isMinimized, setIsMinimized] = useState(false);

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getNotificationConfig = () => {
    switch (type) {
      case 'updating':
        return {
          icon: ClockIcon,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          iconColor: 'text-blue-600',
          title: 'Products Updating',
          defaultMessage: `Products are being updated by administrators. Please wait ${estimatedTime} or refresh the page.`,
          actionText: 'Refresh Page'
        };
      case 'maintenance':
        return {
          icon: ExclamationTriangleIcon,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          iconColor: 'text-yellow-600',
          title: 'Maintenance Mode',
          defaultMessage: 'The product catalog is currently under maintenance. Some products may not be available.',
          actionText: 'Try Again'
        };
      case 'slow-connection':
        return {
          icon: ArrowPathIcon,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          iconColor: 'text-orange-600',
          title: 'Slow Connection Detected',
          defaultMessage: 'Products are loading slowly due to network conditions. Please be patient.',
          actionText: 'Retry'
        };
      case 'error':
        return {
          icon: ExclamationTriangleIcon,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconColor: 'text-red-600',
          title: 'Loading Error',
          defaultMessage: 'There was an error loading products. Please try refreshing the page.',
          actionText: 'Refresh'
        };
      default:
        return {
          icon: InformationCircleIcon,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          iconColor: 'text-gray-600',
          title: 'Loading',
          defaultMessage: 'Loading products...',
          actionText: 'Refresh'
        };
    }
  };

  if (!isVisible) return null;

  const config = getNotificationConfig();
  const IconComponent = config.icon;
  const displayMessage = message || config.defaultMessage;

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsMinimized(false)}
          className={`${config.bgColor} ${config.borderColor} border rounded-full p-3 shadow-lg hover:shadow-xl transition-shadow`}
        >
          <IconComponent className={`h-6 w-6 ${config.iconColor} animate-pulse`} />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
      <div className={`${config.bgColor} ${config.borderColor} border rounded-lg shadow-lg p-4`}>
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <IconComponent className={`h-6 w-6 ${config.iconColor}`} />
          </div>
          
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-gray-900">
              {config.title}
            </h3>
            <p className="mt-1 text-sm text-gray-700">
              {displayMessage}
            </p>
            
            {type === 'updating' && (
              <div className="mt-2 text-xs text-gray-600">
                <div className="flex items-center space-x-2">
                  <span>Time elapsed: {formatTime(timeElapsed)}</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-1">
                    <div 
                      className="bg-blue-600 h-1 rounded-full transition-all duration-1000"
                      style={{ 
                        width: `${Math.min((timeElapsed / (15 * 60)) * 100, 100)}%` 
                      }}
                    />
                  </div>
                </div>
              </div>
            )}
            
            <div className="mt-3 flex items-center space-x-2">
              {showRefreshButton && (
                <button
                  onClick={onRefresh}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <ArrowPathIcon className="h-3 w-3 mr-1" />
                  {config.actionText}
                </button>
              )}
              
              <button
                onClick={() => setIsMinimized(true)}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Minimize
              </button>
            </div>
          </div>
          
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={onDismiss}
              className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Global notification manager
class ProductUpdateNotificationManager {
  private static instance: ProductUpdateNotificationManager;
  private notifications: Map<string, boolean> = new Map();
  private listeners: Set<(notifications: Map<string, boolean>) => void> = new Set();

  static getInstance(): ProductUpdateNotificationManager {
    if (!ProductUpdateNotificationManager.instance) {
      ProductUpdateNotificationManager.instance = new ProductUpdateNotificationManager();
    }
    return ProductUpdateNotificationManager.instance;
  }

  show(id: string): void {
    this.notifications.set(id, true);
    this.notifyListeners();
  }

  hide(id: string): void {
    this.notifications.set(id, false);
    this.notifyListeners();
  }

  isVisible(id: string): boolean {
    return this.notifications.get(id) || false;
  }

  subscribe(listener: (notifications: Map<string, boolean>) => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(new Map(this.notifications)));
  }

  // Convenience methods
  showProductUpdate(): void {
    this.show('product-update');
  }

  hideProductUpdate(): void {
    this.hide('product-update');
  }

  showMaintenance(): void {
    this.show('maintenance');
  }

  hideMaintenance(): void {
    this.hide('maintenance');
  }

  showSlowConnection(): void {
    this.show('slow-connection');
  }

  hideSlowConnection(): void {
    this.hide('slow-connection');
  }

  showError(): void {
    this.show('error');
  }

  hideError(): void {
    this.hide('error');
  }
}

// Hook for using the notification manager
export const useProductUpdateNotification = () => {
  const [notifications, setNotifications] = useState<Map<string, boolean>>(new Map());
  const manager = ProductUpdateNotificationManager.getInstance();

  useEffect(() => {
    const unsubscribe = manager.subscribe(setNotifications);
    return unsubscribe;
  }, [manager]);

  return {
    show: (id: string) => manager.show(id),
    hide: (id: string) => manager.hide(id),
    isVisible: (id: string) => manager.isVisible(id),
    showProductUpdate: () => manager.showProductUpdate(),
    hideProductUpdate: () => manager.hideProductUpdate(),
    showMaintenance: () => manager.showMaintenance(),
    hideMaintenance: () => manager.hideMaintenance(),
    showSlowConnection: () => manager.showSlowConnection(),
    hideSlowConnection: () => manager.hideSlowConnection(),
    showError: () => manager.showError(),
    hideError: () => manager.hideError(),
  };
};

export default ProductUpdateNotification;
export { ProductUpdateNotificationManager };
