import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchCart } from '../store/slices/cartSlice';
import { addToast } from '../store/slices/uiSlice';
import {
  CreditCardIcon,
  TruckIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/common/LoadingSpinner';
import SEOHead from '../components/seo/SEOHead';
import Breadcrumb from '../components/common/Breadcrumb';
import GuestCheckoutOption from '../components/checkout/GuestCheckoutOption';
import { guestOrderService, GuestOrderData } from '../services/guestOrderService';

interface CheckoutFormData {
  // Contact Information
  email: string;
  phone: string;

  // Shipping Address
  shippingFirstName: string;
  shippingLastName: string;
  shippingAddress1: string;
  shippingAddress2: string;
  shippingCity: string;
  shippingState: string;
  shippingZip: string;

  // Billing Address
  billingFirstName: string;
  billingLastName: string;
  billingAddress1: string;
  billingAddress2: string;
  billingCity: string;
  billingState: string;
  billingZip: string;
  billingIsSame: boolean;

  // Payment
  paymentMethod: 'square' | 'paypal';

  // Enhanced customer data
  gender: 'he' | 'she' | 'they' | '';
  shippingMethod: 'regular' | 'express';

  // Additional
  orderNotes: string;
  ageVerification: boolean;
  termsAccepted: boolean;
}

const Checkout: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { cart, loading: cartLoading } = useAppSelector((state) => state.cart);
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);

  const [isGuestCheckout, setIsGuestCheckout] = useState(false);
  const [showCheckoutOptions, setShowCheckoutOptions] = useState(!isAuthenticated);

  const [formData, setFormData] = useState<CheckoutFormData>({
    email: '',
    phone: '',
    shippingFirstName: '',
    shippingLastName: '',
    shippingAddress1: '',
    shippingAddress2: '',
    shippingCity: '',
    shippingState: '',
    shippingZip: '',
    billingFirstName: '',
    billingLastName: '',
    billingAddress1: '',
    billingAddress2: '',
    billingCity: '',
    billingState: '',
    billingZip: '',
    billingIsSame: true,
    paymentMethod: 'square',
    gender: '',
    shippingMethod: 'regular',
    orderNotes: '',
    ageVerification: false,
    termsAccepted: false
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [shippingCost, setShippingCost] = useState(0);
  const [shippingDescription, setShippingDescription] = useState('Standard Shipping');

  // Guest checkout handlers
  const handleGuestCheckout = () => {
    setIsGuestCheckout(true);
    setShowCheckoutOptions(false);
  };

  const handleLoginRedirect = () => {
    navigate('/login?redirect=/checkout');
  };

  const handleRegisterRedirect = () => {
    navigate('/register?redirect=/checkout');
  };

  useEffect(() => {
    // Always fetch cart data, regardless of authentication status
    dispatch(fetchCart());
  }, [dispatch]);

  // Pre-fill form data for authenticated users
  useEffect(() => {
    if (isAuthenticated && user) {
      setFormData(prev => ({
        ...prev,
        email: user.email || '',
        shippingFirstName: user.firstName || '',
        shippingLastName: user.lastName || '',
        // Add other user data if available
      }));
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    if (!cartLoading && (!cart || !cart.items || cart.items.length === 0)) {
      dispatch(addToast({
        type: 'error',
        title: 'Empty Cart',
        message: 'Your cart is empty. Add some items before checkout.'
      }));
      navigate('/shop');
    }
  }, [cart, cartLoading, navigate, dispatch]);

  // Calculate initial shipping cost
  useEffect(() => {
    if (cart?.total) {
      const shipping = calculateShippingCost(formData.shippingMethod, cart.total);
      setShippingCost(shipping.cost);
      setShippingDescription(shipping.description);
    }
  }, [cart?.total, formData.shippingMethod]);

  // Calculate shipping cost based on method and order total
  const calculateShippingCost = (method: string, orderTotal: number) => {
    const membershipType = user?.membershipType || 'first-time';

    switch (method) {
      case 'regular':
        if (orderTotal >= 100) {
          return { cost: 0, description: 'Free Standard Shipping (5-7 business days)' };
        }
        return { cost: 9.99, description: 'Standard Shipping (5-7 business days)' };

      case 'express':
        let cost = 19.99;
        if (membershipType === 'premium') {
          cost = 14.99; // $5 discount for premium members
        }
        return { cost, description: `Express Shipping (2-3 business days)${membershipType === 'premium' ? ' - Premium Discount Applied' : ''}` };

      default:
        return { cost: 9.99, description: 'Standard Shipping (5-7 business days)' };
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Update shipping cost when method changes
    if (name === 'shippingMethod') {
      const orderTotal = cart?.total || 0;
      const shipping = calculateShippingCost(value, orderTotal);
      setShippingCost(shipping.cost);
      setShippingDescription(shipping.description);
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Auto-fill billing address if same as shipping
    if (name.startsWith('shipping') && formData.billingIsSame) {
      const billingField = name.replace('shipping', 'billing');
      setFormData(prev => ({
        ...prev,
        [billingField]: value
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      // Contact Information
      if (!formData.email) newErrors.email = 'Email is required';
      if (!formData.phone) newErrors.phone = 'Phone number is required';

      // Shipping Address
      if (!formData.shippingFirstName) newErrors.shippingFirstName = 'First name is required';
      if (!formData.shippingLastName) newErrors.shippingLastName = 'Last name is required';
      if (!formData.shippingAddress1) newErrors.shippingAddress1 = 'Address is required';
      if (!formData.shippingCity) newErrors.shippingCity = 'City is required';
      if (!formData.shippingState) newErrors.shippingState = 'State is required';
      if (!formData.shippingZip) newErrors.shippingZip = 'ZIP code is required';

      // Billing Address (if different)
      if (!formData.billingIsSame) {
        if (!formData.billingFirstName) newErrors.billingFirstName = 'First name is required';
        if (!formData.billingLastName) newErrors.billingLastName = 'Last name is required';
        if (!formData.billingAddress1) newErrors.billingAddress1 = 'Address is required';
        if (!formData.billingCity) newErrors.billingCity = 'City is required';
        if (!formData.billingState) newErrors.billingState = 'State is required';
        if (!formData.billingZip) newErrors.billingZip = 'ZIP code is required';
      }
    }

    if (step === 2) {
      // Payment validation would go here
      if (!formData.paymentMethod) newErrors.paymentMethod = 'Payment method is required';
    }

    if (step === 3) {
      // Final validation
      if (!formData.ageVerification) newErrors.ageVerification = 'Age verification is required';
      if (!formData.termsAccepted) newErrors.termsAccepted = 'You must accept the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmitOrder = async () => {
    if (!validateStep(3)) return;

    setIsProcessing(true);

    try {
      if (isGuestCheckout) {
        // Process guest order
        const guestOrderData: GuestOrderData = {
          email: formData.email,
          phone: formData.phone,
          shippingAddress: {
            firstName: formData.shippingFirstName,
            lastName: formData.shippingLastName,
            address1: formData.shippingAddress1,
            address2: formData.shippingAddress2,
            city: formData.shippingCity,
            state: formData.shippingState,
            zipCode: formData.shippingZip,
          },
          billingAddress: {
            firstName: formData.billingFirstName,
            lastName: formData.billingLastName,
            address1: formData.billingAddress1,
            address2: formData.billingAddress2,
            city: formData.billingCity,
            state: formData.billingState,
            zipCode: formData.billingZip,
          },
          items: cart.items.map(item => ({
            productId: item.product.id,
            variantId: item.variant?.id,
            quantity: item.quantity,
            price: item.variant?.price || item.product.price,
          })),
          paymentMethod: formData.paymentMethod,
          orderNotes: formData.orderNotes,
          ageVerification: formData.ageVerification,
          termsAccepted: formData.termsAccepted,
          // Enhanced customer data
          customerGender: formData.gender || null,
          shippingMethod: formData.shippingMethod,
          shippingCost: shippingCost,
          // Calculate totals
          subtotal: cart.subtotal,
          tax: cart.tax,
          shipping: shippingCost,
          total: cart.subtotal + shippingCost + cart.tax - (cart.discount || 0),
        };

        const response = await guestOrderService.createGuestOrder(guestOrderData);
        const { order, trackingToken } = response.data.data;

        // Store tracking info for easy access
        guestOrderService.storeGuestOrderTracking(
          order.orderNumber,
          order.email,
          trackingToken
        );

        dispatch(addToast({
          type: 'success',
          title: 'Order Placed Successfully!',
          message: `Your order #${order.orderNumber} has been placed. Check your email for confirmation and tracking information.`
        }));

        // Redirect to order tracking page
        navigate(`/track-order?token=${trackingToken}`);
      } else {
        // Process authenticated user order (existing logic)
        // TODO: Implement authenticated user order processing
        await new Promise(resolve => setTimeout(resolve, 2000));

        dispatch(addToast({
          type: 'success',
          title: 'Order Placed!',
          message: 'Your order has been successfully placed. You will receive a confirmation email shortly.'
        }));

        navigate('/orders');
      }
    } catch (error: any) {
      dispatch(addToast({
        type: 'error',
        title: 'Order Failed',
        message: error.response?.data?.message || 'There was an error processing your order. Please try again.'
      }));
    } finally {
      setIsProcessing(false);
    }
  };

  const steps = [
    { number: 1, title: 'Shipping & Billing', icon: TruckIcon },
    { number: 2, title: 'Payment', icon: CreditCardIcon },
    { number: 3, title: 'Review & Place Order', icon: CheckCircleIcon }
  ];

  const usStates = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
  ];

  if (cartLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead
        title="Secure Checkout - Complete Your Cannabis Order"
        description="Complete your secure checkout for premium cannabis products. SSL encrypted payment processing with multiple payment options. Fast and discreet shipping."
        keywords={['cannabis checkout', 'secure payment', 'hemp order', 'cannabis purchase', 'secure checkout']}
        canonicalUrl="/checkout"
        noIndex={true}
      />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Shopping Cart', href: '/cart' },
            { label: 'Checkout', current: true }
          ]}
          className="mb-6"
        />

        {/* Guest Checkout Options */}
        {showCheckoutOptions && (
          <GuestCheckoutOption
            onGuestCheckout={handleGuestCheckout}
            onLoginRedirect={handleLoginRedirect}
            onRegisterRedirect={handleRegisterRedirect}
          />
        )}

        {/* Checkout Form - Only show when user has chosen an option */}
        {(isAuthenticated || isGuestCheckout) && (
          <>
            {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Secure Checkout</h1>
          <p className="text-gray-600">Complete your order in just a few simple steps</p>
        </div>

        {/* Progress Steps */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.number
                    ? 'bg-primary-600 border-primary-600 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.number ? (
                    <CheckCircleIcon className="h-6 w-6" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.number ? 'text-primary-600' : 'text-gray-400'
                  }`}>
                    Step {step.number}
                  </p>
                  <p className={`text-xs ${
                    currentStep >= step.number ? 'text-gray-900' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${
                    currentStep > step.number ? 'bg-primary-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-6">
              {/* Step 1: Shipping & Billing */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Shipping & Billing Information</h2>

                  {/* Contact Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Contact Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                            errors.email ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number *
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                            errors.phone ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="(*************"
                        />
                        {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                      </div>
                    </div>
                  </div>

                  {/* Customer Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Additional Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Preferred Pronouns
                        </label>
                        <select
                          name="gender"
                          value={formData.gender}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                          <option value="">Select pronouns (optional)</option>
                          <option value="he">He/Him</option>
                          <option value="she">She/Her</option>
                          <option value="they">They/Them</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Shipping Method *
                        </label>
                        <select
                          name="shippingMethod"
                          value={formData.shippingMethod}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        >
                          <option value="regular">Standard Shipping (5-7 business days)</option>
                          <option value="express">Express Shipping (2-3 business days)</option>
                        </select>
                        <p className="text-sm text-gray-600 mt-1">{shippingDescription}</p>
                      </div>
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Shipping Address</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            First Name *
                          </label>
                          <input
                            type="text"
                            name="shippingFirstName"
                            value={formData.shippingFirstName}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                              errors.shippingFirstName ? 'border-red-500' : 'border-gray-300'
                            }`}
                          />
                          {errors.shippingFirstName && <p className="text-red-500 text-xs mt-1">{errors.shippingFirstName}</p>}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Last Name *
                          </label>
                          <input
                            type="text"
                            name="shippingLastName"
                            value={formData.shippingLastName}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                              errors.shippingLastName ? 'border-red-500' : 'border-gray-300'
                            }`}
                          />
                          {errors.shippingLastName && <p className="text-red-500 text-xs mt-1">{errors.shippingLastName}</p>}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Address Line 1 *
                        </label>
                        <input
                          type="text"
                          name="shippingAddress1"
                          value={formData.shippingAddress1}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                            errors.shippingAddress1 ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="Street address"
                        />
                        {errors.shippingAddress1 && <p className="text-red-500 text-xs mt-1">{errors.shippingAddress1}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Address Line 2
                        </label>
                        <input
                          type="text"
                          name="shippingAddress2"
                          value={formData.shippingAddress2}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                          placeholder="Apartment, suite, etc. (optional)"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            City *
                          </label>
                          <input
                            type="text"
                            name="shippingCity"
                            value={formData.shippingCity}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                              errors.shippingCity ? 'border-red-500' : 'border-gray-300'
                            }`}
                          />
                          {errors.shippingCity && <p className="text-red-500 text-xs mt-1">{errors.shippingCity}</p>}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            State *
                          </label>
                          <select
                            name="shippingState"
                            value={formData.shippingState}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                              errors.shippingState ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select State</option>
                            {usStates.map(state => (
                              <option key={state} value={state}>{state}</option>
                            ))}
                          </select>
                          {errors.shippingState && <p className="text-red-500 text-xs mt-1">{errors.shippingState}</p>}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            ZIP Code *
                          </label>
                          <input
                            type="text"
                            name="shippingZip"
                            value={formData.shippingZip}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                              errors.shippingZip ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="12345"
                          />
                          {errors.shippingZip && <p className="text-red-500 text-xs mt-1">{errors.shippingZip}</p>}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Billing Address */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900">Billing Address</h3>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="billingIsSame"
                          checked={formData.billingIsSame}
                          onChange={handleInputChange}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">Same as shipping address</span>
                      </label>
                    </div>

                    {!formData.billingIsSame && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              First Name *
                            </label>
                            <input
                              type="text"
                              name="billingFirstName"
                              value={formData.billingFirstName}
                              onChange={handleInputChange}
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                                errors.billingFirstName ? 'border-red-500' : 'border-gray-300'
                              }`}
                            />
                            {errors.billingFirstName && <p className="text-red-500 text-xs mt-1">{errors.billingFirstName}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Last Name *
                            </label>
                            <input
                              type="text"
                              name="billingLastName"
                              value={formData.billingLastName}
                              onChange={handleInputChange}
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                                errors.billingLastName ? 'border-red-500' : 'border-gray-300'
                              }`}
                            />
                            {errors.billingLastName && <p className="text-red-500 text-xs mt-1">{errors.billingLastName}</p>}
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Address Line 1 *
                          </label>
                          <input
                            type="text"
                            name="billingAddress1"
                            value={formData.billingAddress1}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                              errors.billingAddress1 ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="Street address"
                          />
                          {errors.billingAddress1 && <p className="text-red-500 text-xs mt-1">{errors.billingAddress1}</p>}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Address Line 2
                          </label>
                          <input
                            type="text"
                            name="billingAddress2"
                            value={formData.billingAddress2}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            placeholder="Apartment, suite, etc. (optional)"
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              City *
                            </label>
                            <input
                              type="text"
                              name="billingCity"
                              value={formData.billingCity}
                              onChange={handleInputChange}
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                                errors.billingCity ? 'border-red-500' : 'border-gray-300'
                              }`}
                            />
                            {errors.billingCity && <p className="text-red-500 text-xs mt-1">{errors.billingCity}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              State *
                            </label>
                            <select
                              name="billingState"
                              value={formData.billingState}
                              onChange={handleInputChange}
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                                errors.billingState ? 'border-red-500' : 'border-gray-300'
                              }`}
                            >
                              <option value="">Select State</option>
                              {usStates.map(state => (
                                <option key={state} value={state}>{state}</option>
                              ))}
                            </select>
                            {errors.billingState && <p className="text-red-500 text-xs mt-1">{errors.billingState}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              ZIP Code *
                            </label>
                            <input
                              type="text"
                              name="billingZip"
                              value={formData.billingZip}
                              onChange={handleInputChange}
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                                errors.billingZip ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="12345"
                            />
                            {errors.billingZip && <p className="text-red-500 text-xs mt-1">{errors.billingZip}</p>}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Step 2: Payment */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Payment Method</h2>

                  <div className="space-y-4">
                    {/* Square Payment */}
                    <div className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                      formData.paymentMethod === 'square'
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="square"
                          checked={formData.paymentMethod === 'square'}
                          onChange={handleInputChange}
                          className="text-primary-600 focus:ring-primary-500"
                        />
                        <div className="ml-3 flex items-center">
                          <CreditCardIcon className="h-6 w-6 text-gray-600 mr-2" />
                          <div>
                            <p className="font-medium text-gray-900">Credit/Debit Card</p>
                            <p className="text-sm text-gray-600">Secure payment via Square</p>
                          </div>
                        </div>
                      </label>
                    </div>

                    {/* PayPal Payment */}
                    <div className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                      formData.paymentMethod === 'paypal'
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="paypal"
                          checked={formData.paymentMethod === 'paypal'}
                          onChange={handleInputChange}
                          className="text-primary-600 focus:ring-primary-500"
                        />
                        <div className="ml-3 flex items-center">
                          <div className="h-6 w-6 bg-blue-600 rounded mr-2 flex items-center justify-center">
                            <span className="text-white text-xs font-bold">PP</span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">PayPal</p>
                            <p className="text-sm text-gray-600">Pay with your PayPal account</p>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Security Notice */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <ShieldCheckIcon className="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-green-900">Secure Payment</h4>
                        <p className="text-sm text-green-800 mt-1">
                          Your payment information is encrypted and secure. We never store your credit card details.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Order Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Order Notes (Optional)
                    </label>
                    <textarea
                      name="orderNotes"
                      value={formData.orderNotes}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="Any special instructions for your order..."
                    />
                  </div>
                </div>
              )}

              {/* Step 3: Review & Place Order */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Review Your Order</h2>

                  {/* Order Summary */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-900 mb-3">Order Items</h3>
                    <div className="space-y-3">
                      {cart.items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{item.product?.name || 'Product'}</p>
                            <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                          </div>
                          <p className="font-medium text-gray-900">
                            ${((item.product?.price || 0) * item.quantity).toFixed(2)}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Shipping & Billing Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-2">Shipping Address</h3>
                      <div className="text-sm text-gray-600">
                        <p>{formData.shippingFirstName} {formData.shippingLastName}</p>
                        <p>{formData.shippingAddress1}</p>
                        {formData.shippingAddress2 && <p>{formData.shippingAddress2}</p>}
                        <p>{formData.shippingCity}, {formData.shippingState} {formData.shippingZip}</p>
                      </div>
                    </div>

                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-2">Payment Method</h3>
                      <div className="text-sm text-gray-600">
                        <p className="capitalize">{formData.paymentMethod}</p>
                        {formData.paymentMethod === 'square' && <p>Credit/Debit Card</p>}
                        {formData.paymentMethod === 'paypal' && <p>PayPal Account</p>}
                      </div>
                    </div>
                  </div>

                  {/* Age Verification & Terms */}
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <input
                        type="checkbox"
                        name="ageVerification"
                        checked={formData.ageVerification}
                        onChange={handleInputChange}
                        className={`mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500 ${
                          errors.ageVerification ? 'border-red-500' : ''
                        }`}
                      />
                      <div className="ml-3">
                        <label className="text-sm text-gray-700">
                          <span className="font-medium">Age Verification *</span>
                          <br />
                          I certify that I am 21 years of age or older and legally allowed to purchase these products in my jurisdiction.
                        </label>
                        {errors.ageVerification && <p className="text-red-500 text-xs mt-1">{errors.ageVerification}</p>}
                      </div>
                    </div>

                    <div className="flex items-start">
                      <input
                        type="checkbox"
                        name="termsAccepted"
                        checked={formData.termsAccepted}
                        onChange={handleInputChange}
                        className={`mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500 ${
                          errors.termsAccepted ? 'border-red-500' : ''
                        }`}
                      />
                      <div className="ml-3">
                        <label className="text-sm text-gray-700">
                          <span className="font-medium">Terms & Conditions *</span>
                          <br />
                          I agree to the <a href="/terms" className="text-primary-600 hover:underline" target="_blank">Terms of Service</a> and <a href="/privacy" className="text-primary-600 hover:underline" target="_blank">Privacy Policy</a>.
                        </label>
                        {errors.termsAccepted && <p className="text-red-500 text-xs mt-1">{errors.termsAccepted}</p>}
                      </div>
                    </div>
                  </div>

                  {/* Legal Disclaimer */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-900">Important Legal Notice</h4>
                        <p className="text-sm text-yellow-800 mt-1">
                          These statements have not been evaluated by the FDA. Products are not intended to diagnose, treat, cure, or prevent any disease. Please ensure hemp-derived products are legal in your jurisdiction before ordering.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6 border-t border-gray-200">
                <button
                  onClick={handlePrevStep}
                  disabled={currentStep === 1}
                  className="btn-secondary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {currentStep < 3 ? (
                  <button
                    onClick={handleNextStep}
                    className="btn-primary px-6 py-2"
                  >
                    Continue
                  </button>
                ) : (
                  <button
                    onClick={handleSubmitOrder}
                    disabled={isProcessing}
                    className="btn-primary px-8 py-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isProcessing ? (
                      <>
                        <LoadingSpinner size="small" className="mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <LockClosedIcon className="h-5 w-5 mr-2" />
                        Place Order
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-4">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Order Summary</h3>

              <div className="space-y-3 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="text-gray-900">${cart.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="text-gray-900">
                    {shippingCost === 0 ? 'FREE' : `$${shippingCost.toFixed(2)}`}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax</span>
                  <span className="text-gray-900">${cart.tax.toFixed(2)}</span>
                </div>
                {cart.discount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Discount</span>
                    <span className="text-green-600">-${cart.discount.toFixed(2)}</span>
                  </div>
                )}
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="text-lg font-bold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-gray-900">${(cart.subtotal + shippingCost + cart.tax - (cart.discount || 0)).toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Security Badges */}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                  <div className="flex items-center">
                    <LockClosedIcon className="h-4 w-4 mr-1" />
                    <span>SSL Secure</span>
                  </div>
                  <div className="flex items-center">
                    <ShieldCheckIcon className="h-4 w-4 mr-1" />
                    <span>PCI Compliant</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </>
        )}
      </div>
    </div>
  );
};

export default Checkout;
