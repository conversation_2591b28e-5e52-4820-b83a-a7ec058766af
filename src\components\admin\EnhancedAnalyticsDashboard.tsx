import React, { useState, useEffect, useRef } from 'react';
import {
  ChartBarIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ShoppingBagIcon,
  TrendingUpIcon,
  ClockIcon,
  BellIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import LoadingSpinner from '../common/LoadingSpinner';
import io, { Socket } from 'socket.io-client';

interface AnalyticsData {
  revenue: {
    totalRevenue: number;
    averageOrderValue: number;
    revenueByDay: Array<{ date: string; revenue: number; orders: number }>;
    growth: number;
  };
  orders: {
    totalOrders: number;
    ordersByStatus: Array<{ status: string; count: number }>;
    ordersByShipping: Array<{ shippingMethod: string; count: number; revenue: number }>;
    growth: number;
  };
  customers: {
    totalCustomers: number;
    customersByMembership: Array<{ membershipType: string; count: number }>;
    customersByTrafficSource: Array<{ trafficSource: string; count: number }>;
    customersByAuthProvider: Array<{ authProvider: string; count: number }>;
    growth: number;
  };
  products: {
    topProducts: Array<{ productName: string; totalSold: number; totalRevenue: number }>;
    categoryPerformance: Array<{ categoryName: string; totalSold: number; totalRevenue: number }>;
  };
  traffic: {
    trafficSources: Array<{ trafficSource: string; visitors: number; conversions: number; conversionRate: string }>;
  };
  marketing: {
    couponUsage: Array<{ couponCode: string; usageCount: number; totalDiscount: number }>;
    referralStats: Array<{ status: string; count: number }>;
  };
}

interface RealTimeData {
  todayOrders: number;
  todayRevenue: number;
  activeUsers: number;
  pendingOrders: number;
  timestamp: string;
}

interface LiveOrder {
  id: number;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
    membershipType: string;
  };
  itemCount: number;
  total: number;
  status: string;
  paymentStatus: string;
  createdAt: string;
  priority: 'normal' | 'high';
}

interface RealTimeMetrics {
  today: {
    orders: number;
    revenue: number;
    averageOrderValue: number;
  };
  week: {
    orders: number;
    revenue: number;
  };
  month: {
    orders: number;
    revenue: number;
  };
  activeCustomers: number;
  pendingOrders: number;
  timestamp: string;
}

const EnhancedAnalyticsDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const socketRef = useRef<Socket | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [realTimeData, setRealTimeData] = useState<RealTimeData | null>(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [liveOrders, setLiveOrders] = useState<LiveOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [isRealTimeConnected, setIsRealTimeConnected] = useState(false);
  const [showLiveOrderFeed, setShowLiveOrderFeed] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    fetchAnalyticsData();
    fetchRealTimeData();
    initializeRealTimeConnection();

    // Set up real-time data refresh
    const interval = setInterval(fetchRealTimeData, 30000); // Every 30 seconds

    return () => {
      clearInterval(interval);
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [dateRange]);

  const initializeRealTimeConnection = () => {
    const token = localStorage.getItem('adminToken');

    socketRef.current = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    const socket = socketRef.current;

    socket.on('connect', () => {
      console.log('Connected to real-time analytics server');
      setIsRealTimeConnected(true);

      // Authenticate as admin
      socket.emit('authenticate', {
        token,
        userRole: 'admin'
      });
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from real-time analytics server');
      setIsRealTimeConnected(false);
    });

    socket.on('initial-orders', (orders: LiveOrder[]) => {
      setLiveOrders(orders);
    });

    socket.on('initial-metrics', (metrics: RealTimeMetrics) => {
      setRealTimeMetrics(metrics);
    });

    socket.on('new-order', (newOrder: LiveOrder) => {
      setLiveOrders(prevOrders => [newOrder, ...prevOrders.slice(0, 19)]); // Keep last 20 orders

      // Update real-time metrics
      if (realTimeMetrics) {
        setRealTimeMetrics(prev => prev ? {
          ...prev,
          today: {
            ...prev.today,
            orders: prev.today.orders + 1,
            revenue: prev.today.revenue + newOrder.total
          }
        } : null);
      }

      // Show notification
      dispatch(addToast({
        type: 'success',
        message: `New order: ${newOrder.orderNumber} - $${newOrder.total.toFixed(2)}`
      }));
    });

    socket.on('metrics-update', (updatedMetrics: RealTimeMetrics) => {
      setRealTimeMetrics(updatedMetrics);
    });

    socket.on('order-status-update', (updateData: any) => {
      setLiveOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === updateData.orderId
            ? { ...order, status: updateData.newStatus }
            : order
        )
      );
    });

    socket.on('connect_error', (error) => {
      console.error('Real-time connection error:', error);
      setIsRealTimeConnected(false);
    });
  };

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      // Mock API call - replace with actual API
      const mockData: AnalyticsData = {
        revenue: {
          totalRevenue: 45678.90,
          averageOrderValue: 89.50,
          revenueByDay: [
            { date: '2024-01-01', revenue: 1200, orders: 15 },
            { date: '2024-01-02', revenue: 1450, orders: 18 },
            { date: '2024-01-03', revenue: 980, orders: 12 }
          ],
          growth: 12.5
        },
        orders: {
          totalOrders: 1234,
          ordersByStatus: [
            { status: 'completed', count: 890 },
            { status: 'pending', count: 234 },
            { status: 'cancelled', count: 110 }
          ],
          ordersByShipping: [
            { shippingMethod: 'regular', count: 800, revenue: 32000 },
            { shippingMethod: 'express', count: 434, revenue: 21700 }
          ],
          growth: 8.3
        },
        customers: {
          totalCustomers: 2890,
          customersByMembership: [
            { membershipType: 'regular', count: 1500 },
            { membershipType: 'premium', count: 890 },
            { membershipType: 'first-time', count: 500 }
          ],
          customersByTrafficSource: [
            { trafficSource: 'organic', count: 1200 },
            { trafficSource: 'direct', count: 800 },
            { trafficSource: 'social-media', count: 500 },
            { trafficSource: 'referral', count: 390 }
          ],
          customersByAuthProvider: [
            { authProvider: 'local', count: 1800 },
            { authProvider: 'google', count: 700 },
            { authProvider: 'facebook', count: 390 }
          ],
          growth: 15.2
        },
        products: {
          topProducts: [
            { productName: 'Organic Hemp Oil', totalSold: 234, totalRevenue: 4680 },
            { productName: 'CBD Gummies', totalSold: 189, totalRevenue: 3780 },
            { productName: 'Wellness Tea', totalSold: 156, totalRevenue: 2340 }
          ],
          categoryPerformance: [
            { categoryName: 'CBD Products', totalSold: 500, totalRevenue: 15000 },
            { categoryName: 'Wellness', totalSold: 300, totalRevenue: 9000 },
            { categoryName: 'Supplements', totalSold: 200, totalRevenue: 6000 }
          ]
        },
        traffic: {
          trafficSources: [
            { trafficSource: 'organic', visitors: 1200, conversions: 180, conversionRate: '15.0' },
            { trafficSource: 'direct', visitors: 800, conversions: 120, conversionRate: '15.0' },
            { trafficSource: 'social-media', visitors: 500, conversions: 50, conversionRate: '10.0' }
          ]
        },
        marketing: {
          couponUsage: [
            { couponCode: 'WELCOME10', usageCount: 234, totalDiscount: 1170 },
            { couponCode: 'SAVE20', usageCount: 156, totalDiscount: 2340 }
          ],
          referralStats: [
            { status: 'completed', count: 89 },
            { status: 'pending', count: 45 }
          ]
        }
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      dispatch(addToast({
        type: 'error',
        message: 'Failed to fetch analytics data'
      }));
    } finally {
      setLoading(false);
    }
  };

  const fetchRealTimeData = async () => {
    try {
      // Mock API call - replace with actual API
      const mockRealTimeData: RealTimeData = {
        todayOrders: 23,
        todayRevenue: 1456.78,
        activeUsers: 45,
        pendingOrders: 12,
        timestamp: new Date().toISOString()
      };

      setRealTimeData(mockRealTimeData);
    } catch (error) {
      console.error('Error fetching real-time data:', error);
    }
  };

  const handleExport = async (format: 'excel' | 'csv') => {
    try {
      setExporting(true);
      
      // Mock export - replace with actual API call
      const filename = `analytics-${dateRange.startDate}-to-${dateRange.endDate}`;
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      dispatch(addToast({
        type: 'success',
        message: `Analytics data exported to ${format.toUpperCase()} successfully`
      }));
      
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        message: `Failed to export to ${format.toUpperCase()}`
      }));
    } finally {
      setExporting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Analytics Dashboard</h2>
          <p className="text-gray-600">Comprehensive insights and data export capabilities</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isRealTimeConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-600">
              {isRealTimeConnected ? 'Live' : 'Offline'}
            </span>
          </div>
          <button
            onClick={() => setShowLiveOrderFeed(!showLiveOrderFeed)}
            className={`flex items-center px-3 py-2 rounded-md text-sm ${
              showLiveOrderFeed
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <BellIcon className="h-4 w-4 mr-2" />
            Live Orders
          </button>
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-gray-400" />
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
            <span className="text-gray-500">to</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleExport('excel')}
              disabled={exporting}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {exporting ? (
                <LoadingSpinner size="small" className="mr-2" />
              ) : (
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              )}
              Export Excel
            </button>
            <button
              onClick={() => handleExport('csv')}
              disabled={exporting}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {exporting ? (
                <LoadingSpinner size="small" className="mr-2" />
              ) : (
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              )}
              Export CSV
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Real-time Stats */}
      {realTimeMetrics && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Real-time Overview</h3>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center text-sm ${isRealTimeConnected ? 'text-green-600' : 'text-red-600'}`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${isRealTimeConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
                {isRealTimeConnected ? 'Live Updates' : 'Offline'}
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <ClockIcon className="h-4 w-4 mr-1" />
                {new Date(realTimeMetrics.timestamp).toLocaleTimeString()}
              </div>
            </div>
          </div>

          {/* Today's Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-2">
                <ShoppingBagIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.today.orders}</div>
              <div className="text-sm text-gray-600">Today's Orders</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-2">
                <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(realTimeMetrics.today.revenue)}</div>
              <div className="text-sm text-gray-600">Today's Revenue</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-2">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.activeCustomers}</div>
              <div className="text-sm text-gray-600">Active Customers</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full mx-auto mb-2">
                <ClockIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.pendingOrders}</div>
              <div className="text-sm text-gray-600">Pending Orders</div>
            </div>
          </div>

          {/* Period Comparison */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{formatCurrency(realTimeMetrics.today.averageOrderValue)}</div>
              <div className="text-sm text-gray-600">Avg Order Value (Today)</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{realTimeMetrics.week.orders}</div>
              <div className="text-sm text-gray-600">Week Orders</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{formatCurrency(realTimeMetrics.month.revenue)}</div>
              <div className="text-sm text-gray-600">Month Revenue</div>
            </div>
          </div>
        </div>
      )}

      {/* Live Order Feed */}
      {showLiveOrderFeed && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Live Order Feed</h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">Live ({liveOrders.length})</span>
              </div>
            </div>
          </div>
          <div className="divide-y divide-gray-200 max-h-80 overflow-y-auto">
            {liveOrders.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <ShoppingBagIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No recent orders. Waiting for new orders...</p>
              </div>
            ) : (
              liveOrders.map((order) => (
                <div key={order.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            #{order.orderNumber}
                          </p>
                          <p className="text-sm text-gray-600">
                            {order.customer.name}
                          </p>
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.customer.membershipType === 'premium'
                            ? 'bg-purple-100 text-purple-800'
                            : order.customer.membershipType === 'regular'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {order.customer.membershipType}
                        </span>
                      </div>
                      <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                        <span>{order.itemCount} items</span>
                        <span>•</span>
                        <span>{formatCurrency(order.total)}</span>
                        <span>•</span>
                        <span>{new Date(order.createdAt).toLocaleTimeString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                        order.status === 'completed' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status}
                      </span>
                      {order.priority === 'high' && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                          High Priority
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Main Analytics Grid */}
      {analyticsData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Analytics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Revenue Analytics</h3>
              <div className={`flex items-center text-sm ${
                analyticsData.revenue.growth > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUpIcon className="h-4 w-4 mr-1" />
                {formatPercentage(analyticsData.revenue.growth)}
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Revenue</span>
                <span className="font-semibold">{formatCurrency(analyticsData.revenue.totalRevenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Average Order Value</span>
                <span className="font-semibold">{formatCurrency(analyticsData.revenue.averageOrderValue)}</span>
              </div>
            </div>
          </div>

          {/* Customer Analytics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Customer Analytics</h3>
              <div className={`flex items-center text-sm ${
                analyticsData.customers.growth > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUpIcon className="h-4 w-4 mr-1" />
                {formatPercentage(analyticsData.customers.growth)}
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Customers</span>
                <span className="font-semibold">{analyticsData.customers.totalCustomers.toLocaleString()}</span>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-700">By Membership</div>
                {analyticsData.customers.customersByMembership.map((item, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-gray-600 capitalize">{item.membershipType}</span>
                    <span>{item.count.toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Top Products */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Products</h3>
            <div className="space-y-3">
              {analyticsData.products.topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{product.productName}</div>
                    <div className="text-sm text-gray-600">{product.totalSold} sold</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{formatCurrency(product.totalRevenue)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Traffic Sources */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Traffic Sources</h3>
            <div className="space-y-3">
              {analyticsData.traffic.trafficSources.map((source, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 capitalize">{source.trafficSource}</div>
                    <div className="text-sm text-gray-600">{source.visitors} visitors</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{source.conversions} conversions</div>
                    <div className="text-sm text-gray-600">{source.conversionRate}% rate</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAnalyticsDashboard;
