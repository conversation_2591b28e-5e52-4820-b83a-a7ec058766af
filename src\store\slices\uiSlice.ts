import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Toast, Modal } from '../../types';

interface UIState {
  toasts: Toast[];
  modal: Modal;
  loading: {
    global: boolean;
    [key: string]: boolean;
  };
  sidebar: {
    isOpen: boolean;
    type?: string;
  };
}

const initialState: UIState = {
  toasts: [],
  modal: {
    isOpen: false,
  },
  loading: {
    global: false,
  },
  sidebar: {
    isOpen: false,
  },
};

// UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Toast actions
    addToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {
      const toast: Toast = {
        ...action.payload,
        id: Date.now().toString(),
        duration: action.payload.duration || 5000,
      };
      state.toasts.push(toast);
    },
    removeToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },
    clearToasts: (state) => {
      state.toasts = [];
    },

    // Modal actions
    openModal: (state, action: PayloadAction<{ type?: string; data?: any }>) => {
      state.modal = {
        isOpen: true,
        type: action.payload.type,
        data: action.payload.data,
      };
    },
    closeModal: (state) => {
      state.modal = {
        isOpen: false,
      };
    },

    // Loading actions
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading;
    },

    // Sidebar actions
    openSidebar: (state, action: PayloadAction<{ type?: string }>) => {
      state.sidebar = {
        isOpen: true,
        type: action.payload.type,
      };
    },
    closeSidebar: (state) => {
      state.sidebar = {
        isOpen: false,
      };
    },
    toggleSidebar: (state) => {
      state.sidebar.isOpen = !state.sidebar.isOpen;
    },
  },
});

export const {
  addToast,
  removeToast,
  clearToasts,
  openModal,
  closeModal,
  setGlobalLoading,
  setLoading,
  openSidebar,
  closeSidebar,
  toggleSidebar,
} = uiSlice.actions;

export default uiSlice.reducer;
