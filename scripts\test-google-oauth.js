#!/usr/bin/env node

/**
 * Test script for Google OAuth authentication endpoint
 * This script tests the Google OAuth login endpoint to ensure it's working properly
 */

require('dotenv').config();
const axios = require('axios');

const BASE_URL = process.env.BACKEND_URL || 'http://localhost:5000';
const GOOGLE_LOGIN_URL = `${BASE_URL}/api/auth/google/login`;

async function testGoogleOAuthEndpoint() {
  console.log('🔍 Testing Google OAuth Authentication Endpoint...\n');
  
  console.log(`📍 Testing URL: ${GOOGLE_LOGIN_URL}`);
  console.log(`🌐 Backend URL: ${BASE_URL}`);
  console.log(`🔑 Google Client ID: ${process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not Set'}`);
  console.log(`🔐 Google Client Secret: ${process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not Set'}\n`);

  try {
    // Test 1: Check if server is running
    console.log('1️⃣ Testing server connectivity...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`, {
      timeout: 5000
    });
    console.log('✅ Server is running and accessible');
    console.log(`   Status: ${healthResponse.status}`);
    console.log(`   Response: ${JSON.stringify(healthResponse.data)}\n`);
  } catch (error) {
    console.log('❌ Server connectivity test failed');
    console.log(`   Error: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log('   💡 Make sure the backend server is running on port 5000');
      console.log('   💡 Continuing with Google OAuth test anyway...\n');
    } else {
      console.log('');
    }
  }

  try {
    // Test 2: Test Google OAuth login endpoint
    console.log('2️⃣ Testing Google OAuth login endpoint...');
    const response = await axios.get(GOOGLE_LOGIN_URL, {
      timeout: 10000,
      maxRedirects: 0, // Don't follow redirects
      validateStatus: function (status) {
        // Accept any status code (including redirects)
        return status >= 200 && status < 400;
      }
    });
    
    console.log('✅ Google OAuth endpoint is accessible');
    console.log(`   Status: ${response.status}`);
    console.log(`   Headers: ${JSON.stringify(response.headers, null, 2)}`);
    
    if (response.status === 302) {
      console.log('✅ Endpoint correctly redirects to Google OAuth');
      console.log(`   Redirect Location: ${response.headers.location}`);
      
      // Check if the redirect URL contains Google OAuth
      if (response.headers.location && response.headers.location.includes('accounts.google.com')) {
        console.log('✅ Redirect URL points to Google OAuth service');
      } else {
        console.log('⚠️  Redirect URL does not point to Google OAuth service');
      }
    }
    
  } catch (error) {
    console.log('❌ Google OAuth endpoint test failed');
    console.log(`   Error: ${error.message}`);
    
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Status Text: ${error.response.statusText}`);
      console.log(`   Response Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   💡 Make sure the backend server is running');
    } else if (error.response && error.response.status === 404) {
      console.log('   💡 The Google OAuth route might not be properly configured');
    } else if (error.response && error.response.status === 500) {
      console.log('   💡 There might be an issue with the Passport.js configuration');
    }
  }

  try {
    // Test 3: Test other auth endpoints for comparison
    console.log('\n3️⃣ Testing other auth endpoints for comparison...');
    
    // Test regular login endpoint
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'testpassword'
    }, {
      timeout: 5000,
      validateStatus: function (status) {
        return status >= 200 && status < 500; // Accept any status
      }
    });
    
    console.log('✅ Regular login endpoint is accessible');
    console.log(`   Status: ${loginResponse.status}`);
    
  } catch (error) {
    console.log('❌ Regular login endpoint test failed');
    console.log(`   Error: ${error.message}`);
  }

  console.log('\n📋 Test Summary:');
  console.log('   - Check server logs for any error messages');
  console.log('   - Verify Google OAuth credentials in .env file');
  console.log('   - Ensure Passport.js is properly configured');
  console.log('   - Check CORS settings for frontend domain');
}

// Run the test
if (require.main === module) {
  testGoogleOAuthEndpoint()
    .then(() => {
      console.log('\n🏁 Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed with error:', error.message);
      process.exit(1);
    });
}

module.exports = { testGoogleOAuthEndpoint };
