const models = require('../../server/models');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * Test Helpers for Nirvana Organics E-commerce Platform
 * Provides utility functions for testing all system components
 */

class TestHelpers {
  /**
   * Generate a test user with specified properties
   */
  static async generateTestUser(overrides = {}) {
    // Create or find the customer role
    let customerRole = await models.Role.findOne({ where: { name: 'customer' } });
    if (!customerRole) {
      customerRole = await models.Role.create({
        name: 'customer',
        displayName: 'Customer',
        description: 'Regular customer user',
        permissions: {},
        priority: 10
      });
    }

    const defaultUser = {
      firstName: 'Test',
      lastName: 'User',
      email: `test.user.${Date.now()}@example.com`,
      password: await bcrypt.hash('TestPassword123!', 10),
      roleId: customerRole.id,
      isEmailVerified: true,
      membershipType: 'regular',
      trafficSource: 'direct',
      isActive: true,
      ...overrides
    };

    try {
      const user = await models.User.create(defaultUser);
      return user;
    } catch (error) {
      console.error('Error creating test user:', error);
      throw error;
    }
  }

  /**
   * Generate a test admin user
   */
  static async generateTestAdmin(overrides = {}) {
    // Create or find the admin role
    let adminRole = await models.Role.findOne({ where: { name: 'admin' } });
    if (!adminRole) {
      adminRole = await models.Role.create({
        name: 'admin',
        displayName: 'Administrator',
        description: 'System administrator with full access',
        permissions: { all: true },
        priority: 100,
        isSystemRole: true
      });
    }

    return this.generateTestUser({
      roleId: adminRole.id,
      firstName: 'Admin',
      lastName: 'User',
      email: `admin.${Date.now()}@example.com`,
      ...overrides
    });
  }

  /**
   * Generate a test category
   */
  static async generateTestCategory(overrides = {}) {
    const defaultCategory = {
      name: `Test Category ${Date.now()}`,
      slug: `test-category-${Date.now()}`,
      description: 'A test category for automated testing',
      isActive: true,
      ...overrides
    };

    try {
      const category = await models.Category.create(defaultCategory);
      return category;
    } catch (error) {
      console.error('Error creating test category:', error);
      throw error;
    }
  }

  /**
   * Generate a test product
   */
  static async generateTestProduct(overrides = {}) {
    // Create a test category if categoryId is not provided
    let categoryId = overrides.categoryId;
    if (!categoryId) {
      const testCategory = await this.generateTestCategory();
      categoryId = testCategory.id;
    }

    const productName = `Test Product ${Date.now()}`;
    const defaultProduct = {
      name: productName,
      slug: productName.toLowerCase().replace(/\s+/g, '-'),
      description: 'A test product for automated testing',
      price: 29.99,
      sku: `TEST-${Date.now()}`,
      categoryId: categoryId,
      quantity: 100,
      trackQuantity: true,
      isActive: true,
      images: [
        {
          url: '/images/test-product.jpg',
          alt: 'Test Product Image',
          isPrimary: true
        }
      ],
      specifications: {
        weight: '100g',
        dimensions: '10x5x5cm',
        ingredients: 'Test ingredients'
      },
      seoTitle: 'Test Product - Nirvana Organics',
      seoDescription: 'Test product for automated testing',
      tags: ['test', 'organic'],
      ...overrides
    };

    try {
      const product = await models.Product.create(defaultProduct);
      return product;
    } catch (error) {
      console.error('Error creating test product:', error);
      throw error;
    }
  }

  /**
   * Generate a test order
   */
  static async generateTestOrder(overrides = {}) {
    const user = overrides.userId ? 
      await models.User.findByPk(overrides.userId) : 
      await this.generateTestUser();

    const product = overrides.productId ? 
      await models.Product.findByPk(overrides.productId) : 
      await this.generateTestProduct();

    const defaultOrder = {
      userId: user.id,
      email: user.email,
      orderNumber: `ORD-${Date.now()}`,
      status: 'pending',
      paymentStatus: 'pending',
      paymentMethod: 'credit_card',
      subtotal: 29.99,
      tax: 2.40,
      shipping: 9.99,
      discount: 0,
      total: 42.38,
      currency: 'USD',
      billingAddress: '123 Test St',
      billingCity: 'Test City',
      billingState: 'TS',
      billingZipCode: '12345',
      billingCountry: 'USA',
      shippingAddress: '123 Test St',
      shippingCity: 'Test City',
      shippingState: 'TS',
      shippingZipCode: '12345',
      shippingCountry: 'USA',
      shippingMethod: 'regular',
      customerMembershipType: user.membershipType || 'regular',
      customerLifetimeValue: 42.38,
      isFirstOrder: true,
      trafficSource: 'direct',
      ...overrides
    };

    try {
      const order = await models.Order.create(defaultOrder);

      // Create order items
      await models.OrderItem.create({
        orderId: order.id,
        productId: product.id,
        name: product.name,
        sku: product.sku,
        productSnapshot: {
          name: product.name,
          sku: product.sku,
          image: product.images[0]?.url
        },
        quantity: 1,
        price: product.price,
        total: product.price
      });

      return order;
    } catch (error) {
      console.error('Error creating test order:', error);
      throw error;
    }
  }

  /**
   * Generate a test cart for a user
   */
  static async generateTestCart(userId, productIds = []) {
    try {
      const cart = await models.Cart.create({
        userId,
        items: []
      });

      if (productIds.length > 0) {
        const items = [];
        for (const productId of productIds) {
          const product = await models.Product.findByPk(productId);
          if (product) {
            items.push({
              productId: product.id,
              quantity: 1,
              price: product.price,
              variant: null
            });
          }
        }
        cart.items = items;
        await cart.save();
      }

      return cart;
    } catch (error) {
      console.error('Error creating test cart:', error);
      throw error;
    }
  }

  /**
   * Generate a JWT token for testing
   */
  static generateTestToken(user, expiresIn = '1h') {
    return jwt.sign(
      { 
        id: user.id, 
        email: user.email, 
        role: user.role 
      },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn }
    );
  }

  /**
   * Generate test coupon
   */
  static async generateTestCoupon(overrides = {}) {
    const defaultCoupon = {
      code: `TEST${Date.now()}`,
      name: 'Test Coupon',
      description: 'Test coupon for automated testing',
      type: 'percentage',
      value: 10,
      minimumOrderAmount: 0,
      maxUses: 100,
      usedCount: 0,
      isActive: true,
      validFrom: new Date(),
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      ...overrides
    };

    try {
      const coupon = await models.Coupon.create(defaultCoupon);
      return coupon;
    } catch (error) {
      console.error('Error creating test coupon:', error);
      throw error;
    }
  }

  /**
   * Generate test email log entry
   */
  static async generateTestEmailLog(overrides = {}) {
    const defaultEmailLog = {
      orderId: null,
      emailType: 'order-confirmation',
      recipient: '<EMAIL>',
      subject: 'Test Email',
      status: 'sent',
      messageId: `test-${Date.now()}`,
      attempts: 1,
      sentAt: new Date(),
      ...overrides
    };

    try {
      const emailLog = await models.EmailLog.create(defaultEmailLog);
      return emailLog;
    } catch (error) {
      console.error('Error creating test email log:', error);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  static async cleanupTestData() {
    try {
      // Delete in order to respect foreign key constraints
      // Use try-catch for each table in case it doesn't exist yet
      const tablesToClean = [
        'OrderItem', 'Order', 'Cart', 'CouponUsage', 'Coupon',
        'EmailLog', 'Product', 'User'
      ];

      for (const tableName of tablesToClean) {
        try {
          if (models[tableName]) {
            await models[tableName].destroy({ where: {}, force: true });
          }
        } catch (error) {
          // Ignore table doesn't exist errors
          if (!error.message.includes("doesn't exist")) {
            console.warn(`Warning cleaning ${tableName}:`, error.message);
          }
        }
      }

      console.log('Test data cleanup completed');
    } catch (error) {
      console.error('Error cleaning up test data:', error);
      throw error;
    }
  }

  /**
   * Wait for a specified amount of time
   */
  static async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate random test data
   */
  static generateRandomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Generate random email
   */
  static generateRandomEmail() {
    return `test.${this.generateRandomString(8)}@example.com`;
  }

  /**
   * Generate random price
   */
  static generateRandomPrice(min = 10, max = 100) {
    return Math.round((Math.random() * (max - min) + min) * 100) / 100;
  }

  /**
   * Mock API response
   */
  static mockApiResponse(data, status = 200) {
    return {
      status,
      data: {
        success: status < 400,
        data,
        message: status < 400 ? 'Success' : 'Error'
      }
    };
  }

  /**
   * Mock WebSocket connection
   */
  static mockWebSocketConnection() {
    const events = {};
    
    return {
      on: (event, callback) => {
        if (!events[event]) events[event] = [];
        events[event].push(callback);
      },
      emit: (event, data) => {
        if (events[event]) {
          events[event].forEach(callback => callback(data));
        }
      },
      connected: true,
      close: () => {
        this.connected = false;
      }
    };
  }

  /**
   * Validate email format
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate order structure
   */
  static validateOrderStructure(order) {
    const requiredFields = [
      'id', 'orderNumber', 'userId', 'status', 'paymentStatus',
      'subtotal', 'tax', 'shipping', 'total', 'createdAt'
    ];

    for (const field of requiredFields) {
      if (!(field in order)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    if (typeof order.total !== 'number' || order.total <= 0) {
      throw new Error('Invalid order total');
    }

    return true;
  }

  /**
   * Validate product structure
   */
  static validateProductStructure(product) {
    const requiredFields = [
      'id', 'name', 'price', 'sku', 'category', 'isActive'
    ];

    for (const field of requiredFields) {
      if (!(field in product)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    const price = typeof product.price === 'string' ? parseFloat(product.price) : product.price;
    if (typeof price !== 'number' || price <= 0 || isNaN(price)) {
      throw new Error('Invalid product price');
    }

    return true;
  }

  /**
   * Validate user structure
   */
  static validateUserStructure(user) {
    const requiredFields = [
      'id', 'firstName', 'lastName', 'email', 'role'
    ];

    for (const field of requiredFields) {
      if (!(field in user)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    if (!this.isValidEmail(user.email)) {
      throw new Error('Invalid email format');
    }

    return true;
  }

  /**
   * Create test database transaction
   */
  static async createTestTransaction() {
    try {
      const transaction = await models.sequelize.transaction();
      return transaction;
    } catch (error) {
      console.error('Error creating test transaction:', error);
      throw error;
    }
  }

  /**
   * Setup test environment
   */
  static async setupTestEnvironment() {
    try {
      // Ensure database connection
      await models.sequelize.authenticate();

      // Ensure database tables are created (without altering existing structure)
      await models.sequelize.sync({ force: false });
      console.log('✅ Database tables synchronized');

      // Clean up any existing test data
      await this.cleanupTestData();
      
      console.log('Test environment setup completed');
    } catch (error) {
      console.error('Error setting up test environment:', error);
      throw error;
    }
  }

  /**
   * Teardown test environment
   */
  static async teardownTestEnvironment() {
    try {
      await this.cleanupTestData();
      console.log('Test environment teardown completed');
    } catch (error) {
      console.error('Error tearing down test environment:', error);
      throw error;
    }
  }
}

module.exports = TestHelpers;
