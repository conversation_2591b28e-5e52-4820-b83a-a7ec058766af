import React, { useEffect } from 'react';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';

import { useAppDispatch } from '../../hooks/redux';
import { addToast } from '../../store/slices/uiSlice';
import { authService } from '../../services/authService';
import { useNavigate, useSearchParams } from 'react-router-dom';

interface SocialLoginProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

const SocialLogin: React.FC<SocialLoginProps> = ({ onSuccess, onError }) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Handle OAuth callback from Passport
  useEffect(() => {
    const token = searchParams.get('token');
    const refreshToken = searchParams.get('refreshToken');
    const provider = searchParams.get('provider');
    const error = searchParams.get('error');

    if (error) {
      const errorMessage = error === 'google_auth_failed'
        ? 'Google authentication failed'
        : 'Social authentication failed';

      dispatch(addToast({
        type: 'error',
        message: errorMessage
      }));
      onError?.(errorMessage);
      return;
    }

    if (token && refreshToken) {
      // Store tokens
      localStorage.setItem('token', token);
      localStorage.setItem('refreshToken', refreshToken);

      dispatch(addToast({
        type: 'success',
        message: `Successfully signed in with ${provider}!`
      }));

      onSuccess?.();
      navigate('/', { replace: true });
    }
  }, [searchParams, dispatch, navigate, onSuccess, onError]);

  // Handle Passport OAuth flow
  const handlePassportGoogleLogin = () => {
    window.location.href = `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/auth/google/login`;
  };



  // Legacy API-based Google login (fallback)
  const handleGoogleSuccess = async (credentialResponse: any) => {
    try {
      const response = await authService.googleLogin(credentialResponse.credential);

      if (response.success) {
        dispatch(addToast({
          type: 'success',
          message: 'Successfully signed in with Google!'
        }));
        onSuccess?.();
      } else {
        throw new Error(response.message || 'Google login failed');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Google login failed';
      dispatch(addToast({
        type: 'error',
        message: errorMessage
      }));
      onError?.(errorMessage);
    }
  };

  const handleGoogleError = () => {
    const errorMessage = 'Google login was cancelled or failed';
    dispatch(addToast({
      type: 'error',
      message: errorMessage
    }));
    onError?.(errorMessage);
  };



  return (
    <div className="space-y-3">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or continue with</span>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {/* Google Login - Using Passport OAuth */}
        <button
          onClick={handlePassportGoogleLogin}
          className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Continue with Google
        </button>


      </div>
    </div>
  );
};

export default SocialLogin;
