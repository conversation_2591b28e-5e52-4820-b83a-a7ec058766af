const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { authenticate, requireAdmin } = require('../middleware/auth');
const { 
  uploadSingleProductImage, 
  uploadMultipleProductImages, 
  handleUploadError 
} = require('../middleware/upload');

// @route   POST /api/upload/product/image
// @desc    Upload single product image
// @access  Private (Admin only)
router.post('/product/image', 
  authenticate, 
  requireAdmin, 
  uploadSingleProductImage,
  handleUploadError,
  uploadController.uploadProductImage
);

// @route   POST /api/upload/product/images
// @desc    Upload multiple product images
// @access  Private (Admin only)
router.post('/product/images', 
  authenticate, 
  requireAdmin, 
  uploadMultipleProductImages,
  handleUploadError,
  uploadController.uploadProductImages
);

// @route   DELETE /api/upload/product/image/:filename
// @desc    Delete product image
// @access  Private (Admin only)
router.delete('/product/image/:filename', 
  authenticate, 
  requireAdmin, 
  uploadController.deleteProductImage
);

module.exports = router;
