#!/usr/bin/env node

/**
 * SSL Setup Verification Script
 * Verifies SSL certificate installation and HTTPS configuration
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}🔧${colors.reset} ${msg}`)
};

/**
 * Test HTTPS connection
 */
function testHTTPSConnection(hostname, port = 443) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname,
      port,
      path: '/health',
      method: 'GET',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data,
          certificate: res.socket.getPeerCertificate()
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Test HTTP to HTTPS redirect
 */
function testHTTPRedirect(hostname, port = 80) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname,
      port,
      path: '/',
      method: 'GET',
      timeout: 10000
    };

    const req = http.request(options, (res) => {
      resolve({
        statusCode: res.statusCode,
        headers: res.headers,
        location: res.headers.location
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Verify SSL certificate
 */
function verifyCertificate(certificate) {
  const now = new Date();
  const validFrom = new Date(certificate.valid_from);
  const validTo = new Date(certificate.valid_to);
  
  const daysUntilExpiry = Math.ceil((validTo - now) / (1000 * 60 * 60 * 24));
  
  return {
    subject: certificate.subject,
    issuer: certificate.issuer,
    validFrom,
    validTo,
    daysUntilExpiry,
    isValid: now >= validFrom && now <= validTo,
    fingerprint: certificate.fingerprint,
    serialNumber: certificate.serialNumber
  };
}

/**
 * Check security headers
 */
function checkSecurityHeaders(headers) {
  const requiredHeaders = {
    'strict-transport-security': 'HSTS',
    'x-frame-options': 'X-Frame-Options',
    'x-xss-protection': 'X-XSS-Protection',
    'x-content-type-options': 'X-Content-Type-Options',
    'referrer-policy': 'Referrer-Policy',
    'content-security-policy': 'Content-Security-Policy'
  };

  const results = {};
  
  Object.entries(requiredHeaders).forEach(([header, name]) => {
    results[name] = {
      present: !!headers[header],
      value: headers[header] || null
    };
  });

  return results;
}

/**
 * Main verification function
 */
async function verifySSLSetup() {
  console.log(`${colors.bright}🔒 SSL Setup Verification${colors.reset}`);
  console.log('='.repeat(50));

  const domains = ['shopnirvanaorganics.com', 'www.shopnirvanaorganics.com'];
  let allTestsPassed = true;

  for (const domain of domains) {
    console.log(`\n${colors.cyan}Testing domain: ${domain}${colors.reset}`);
    console.log('-'.repeat(30));

    try {
      // Test 1: HTTP to HTTPS redirect
      log.step('Testing HTTP to HTTPS redirect...');
      
      try {
        const redirectResponse = await testHTTPRedirect(domain);
        
        if (redirectResponse.statusCode === 301 || redirectResponse.statusCode === 302) {
          if (redirectResponse.location && redirectResponse.location.startsWith('https://')) {
            log.success('HTTP to HTTPS redirect is working');
          } else {
            log.warning('Redirect exists but may not be to HTTPS');
            allTestsPassed = false;
          }
        } else {
          log.error('HTTP to HTTPS redirect not configured');
          allTestsPassed = false;
        }
      } catch (error) {
        log.error(`HTTP redirect test failed: ${error.message}`);
        allTestsPassed = false;
      }

      // Test 2: HTTPS connection
      log.step('Testing HTTPS connection...');
      
      try {
        const httpsResponse = await testHTTPSConnection(domain);
        
        if (httpsResponse.statusCode === 200) {
          log.success('HTTPS connection successful');
        } else {
          log.warning(`HTTPS connection returned status ${httpsResponse.statusCode}`);
        }

        // Test 3: SSL certificate verification
        log.step('Verifying SSL certificate...');
        
        const certInfo = verifyCertificate(httpsResponse.certificate);
        
        log.info(`Certificate Subject: ${certInfo.subject.CN}`);
        log.info(`Certificate Issuer: ${certInfo.issuer.O}`);
        log.info(`Valid From: ${certInfo.validFrom.toISOString()}`);
        log.info(`Valid To: ${certInfo.validTo.toISOString()}`);
        log.info(`Days Until Expiry: ${certInfo.daysUntilExpiry}`);
        
        if (certInfo.isValid) {
          log.success('SSL certificate is valid');
          
          if (certInfo.daysUntilExpiry < 30) {
            log.warning(`Certificate expires in ${certInfo.daysUntilExpiry} days - consider renewal`);
          }
        } else {
          log.error('SSL certificate is not valid');
          allTestsPassed = false;
        }

        // Test 4: Security headers
        log.step('Checking security headers...');
        
        const securityHeaders = checkSecurityHeaders(httpsResponse.headers);
        
        Object.entries(securityHeaders).forEach(([name, info]) => {
          if (info.present) {
            log.success(`${name}: ${info.value}`);
          } else {
            log.warning(`${name}: Missing`);
          }
        });

      } catch (error) {
        log.error(`HTTPS connection failed: ${error.message}`);
        allTestsPassed = false;
      }

    } catch (error) {
      log.error(`Domain test failed: ${error.message}`);
      allTestsPassed = false;
    }
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  
  if (allTestsPassed) {
    log.success('🎉 All SSL tests passed! Your HTTPS setup is working correctly.');
  } else {
    log.error('❌ Some SSL tests failed. Please review the issues above.');
  }

  console.log('');
  log.step('Additional Recommendations:');
  console.log('1. Test your SSL configuration: https://www.ssllabs.com/ssltest/');
  console.log('2. Verify HSTS preload eligibility: https://hstspreload.org/');
  console.log('3. Check security headers: https://securityheaders.com/');
  console.log('4. Monitor certificate expiration dates');
  console.log('5. Set up automated monitoring for SSL issues');

  return allTestsPassed;
}

/**
 * Test specific URL
 */
async function testURL(url) {
  try {
    const parsedUrl = new URL(url);
    const response = await testHTTPSConnection(parsedUrl.hostname, parsedUrl.port || 443);
    
    console.log(`\nTesting: ${url}`);
    console.log(`Status: ${response.statusCode}`);
    console.log(`Certificate: ${response.certificate.subject.CN}`);
    console.log(`Expires: ${new Date(response.certificate.valid_to).toISOString()}`);
    
    return response.statusCode === 200;
  } catch (error) {
    console.error(`Failed to test ${url}: ${error.message}`);
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0].startsWith('http')) {
    // Test specific URL
    testURL(args[0]).then(success => {
      process.exit(success ? 0 : 1);
    });
  } else {
    // Run full verification
    verifySSLSetup().then(success => {
      process.exit(success ? 0 : 1);
    });
  }
}

module.exports = {
  verifySSLSetup,
  testHTTPSConnection,
  testHTTPRedirect,
  verifyCertificate,
  checkSecurityHeaders,
  testURL
};
