import api from './api';

export interface OrderAnalytics {
  overview: {
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    conversionRate: number;
  };
  membershipBreakdown: {
    firstTime: { count: number; revenue: number; percentage: number };
    regular: { count: number; revenue: number; percentage: number };
    premium: { count: number; revenue: number; percentage: number };
  };
  trafficSourceAnalytics: {
    organic: { orders: number; revenue: number; conversionRate: number };
    direct: { orders: number; revenue: number; conversionRate: number };
    socialMedia: { orders: number; revenue: number; conversionRate: number };
    referral: { orders: number; revenue: number; conversionRate: number };
    paid: { orders: number; revenue: number; conversionRate: number };
  };
  shippingAnalytics: {
    regular: { count: number; revenue: number };
    regularFree: { count: number; revenue: number };
    express: { count: number; revenue: number };
  };
  customerInsights: {
    newVsReturning: {
      new: { count: number; percentage: number };
      returning: { count: number; percentage: number };
    };
    lifetimeValueDistribution: {
      low: { count: number; range: string };
      medium: { count: number; range: string };
      high: { count: number; range: string };
    };
  };
  trends: {
    daily: Array<{ date: string; orders: number; revenue: number }>;
    weekly: Array<{ week: string; orders: number; revenue: number }>;
    monthly: Array<{ month: string; orders: number; revenue: number }>;
  };
}

export interface ExportOptions {
  format: 'csv' | 'excel';
  dateRange: {
    startDate: string;
    endDate: string;
  };
  includeFields: string[];
  filters?: {
    status?: string;
    membershipType?: string;
    trafficSource?: string;
    shippingMethod?: string;
  };
}

class OrderAnalyticsService {
  /**
   * Get comprehensive order analytics
   */
  async getOrderAnalytics(options?: {
    dateRange?: { startDate: string; endDate: string };
    filters?: any;
  }): Promise<{ success: boolean; data: OrderAnalytics }> {
    try {
      const response = await api.get('/api/admin/orders/analytics', {
        params: options
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching order analytics:', error);
      throw error;
    }
  }

  /**
   * Get customer segmentation data
   */
  async getCustomerSegmentation(): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/customer-segmentation');
      return response.data;
    } catch (error) {
      console.error('Error fetching customer segmentation:', error);
      throw error;
    }
  }

  /**
   * Get traffic source performance
   */
  async getTrafficSourcePerformance(dateRange?: {
    startDate: string;
    endDate: string;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/traffic-performance', {
        params: dateRange
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching traffic source performance:', error);
      throw error;
    }
  }

  /**
   * Export orders data
   */
  async exportOrders(options: ExportOptions): Promise<{ success: boolean; data: { downloadUrl: string } }> {
    try {
      const response = await api.post('/api/admin/orders/export', options, {
        responseType: 'blob'
      });
      
      // Create download URL from blob
      const blob = new Blob([response.data], {
        type: options.format === 'excel' 
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv'
      });
      
      const downloadUrl = window.URL.createObjectURL(blob);
      
      return {
        success: true,
        data: { downloadUrl }
      };
    } catch (error) {
      console.error('Error exporting orders:', error);
      throw error;
    }
  }

  /**
   * Get order trends
   */
  async getOrderTrends(period: 'daily' | 'weekly' | 'monthly', dateRange?: {
    startDate: string;
    endDate: string;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/trends', {
        params: { period, ...dateRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching order trends:', error);
      throw error;
    }
  }

  /**
   * Get shipping method performance
   */
  async getShippingPerformance(): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/shipping-performance');
      return response.data;
    } catch (error) {
      console.error('Error fetching shipping performance:', error);
      throw error;
    }
  }

  /**
   * Get customer lifetime value analysis
   */
  async getCustomerLifetimeValueAnalysis(): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/customer-ltv-analysis');
      return response.data;
    } catch (error) {
      console.error('Error fetching customer LTV analysis:', error);
      throw error;
    }
  }

  /**
   * Get order fulfillment metrics
   */
  async getFulfillmentMetrics(dateRange?: {
    startDate: string;
    endDate: string;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/fulfillment-metrics', {
        params: dateRange
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching fulfillment metrics:', error);
      throw error;
    }
  }

  /**
   * Update order tracking information
   */
  async updateOrderTracking(orderId: string, trackingData: {
    trackingNumber: string;
    trackingUrl?: string;
    carrier: string;
    estimatedDeliveryDate?: string;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.put(`/api/admin/orders/${orderId}/tracking`, trackingData);
      return response.data;
    } catch (error) {
      console.error('Error updating order tracking:', error);
      throw error;
    }
  }

  /**
   * Bulk update order status
   */
  async bulkUpdateOrderStatus(orderIds: string[], status: string, notes?: string): Promise<{
    success: boolean;
    data: { updated: number; failed: number; errors?: any[] };
  }> {
    try {
      const response = await api.post('/api/admin/orders/bulk-update-status', {
        orderIds,
        status,
        notes
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating order status:', error);
      throw error;
    }
  }

  /**
   * Get order performance by product
   */
  async getProductOrderPerformance(dateRange?: {
    startDate: string;
    endDate: string;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/product-performance', {
        params: dateRange
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching product order performance:', error);
      throw error;
    }
  }

  /**
   * Get geographic order distribution
   */
  async getGeographicDistribution(): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/geographic-distribution');
      return response.data;
    } catch (error) {
      console.error('Error fetching geographic distribution:', error);
      throw error;
    }
  }

  /**
   * Get order abandonment analysis
   */
  async getOrderAbandonmentAnalysis(): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get('/api/admin/orders/abandonment-analysis');
      return response.data;
    } catch (error) {
      console.error('Error fetching order abandonment analysis:', error);
      throw error;
    }
  }

  /**
   * Generate order insights report
   */
  async generateInsightsReport(options: {
    dateRange: { startDate: string; endDate: string };
    includeRecommendations: boolean;
  }): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.post('/api/admin/orders/insights-report', options);
      return response.data;
    } catch (error) {
      console.error('Error generating insights report:', error);
      throw error;
    }
  }
}

export const orderAnalyticsService = new OrderAnalyticsService();
export default orderAnalyticsService;
