#!/bin/bash

# ============================================================================
# Nirvana Organics Full-Stack Production Deployment Script
# ============================================================================
# This script handles complete deployment of both frontend and backend
# to production environment with Square payment integration
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔄 $1${NC}"
    echo "=============================================="
}

# Configuration
DEPLOYMENT_DIR="/var/www/nirvana-organics"
BACKUP_DIR="/var/www/backups/nirvana-organics"
CURRENT_DIR=$(pwd)
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_error "This script should not be run as root for security reasons"
   exit 1
fi

# Pre-deployment checks
log_step "Pre-deployment Checks"

# Check Node.js version
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed"
    exit 1
fi

NODE_VERSION=$(node --version)
log_info "Node.js version: $NODE_VERSION"

# Check npm
if ! command -v npm &> /dev/null; then
    log_error "npm is not installed"
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    log_error ".env.production file not found"
    exit 1
fi

log_success "Pre-deployment checks passed"

# Step 1: Environment Setup
log_step "Step 1: Environment Setup"

# Copy production environment
cp .env.production .env
log_success "Production environment configured"

# Validate environment variables
log_info "Validating environment variables..."
if [ -f "scripts/validate-env.js" ]; then
    node scripts/validate-env.js
    if [ $? -eq 0 ]; then
        log_success "Environment validation passed"
    else
        log_error "Environment validation failed"
        exit 1
    fi
fi

# Step 2: Install Dependencies
log_step "Step 2: Installing Dependencies"

log_info "Installing Node.js dependencies..."
npm ci --production
log_success "Dependencies installed"

# Step 3: Test Square Configuration
log_step "Step 3: Testing Square Production Configuration"

if [ -f "scripts/test-square-production.js" ]; then
    node scripts/test-square-production.js
    if [ $? -eq 0 ]; then
        log_success "Square production configuration verified"
    else
        log_error "Square configuration test failed"
        exit 1
    fi
else
    log_warning "Square test script not found, skipping verification"
fi

# Step 4: Database Operations
log_step "Step 4: Database Operations"

# Test database connection
if [ -f "scripts/test-database-connection.js" ]; then
    log_info "Testing database connection..."
    node scripts/test-database-connection.js
    if [ $? -eq 0 ]; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed"
        exit 1
    fi
fi

# Run migrations
log_info "Running database migrations..."
npm run migrate:prod
if [ $? -eq 0 ]; then
    log_success "Database migrations completed"
else
    log_error "Database migrations failed"
    exit 1
fi

# Step 5: Frontend Build
log_step "Step 5: Building Frontend"

log_info "Building React frontend for production..."
npm run build:prod
if [ $? -eq 0 ]; then
    log_success "Frontend build completed"
else
    log_error "Frontend build failed"
    exit 1
fi

# Build admin panel
log_info "Building admin panel..."
npm run build:admin
if [ $? -eq 0 ]; then
    log_success "Admin panel build completed"
else
    log_warning "Admin panel build failed, continuing..."
fi

# Step 6: Security Setup
log_step "Step 6: Security Configuration"

# Run security setup script
if [ -f "scripts/setup-security.js" ]; then
    node scripts/setup-security.js
    log_success "Security setup completed"
fi

# Set proper file permissions
chmod -R 755 .
chmod -R 777 uploads/ 2>/dev/null || true
chmod -R 777 logs/ 2>/dev/null || true
chmod -R 755 dist/ 2>/dev/null || true
chmod -R 755 dist-admin/ 2>/dev/null || true

log_success "File permissions set"

# Step 7: Service Management
log_step "Step 7: Service Management"

# Stop existing PM2 processes
log_info "Stopping existing services..."
pm2 stop all 2>/dev/null || true

# Start with PM2
log_info "Starting application with PM2..."
pm2 start ecosystem.config.js --env production
if [ $? -eq 0 ]; then
    log_success "Application started with PM2"
else
    log_error "Failed to start application with PM2"
    exit 1
fi

# Save PM2 configuration
pm2 save
log_success "PM2 configuration saved"

# Step 8: Post-deployment Verification
log_step "Step 8: Post-deployment Verification"

# Wait for application to start
log_info "Waiting for application to start..."
sleep 10

# Test API health
log_info "Testing API health..."
if command -v curl &> /dev/null; then
    if curl -f -s http://localhost:5000/health > /dev/null; then
        log_success "API health check passed"
    else
        log_warning "API health check failed - application may still be starting"
    fi
else
    log_warning "curl not available, skipping health check"
fi

# Display PM2 status
log_info "PM2 Process Status:"
pm2 status

# Step 9: SSL and Nginx Configuration
log_step "Step 9: SSL and Web Server Configuration"

if [ -f "scripts/setup-ssl-certificates.sh" ]; then
    log_info "Setting up SSL certificates..."
    sudo ./scripts/setup-ssl-certificates.sh
    if [ $? -eq 0 ]; then
        log_success "SSL certificates configured"
    else
        log_warning "SSL setup failed - manual configuration may be required"
    fi
else
    log_warning "SSL setup script not found"
fi

# Final Success Message
log_step "Deployment Complete!"

echo -e "${GREEN}"
echo "🎉 Nirvana Organics Full-Stack Deployment Successful!"
echo "=============================================="
echo "✅ Frontend: Built and deployed"
echo "✅ Backend: Running with PM2"
echo "✅ Database: Migrated and ready"
echo "✅ Square: Production configuration verified"
echo "✅ SSL: Configured (if available)"
echo ""
echo "🌐 Application URLs:"
echo "   Frontend: https://shopnirvanaorganics.com"
echo "   Admin:    https://shopnirvanaorganics.com/admin"
echo "   API:      https://shopnirvanaorganics.com/api"
echo ""
echo "📊 Next Steps:"
echo "   1. Test payment processing"
echo "   2. Verify SSL certificates"
echo "   3. Set up monitoring"
echo "   4. Configure backups"
echo -e "${NC}"

log_success "Deployment completed successfully at $(date)"

# Create deployment record
DEPLOYMENT_RECORD="/var/www/nirvana-organics/deployments.log"
echo "$(date '+%Y-%m-%d %H:%M:%S') - Deployment completed successfully" >> "$DEPLOYMENT_RECORD"
