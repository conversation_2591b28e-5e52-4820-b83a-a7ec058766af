#!/usr/bin/env node

/**
 * Backup and Disaster Recovery Setup
 * Comprehensive backup system for database, files, and configurations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('💾 Setting up Backup and Disaster Recovery System...\n');

// Configuration
const config = {
  backupDir: '/var/backups/nirvana-organics',
  retentionDays: 30,
  dbBackupSchedule: '0 2 * * *', // Daily at 2 AM
  fileBackupSchedule: '0 3 * * 0', // Weekly on Sunday at 3 AM
  remoteBackupEnabled: process.env.REMOTE_BACKUP_ENABLED === 'true',
  s3Bucket: process.env.BACKUP_S3_BUCKET,
  encryptionKey: process.env.BACKUP_ENCRYPTION_KEY
};

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

function execCommand(command, description) {
  log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed`, 'success');
    return true;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'error');
    return false;
  }
}

// Create backup directories
function createBackupDirectories() {
  log('📁 Creating backup directories...');
  
  const dirs = [
    config.backupDir,
    path.join(config.backupDir, 'database'),
    path.join(config.backupDir, 'files'),
    path.join(config.backupDir, 'config'),
    path.join(config.backupDir, 'logs'),
    path.join(config.backupDir, 'scripts')
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      log(`   Created: ${dir}`);
    }
  });
  
  log('✅ Backup directories created', 'success');
}

// Create database backup script
function createDatabaseBackupScript() {
  log('🗄️  Creating database backup script...');
  
  const backupScript = `#!/bin/bash

# Database Backup Script for Nirvana Organics
# Performs automated MySQL database backups with compression and encryption

set -e

# Configuration
BACKUP_DIR="${config.backupDir}/database"
DB_HOST="${process.env.DB_HOST || 'localhost'}"
DB_PORT="${process.env.DB_PORT || '3306'}"
DB_NAME="${process.env.DB_NAME}"
DB_USER="${process.env.DB_USER}"
DB_PASSWORD="${process.env.DB_PASSWORD}"
RETENTION_DAYS=${config.retentionDays}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="nirvana_db_backup_$TIMESTAMP.sql"
COMPRESSED_FILE="$BACKUP_FILE.gz"
LOG_FILE="$BACKUP_DIR/backup.log"

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_message "Starting database backup"

# Create backup
mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \\
    --single-transaction \\
    --routines \\
    --triggers \\
    --events \\
    --hex-blob \\
    --opt \\
    "$DB_NAME" > "$BACKUP_DIR/$BACKUP_FILE"

if [ $? -eq 0 ]; then
    log_message "Database dump completed successfully"
    
    # Compress backup
    gzip "$BACKUP_DIR/$BACKUP_FILE"
    log_message "Backup compressed: $COMPRESSED_FILE"
    
    # Verify backup integrity
    if gunzip -t "$BACKUP_DIR/$COMPRESSED_FILE"; then
        log_message "Backup integrity verified"
        
        # Calculate backup size
        BACKUP_SIZE=$(du -h "$BACKUP_DIR/$COMPRESSED_FILE" | cut -f1)
        log_message "Backup size: $BACKUP_SIZE"
        
        # Upload to remote storage if enabled
        ${config.remoteBackupEnabled ? `
        if [ ! -z "$S3_BUCKET" ]; then
            aws s3 cp "$BACKUP_DIR/$COMPRESSED_FILE" "s3://$S3_BUCKET/database/" --storage-class STANDARD_IA
            if [ $? -eq 0 ]; then
                log_message "Backup uploaded to S3 successfully"
            else
                log_message "Failed to upload backup to S3"
            fi
        fi
        ` : '# Remote backup disabled'}
        
        # Clean up old backups
        find "$BACKUP_DIR" -name "nirvana_db_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete
        log_message "Old backups cleaned up (retention: $RETENTION_DAYS days)"
        
        log_message "Database backup completed successfully"
        echo "✅ Database backup completed: $COMPRESSED_FILE"
        
    else
        log_message "Backup integrity check failed"
        rm -f "$BACKUP_DIR/$COMPRESSED_FILE"
        echo "❌ Backup integrity check failed"
        exit 1
    fi
else
    log_message "Database dump failed"
    echo "❌ Database backup failed"
    exit 1
fi
`;

  const scriptPath = path.join(config.backupDir, 'scripts', 'backup-database.sh');
  fs.writeFileSync(scriptPath, backupScript);
  fs.chmodSync(scriptPath, '755');
  
  log(`✅ Database backup script created: ${scriptPath}`, 'success');
  return scriptPath;
}

// Create file backup script
function createFileBackupScript() {
  log('📁 Creating file backup script...');
  
  const fileBackupScript = `#!/bin/bash

# File Backup Script for Nirvana Organics
# Backs up application files, uploads, and configurations

set -e

# Configuration
BACKUP_DIR="${config.backupDir}/files"
APP_DIR="/var/www/nirvana-organics/current"
UPLOADS_DIR="$APP_DIR/public/uploads"
CONFIG_DIR="$APP_DIR"
RETENTION_DAYS=${config.retentionDays}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="nirvana_files_backup_$TIMESTAMP"
LOG_FILE="$BACKUP_DIR/backup.log"

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_message "Starting file backup"

# Create backup archive
tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" \\
    --exclude="node_modules" \\
    --exclude="dist" \\
    --exclude="dist-admin" \\
    --exclude="logs" \\
    --exclude=".git" \\
    --exclude="*.log" \\
    -C "$(dirname "$APP_DIR")" \\
    "$(basename "$APP_DIR")"

if [ $? -eq 0 ]; then
    log_message "File backup completed successfully"
    
    # Calculate backup size
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_NAME.tar.gz" | cut -f1)
    log_message "Backup size: $BACKUP_SIZE"
    
    # Upload to remote storage if enabled
    ${config.remoteBackupEnabled ? `
    if [ ! -z "$S3_BUCKET" ]; then
        aws s3 cp "$BACKUP_DIR/$BACKUP_NAME.tar.gz" "s3://$S3_BUCKET/files/" --storage-class STANDARD_IA
        if [ $? -eq 0 ]; then
            log_message "File backup uploaded to S3 successfully"
        else
            log_message "Failed to upload file backup to S3"
        fi
    fi
    ` : '# Remote backup disabled'}
    
    # Clean up old backups
    find "$BACKUP_DIR" -name "nirvana_files_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    log_message "Old file backups cleaned up"
    
    echo "✅ File backup completed: $BACKUP_NAME.tar.gz"
    
else
    log_message "File backup failed"
    echo "❌ File backup failed"
    exit 1
fi
`;

  const scriptPath = path.join(config.backupDir, 'scripts', 'backup-files.sh');
  fs.writeFileSync(scriptPath, fileBackupScript);
  fs.chmodSync(scriptPath, '755');
  
  log(`✅ File backup script created: ${scriptPath}`, 'success');
  return scriptPath;
}

// Create restore script
function createRestoreScript() {
  log('🔄 Creating restore script...');
  
  const restoreScript = `#!/bin/bash

# Restore Script for Nirvana Organics
# Restores database and files from backup

set -e

# Configuration
BACKUP_DIR="${config.backupDir}"
APP_DIR="/var/www/nirvana-organics"
DB_HOST="${process.env.DB_HOST || 'localhost'}"
DB_PORT="${process.env.DB_PORT || '3306'}"
DB_NAME="${process.env.DB_NAME}"
DB_USER="${process.env.DB_USER}"
DB_PASSWORD="${process.env.DB_PASSWORD}"

# Usage function
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -d, --database BACKUP_FILE    Restore database from backup file"
    echo "  -f, --files BACKUP_FILE       Restore files from backup file"
    echo "  -l, --list                    List available backups"
    echo "  -h, --help                    Show this help message"
    exit 1
}

# List available backups
list_backups() {
    echo "📋 Available Database Backups:"
    ls -la "$BACKUP_DIR/database/"*.sql.gz 2>/dev/null || echo "No database backups found"
    
    echo ""
    echo "📋 Available File Backups:"
    ls -la "$BACKUP_DIR/files/"*.tar.gz 2>/dev/null || echo "No file backups found"
}

# Restore database
restore_database() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo "❌ Backup file not found: $backup_file"
        exit 1
    fi
    
    echo "🔄 Restoring database from: $backup_file"
    
    # Stop application
    pm2 stop all || true
    
    # Create backup of current database
    echo "📦 Creating backup of current database..."
    mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \\
        --single-transaction "$DB_NAME" > "$BACKUP_DIR/database/pre_restore_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Restore database
    echo "🔄 Restoring database..."
    gunzip -c "$backup_file" | mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
    
    if [ $? -eq 0 ]; then
        echo "✅ Database restored successfully"
        
        # Restart application
        pm2 start all
        
        echo "✅ Application restarted"
    else
        echo "❌ Database restore failed"
        exit 1
    fi
}

# Restore files
restore_files() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo "❌ Backup file not found: $backup_file"
        exit 1
    fi
    
    echo "🔄 Restoring files from: $backup_file"
    
    # Stop application
    pm2 stop all || true
    
    # Create backup of current files
    echo "📦 Creating backup of current files..."
    tar -czf "$BACKUP_DIR/files/pre_restore_backup_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$APP_DIR" current
    
    # Restore files
    echo "🔄 Extracting files..."
    tar -xzf "$backup_file" -C "$APP_DIR"
    
    # Set permissions
    chown -R www-data:www-data "$APP_DIR/current"
    chmod -R 755 "$APP_DIR/current"
    
    # Restart application
    pm2 start all
    
    echo "✅ Files restored and application restarted"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--database)
            restore_database "$2"
            shift 2
            ;;
        -f|--files)
            restore_files "$2"
            shift 2
            ;;
        -l|--list)
            list_backups
            exit 0
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# If no arguments provided, show usage
if [ $# -eq 0 ]; then
    usage
fi
`;

  const scriptPath = path.join(config.backupDir, 'scripts', 'restore.sh');
  fs.writeFileSync(scriptPath, restoreScript);
  fs.chmodSync(scriptPath, '755');
  
  log(`✅ Restore script created: ${scriptPath}`, 'success');
  return scriptPath;
}

// Setup cron jobs
function setupCronJobs() {
  log('⏰ Setting up backup cron jobs...');
  
  const cronJobs = `
# Nirvana Organics Backup Jobs
${config.dbBackupSchedule} ${path.join(config.backupDir, 'scripts', 'backup-database.sh')}
${config.fileBackupSchedule} ${path.join(config.backupDir, 'scripts', 'backup-files.sh')}
`;

  const cronPath = path.join(config.backupDir, 'backup-crontab');
  fs.writeFileSync(cronPath, cronJobs);
  
  log(`✅ Cron jobs configuration created: ${cronPath}`, 'success');
  log('   To install: crontab ' + cronPath, 'info');
}

// Main setup function
function main() {
  try {
    createBackupDirectories();
    createDatabaseBackupScript();
    createFileBackupScript();
    createRestoreScript();
    setupCronJobs();
    
    log('\n🎉 Backup system setup completed!', 'success');
    log('\n📋 Next steps:');
    log('   1. Install cron jobs: crontab ' + path.join(config.backupDir, 'backup-crontab'));
    log('   2. Test database backup: ' + path.join(config.backupDir, 'scripts', 'backup-database.sh'));
    log('   3. Test file backup: ' + path.join(config.backupDir, 'scripts', 'backup-files.sh'));
    log('   4. Configure remote backup (S3) if needed');
    log('   5. Test restore procedures');
    
    log('\n🔧 Available commands:');
    log('   Database backup: npm run backup:db');
    log('   File backup: npm run backup:files');
    log('   List backups: ' + path.join(config.backupDir, 'scripts', 'restore.sh') + ' --list');
    log('   Restore database: ' + path.join(config.backupDir, 'scripts', 'restore.sh') + ' --database <file>');
    log('   Restore files: ' + path.join(config.backupDir, 'scripts', 'restore.sh') + ' --files <file>');
    
  } catch (error) {
    log(`❌ Backup setup failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run setup
main();
