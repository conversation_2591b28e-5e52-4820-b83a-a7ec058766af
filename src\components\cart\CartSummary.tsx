import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAppDispatch } from '../../hooks/redux';
import { applyCoupon, removeCoupon } from '../../store/slices/cartSlice';
import { addToast } from '../../store/slices/uiSlice';
import { Cart } from '../../types';
import { TagIcon, XMarkIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../common/LoadingSpinner';

interface CartSummaryProps {
  cart: Cart;
  loading?: boolean;
}

const CartSummary: React.FC<CartSummaryProps> = ({ cart, loading = false }) => {
  const dispatch = useAppDispatch();
  const [couponCode, setCouponCode] = useState('');
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);

  const handleApplyCoupon = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!couponCode.trim()) return;

    setIsApplyingCoupon(true);
    try {
      await dispatch(applyCoupon(couponCode.trim())).unwrap();
      setCouponCode('');
      dispatch(addToast({
        type: 'success',
        title: 'Coupon Applied',
        message: 'Coupon code applied successfully'
      }));
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Invalid Coupon',
        message: error as string || 'Failed to apply coupon code'
      }));
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  const handleRemoveCoupon = async () => {
    try {
      await dispatch(removeCoupon()).unwrap();
      dispatch(addToast({
        type: 'success',
        title: 'Coupon Removed',
        message: 'Coupon code removed successfully'
      }));
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Remove Failed',
        message: error as string || 'Failed to remove coupon code'
      }));
    }
  };

  const freeShippingThreshold = 100;
  const remainingForFreeShipping = freeShippingThreshold - cart.subtotal;

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

      {/* Free Shipping Progress */}
      {cart.subtotal < freeShippingThreshold && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-800">
              Free Shipping Progress
            </span>
            <span className="text-sm text-blue-600">
              ${remainingForFreeShipping.toFixed(2)} to go
            </span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min((cart.subtotal / freeShippingThreshold) * 100, 100)}%` }}
            />
          </div>
          <p className="text-xs text-blue-600 mt-2">
            Add ${remainingForFreeShipping.toFixed(2)} more to qualify for free shipping!
          </p>
        </div>
      )}

      {cart.subtotal >= freeShippingThreshold && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">
                Congratulations! You qualify for free shipping.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Coupon Code Section */}
      <div className="mb-6">
        {cart.couponCode ? (
          <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <TagIcon className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-sm font-medium text-green-800">
                Coupon: {cart.couponCode}
              </span>
            </div>
            <button
              onClick={handleRemoveCoupon}
              className="text-green-600 hover:text-green-800 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        ) : (
          <form onSubmit={handleApplyCoupon} className="space-y-3">
            <label htmlFor="coupon" className="block text-sm font-medium text-gray-700">
              Coupon Code
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                id="coupon"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value)}
                placeholder="Enter coupon code"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                disabled={isApplyingCoupon}
              />
              <button
                type="submit"
                disabled={!couponCode.trim() || isApplyingCoupon}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {isApplyingCoupon ? (
                  <LoadingSpinner size="small" color="white" />
                ) : (
                  'Apply'
                )}
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Order Totals */}
      <div className="space-y-3 border-t border-gray-200 pt-4">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Subtotal ({cart.items.length} items)</span>
          <span className="font-medium">${cart.subtotal.toFixed(2)}</span>
        </div>

        {cart.discount > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Discount</span>
            <span className="font-medium text-green-600">-${cart.discount.toFixed(2)}</span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Shipping</span>
          <span className="font-medium">
            {cart.shipping === 0 ? 'Free' : `$${cart.shipping.toFixed(2)}`}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Tax</span>
          <span className="font-medium">${cart.tax.toFixed(2)}</span>
        </div>

        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between text-lg font-semibold">
            <span>Total</span>
            <span className="text-primary-600">${cart.total.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Checkout Button */}
      <div className="mt-6">
        <Link
          to="/checkout"
          className={`w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? (
            <LoadingSpinner size="small" color="white" />
          ) : (
            'Proceed to Checkout'
          )}
        </Link>
      </div>

      {/* Continue Shopping */}
      <div className="mt-4 text-center">
        <Link
          to="/shop"
          className="text-primary-600 hover:text-primary-700 text-sm font-medium transition-colors"
        >
          Continue Shopping
        </Link>
      </div>
    </div>
  );
};

export default CartSummary;
