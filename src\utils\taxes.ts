// US Tax Calculation Utility
export interface TaxRate {
  state: string;
  stateName: string;
  stateRate: number;
  averageLocalRate: number;
  totalRate: number;
  cannabisTaxRate?: number; // Additional cannabis-specific tax
  notes?: string[];
}

// US State Tax Rates (2024) - Cannabis products may have additional taxes
export const US_TAX_RATES: TaxRate[] = [
  { state: 'AL', stateName: 'Alabama', stateRate: 4.00, averageLocalRate: 5.22, totalRate: 9.22 },
  { state: 'AK', stateName: 'Alaska', stateRate: 0.00, averageLocalRate: 1.76, totalRate: 1.76 },
  { state: 'AZ', stateName: 'Arizona', stateRate: 5.60, averageLocalRate: 2.77, totalRate: 8.37 },
  { state: 'AR', stateName: 'Arkansas', stateRate: 6.50, averageLocalRate: 2.93, totalRate: 9.43 },
  { state: 'CA', stateName: 'California', stateRate: 7.25, averageLocalRate: 3.33, totalRate: 10.58, cannabisTaxRate: 15.00 },
  { state: 'CO', stateName: 'Colorado', stateRate: 2.90, averageLocalRate: 4.73, totalRate: 7.63, cannabisTaxRate: 15.00 },
  { state: 'CT', stateName: 'Connecticut', stateRate: 6.35, averageLocalRate: 0.00, totalRate: 6.35 },
  { state: 'DE', stateName: 'Delaware', stateRate: 0.00, averageLocalRate: 0.00, totalRate: 0.00 },
  { state: 'FL', stateName: 'Florida', stateRate: 6.00, averageLocalRate: 1.05, totalRate: 7.05 },
  { state: 'GA', stateName: 'Georgia', stateRate: 4.00, averageLocalRate: 3.29, totalRate: 7.29 },
  { state: 'HI', stateName: 'Hawaii', stateRate: 4.17, averageLocalRate: 0.41, totalRate: 4.58 },
  { state: 'ID', stateName: 'Idaho', stateRate: 6.00, averageLocalRate: 0.03, totalRate: 6.03 },
  { state: 'IL', stateName: 'Illinois', stateRate: 6.25, averageLocalRate: 2.49, totalRate: 8.74 },
  { state: 'IN', stateName: 'Indiana', stateRate: 7.00, averageLocalRate: 0.00, totalRate: 7.00 },
  { state: 'IA', stateName: 'Iowa', stateRate: 6.00, averageLocalRate: 0.82, totalRate: 6.82 },
  { state: 'KS', stateName: 'Kansas', stateRate: 6.50, averageLocalRate: 2.17, totalRate: 8.67 },
  { state: 'KY', stateName: 'Kentucky', stateRate: 6.00, averageLocalRate: 0.00, totalRate: 6.00 },
  { state: 'LA', stateName: 'Louisiana', stateRate: 4.45, averageLocalRate: 5.00, totalRate: 9.45 },
  { state: 'ME', stateName: 'Maine', stateRate: 5.50, averageLocalRate: 0.00, totalRate: 5.50 },
  { state: 'MD', stateName: 'Maryland', stateRate: 6.00, averageLocalRate: 0.00, totalRate: 6.00 },
  { state: 'MA', stateName: 'Massachusetts', stateRate: 6.25, averageLocalRate: 0.00, totalRate: 6.25 },
  { state: 'MI', stateName: 'Michigan', stateRate: 6.00, averageLocalRate: 0.00, totalRate: 6.00 },
  { state: 'MN', stateName: 'Minnesota', stateRate: 6.88, averageLocalRate: 0.55, totalRate: 7.43 },
  { state: 'MS', stateName: 'Mississippi', stateRate: 7.00, averageLocalRate: 0.07, totalRate: 7.07 },
  { state: 'MO', stateName: 'Missouri', stateRate: 4.23, averageLocalRate: 3.90, totalRate: 8.13 },
  { state: 'MT', stateName: 'Montana', stateRate: 0.00, averageLocalRate: 0.00, totalRate: 0.00 },
  { state: 'NE', stateName: 'Nebraska', stateRate: 5.50, averageLocalRate: 1.35, totalRate: 6.85 },
  { state: 'NV', stateName: 'Nevada', stateRate: 6.85, averageLocalRate: 1.29, totalRate: 8.14 },
  { state: 'NH', stateName: 'New Hampshire', stateRate: 0.00, averageLocalRate: 0.00, totalRate: 0.00 },
  { state: 'NJ', stateName: 'New Jersey', stateRate: 6.63, averageLocalRate: -0.03, totalRate: 6.60 },
  { state: 'NM', stateName: 'New Mexico', stateRate: 5.13, averageLocalRate: 2.69, totalRate: 7.82 },
  { state: 'NY', stateName: 'New York', stateRate: 4.00, averageLocalRate: 4.49, totalRate: 8.49 },
  { state: 'NC', stateName: 'North Carolina', stateRate: 4.75, averageLocalRate: 2.22, totalRate: 6.97 },
  { state: 'ND', stateName: 'North Dakota', stateRate: 5.00, averageLocalRate: 1.85, totalRate: 6.85 },
  { state: 'OH', stateName: 'Ohio', stateRate: 5.75, averageLocalRate: 1.42, totalRate: 7.17 },
  { state: 'OK', stateName: 'Oklahoma', stateRate: 4.50, averageLocalRate: 4.42, totalRate: 8.92 },
  { state: 'OR', stateName: 'Oregon', stateRate: 0.00, averageLocalRate: 0.00, totalRate: 0.00 },
  { state: 'PA', stateName: 'Pennsylvania', stateRate: 6.00, averageLocalRate: 0.34, totalRate: 6.34 },
  { state: 'RI', stateName: 'Rhode Island', stateRate: 7.00, averageLocalRate: 0.00, totalRate: 7.00 },
  { state: 'SC', stateName: 'South Carolina', stateRate: 6.00, averageLocalRate: 1.43, totalRate: 7.43 },
  { state: 'SD', stateName: 'South Dakota', stateRate: 4.20, averageLocalRate: 1.90, totalRate: 6.10 },
  { state: 'TN', stateName: 'Tennessee', stateRate: 7.00, averageLocalRate: 2.47, totalRate: 9.47 },
  { state: 'TX', stateName: 'Texas', stateRate: 6.25, averageLocalRate: 1.94, totalRate: 8.19 },
  { state: 'UT', stateName: 'Utah', stateRate: 6.10, averageLocalRate: 0.99, totalRate: 7.09 },
  { state: 'VT', stateName: 'Vermont', stateRate: 6.00, averageLocalRate: 0.18, totalRate: 6.18 },
  { state: 'VA', stateName: 'Virginia', stateRate: 5.30, averageLocalRate: 0.00, totalRate: 5.30 },
  { state: 'WA', stateName: 'Washington', stateRate: 6.50, averageLocalRate: 3.10, totalRate: 9.60 },
  { state: 'WV', stateName: 'West Virginia', stateRate: 6.00, averageLocalRate: 0.59, totalRate: 6.59 },
  { state: 'WI', stateName: 'Wisconsin', stateRate: 5.00, averageLocalRate: 0.44, totalRate: 5.44 },
  { state: 'WY', stateName: 'Wyoming', stateRate: 4.00, averageLocalRate: 1.36, totalRate: 5.36 },
  { state: 'DC', stateName: 'District of Columbia', stateRate: 6.00, averageLocalRate: 0.00, totalRate: 6.00 }
];

// Calculate tax for a given state and amount
export const calculateTax = (
  state: string,
  subtotal: number,
  isHempProduct: boolean = true
): { 
  salesTax: number; 
  cannabisTax: number; 
  totalTax: number; 
  taxRate: TaxRate | null;
  breakdown: { description: string; rate: number; amount: number }[]
} => {
  const taxRate = US_TAX_RATES.find(rate => rate.state === state);
  
  if (!taxRate) {
    return {
      salesTax: 0,
      cannabisTax: 0,
      totalTax: 0,
      taxRate: null,
      breakdown: []
    };
  }

  const salesTax = subtotal * (taxRate.totalRate / 100);
  const cannabisTax = isHempProduct && taxRate.cannabisTaxRate 
    ? subtotal * (taxRate.cannabisTaxRate / 100) 
    : 0;
  
  const totalTax = salesTax + cannabisTax;

  const breakdown = [
    {
      description: `${taxRate.stateName} Sales Tax`,
      rate: taxRate.totalRate,
      amount: salesTax
    }
  ];

  if (cannabisTax > 0) {
    breakdown.push({
      description: `${taxRate.stateName} Cannabis Tax`,
      rate: taxRate.cannabisTaxRate!,
      amount: cannabisTax
    });
  }

  return {
    salesTax,
    cannabisTax,
    totalTax,
    taxRate,
    breakdown
  };
};

// Get tax information for a state
export const getTaxInfo = (state: string): TaxRate | null => {
  return US_TAX_RATES.find(rate => rate.state === state) || null;
};

// Check if a state has cannabis-specific taxes
export const hasCannabisSpecificTax = (state: string): boolean => {
  const taxRate = US_TAX_RATES.find(rate => rate.state === state);
  return !!(taxRate?.cannabisTaxRate && taxRate.cannabisTaxRate > 0);
};

// Get states with no sales tax
export const getNoSalesTaxStates = (): string[] => {
  return US_TAX_RATES
    .filter(rate => rate.totalRate === 0)
    .map(rate => rate.state);
};

// Get states with cannabis-specific taxes
export const getCannabisSpecificTaxStates = (): TaxRate[] => {
  return US_TAX_RATES.filter(rate => rate.cannabisTaxRate && rate.cannabisTaxRate > 0);
};

// Format tax display
export const formatTaxDisplay = (
  taxAmount: number, 
  taxRate: number, 
  stateName: string
): string => {
  return `${stateName} Tax (${taxRate.toFixed(2)}%): $${taxAmount.toFixed(2)}`;
};

// Calculate total order amount including tax
export const calculateOrderTotal = (
  subtotal: number,
  shipping: number,
  discount: number,
  state: string,
  isHempProduct: boolean = true
): {
  subtotal: number;
  shipping: number;
  discount: number;
  taxDetails: ReturnType<typeof calculateTax>;
  total: number;
} => {
  const taxableAmount = Math.max(0, subtotal - discount);
  const taxDetails = calculateTax(state, taxableAmount, isHempProduct);
  
  const total = subtotal + shipping + taxDetails.totalTax - discount;

  return {
    subtotal,
    shipping,
    discount,
    taxDetails,
    total: Math.max(0, total)
  };
};

// Validate tax calculation
export const validateTaxCalculation = (
  state: string,
  subtotal: number
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!state || state.length !== 2) {
    errors.push('Invalid state code');
  }

  if (subtotal < 0) {
    errors.push('Subtotal cannot be negative');
  }

  const taxRate = US_TAX_RATES.find(rate => rate.state === state);
  if (!taxRate) {
    errors.push(`Tax rates not available for state: ${state}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
