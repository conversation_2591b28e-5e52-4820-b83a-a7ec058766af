
# ============================================================================
# NIRVANA BACKEND DEPLOYMENT COMMANDS
# ============================================================================

# 1. Create deployment directory on remote server
ssh root@srv928821 "mkdir -p /var/www/nirvana-backend"

# 2. Copy files to remote server (choose one method):

# Method A: Using rsync (recommended)
rsync -avz --exclude='node_modules' --exclude='.git' --exclude='logs' \
  ./ root@srv928821:/var/www/nirvana-backend/

# Method B: Using scp
scp -r server/ package.json package-lock.json .env.production \
  root@srv928821:/var/www/nirvana-backend/

# 3. Install dependencies and setup on remote server
ssh root@srv928821 "cd /var/www/nirvana-backend && \
  npm install --production && \
  cp .env.production .env && \
  mkdir -p uploads logs && \
  chmod +x scripts/*.js"

# 4. Setup database (if needed)
ssh root@srv928821 "cd /var/www/nirvana-backend && \
  node scripts/setup-database.js && \
  node scripts/create-database-tables.js"

# 5. Start the server with PM2 (process manager)
ssh root@srv928821 "cd /var/www/nirvana-backend && \
  npm install -g pm2 && \
  pm2 start server/index.js --name nirvana-backend && \
  pm2 startup && \
  pm2 save"

# 6. Setup nginx reverse proxy (optional)
ssh root@srv928821 "apt update && apt install -y nginx"

# 7. Check server status
ssh root@srv928821 "cd /var/www/nirvana-backend && pm2 status"
