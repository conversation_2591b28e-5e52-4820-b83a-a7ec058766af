import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import SEOHead from '../components/seo/SEOHead';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const FAQ: React.FC = () => {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  const faqData: FAQItem[] = [
    // Product Information
    {
      id: 'product-1',
      category: 'products',
      question: 'What types of cannabis products do you offer?',
      answer: 'We offer a comprehensive selection of premium hemp-derived cannabis products including CBD and THC-A flowers, Delta-8 and Delta-9 edibles, pre-rolls, diamond sauce concentrates, vapes, and chocolates. All products are lab-tested for quality and potency.'
    },
    {
      id: 'product-2',
      category: 'products',
      question: 'Are your products lab tested?',
      answer: 'Yes, absolutely! All our products undergo rigorous third-party lab testing for potency, purity, pesticides, heavy metals, and microbials. Certificate of Analysis (COA) documents are available for every batch and can be viewed on individual product pages.'
    },
    {
      id: 'product-3',
      category: 'products',
      question: 'What is the difference between CBD, THC-A, Delta-8, and Delta-9?',
      answer: 'CBD is non-psychoactive and offers therapeutic benefits. THC-A is the acidic precursor to THC that becomes psychoactive when heated. Delta-8 provides mild psychoactive effects, while Delta-9 is the traditional THC with stronger psychoactive properties. All our products comply with federal hemp regulations.'
    },
    {
      id: 'product-4',
      category: 'products',
      question: 'How should I store my cannabis products?',
      answer: 'Store products in a cool, dry place away from direct sunlight. Keep edibles refrigerated if indicated. Flowers should be stored in airtight containers. Always keep products away from children and pets, and follow local storage regulations.'
    },

    // Ordering & Payment
    {
      id: 'order-1',
      category: 'ordering',
      question: 'How do I place an order?',
      answer: 'Simply browse our products, add items to your cart, and proceed to checkout. You\'ll need to create an account and verify you\'re 21+ years old. We accept major credit cards and offer secure payment processing through Square.'
    },
    {
      id: 'order-2',
      category: 'ordering',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, MasterCard, American Express, Discover) processed securely through Square Payment API. We do not currently accept cash, checks, or cryptocurrency.'
    },
    {
      id: 'order-3',
      category: 'ordering',
      question: 'Can I modify or cancel my order?',
      answer: 'Orders can be modified or cancelled within 1 hour of placement, provided they haven\'t entered processing. Contact our customer service <NAME_EMAIL> or use the live chat feature.'
    },
    {
      id: 'order-4',
      category: 'ordering',
      question: 'Do you offer bulk or wholesale pricing?',
      answer: 'Yes! We offer competitive wholesale pricing for licensed retailers and bulk discounts for large orders. Contact our sales <NAME_EMAIL> for pricing information and requirements.'
    },

    // Shipping & Delivery
    {
      id: 'shipping-1',
      category: 'shipping',
      question: 'What are your shipping times and costs?',
      answer: 'We offer free standard shipping (3-5 business days) on orders over $100. Express shipping (1-2 business days) is available for $15. All orders are processed within 1-2 business days and shipped via USPS with tracking.'
    },
    {
      id: 'shipping-2',
      category: 'shipping',
      question: 'Do you ship nationwide?',
      answer: 'We ship to all 50 states where hemp-derived products are legal under federal law. However, some states have additional restrictions. Please check your local and state laws before ordering. We cannot ship to addresses outside the United States.'
    },
    {
      id: 'shipping-3',
      category: 'shipping',
      question: 'How is my order packaged?',
      answer: 'All orders are packaged discreetly in plain, unmarked boxes with no indication of contents. Products are sealed and protected to maintain freshness and quality during transit. We respect your privacy completely.'
    },
    {
      id: 'shipping-4',
      category: 'shipping',
      question: 'Can I track my order?',
      answer: 'Yes! Once your order ships, you\'ll receive a tracking number via email. You can also track your order status in your account dashboard. We provide real-time updates throughout the shipping process.'
    },

    // Returns & Exchanges
    {
      id: 'returns-1',
      category: 'returns',
      question: 'What is your return policy?',
      answer: 'We offer a 30-day return policy for unopened, unused products in original packaging. Due to health regulations, we cannot accept returns on opened consumable products. Contact us within 30 days of delivery to initiate a return.'
    },
    {
      id: 'returns-2',
      category: 'returns',
      question: 'How do I return a product?',
      answer: 'Contact our customer service team to obtain a Return Merchandise Authorization (RMA) number. Package the unopened product securely and ship it back to us with the RMA number. Refunds are processed within 5-7 business days after we receive the return.'
    },
    {
      id: 'returns-3',
      category: 'returns',
      question: 'What if my order arrives damaged?',
      answer: 'We take great care in packaging, but if your order arrives damaged, contact us immediately with photos of the damage. We\'ll replace damaged items at no cost or provide a full refund. All shipments are insured for your protection.'
    },

    // Legal & Compliance
    {
      id: 'legal-1',
      category: 'legal',
      question: 'Are your products legal?',
      answer: 'Yes, all our products are derived from hemp containing less than 0.3% Delta-9 THC, making them federally legal under the 2018 Farm Bill. However, state laws vary, so please check your local regulations before purchasing.'
    },
    {
      id: 'legal-2',
      category: 'legal',
      question: 'Do I need to be 21 to purchase?',
      answer: 'Yes, you must be at least 21 years old to purchase cannabis products from our website. Age verification is required during account registration and may be required upon delivery in some states.'
    },
    {
      id: 'legal-3',
      category: 'legal',
      question: 'Will these products show up on a drug test?',
      answer: 'Products containing THC (including THC-A, Delta-8, and Delta-9) may cause positive results on drug tests. CBD products may also contain trace amounts of THC. If you\'re subject to drug testing, consult with your employer or testing authority before use.'
    },

    // Account & Privacy
    {
      id: 'account-1',
      category: 'account',
      question: 'How do I create an account?',
      answer: 'Click "Register" in the top menu, provide your email, create a password, and verify you\'re 21+. You\'ll receive a confirmation email to activate your account. Having an account allows you to track orders, save addresses, and access exclusive offers.'
    },
    {
      id: 'account-2',
      category: 'account',
      question: 'Is my personal information secure?',
      answer: 'Absolutely. We use industry-standard SSL encryption to protect your data. We never sell or share your personal information with third parties. Your privacy and security are our top priorities. Read our Privacy Policy for complete details.'
    },
    {
      id: 'account-3',
      category: 'account',
      question: 'How do I reset my password?',
      answer: 'Click "Forgot Password" on the login page, enter your email address, and we\'ll send you a secure link to reset your password. The link expires after 24 hours for security purposes.'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Questions', count: faqData.length },
    { id: 'products', name: 'Products', count: faqData.filter(item => item.category === 'products').length },
    { id: 'ordering', name: 'Ordering & Payment', count: faqData.filter(item => item.category === 'ordering').length },
    { id: 'shipping', name: 'Shipping & Delivery', count: faqData.filter(item => item.category === 'shipping').length },
    { id: 'returns', name: 'Returns & Exchanges', count: faqData.filter(item => item.category === 'returns').length },
    { id: 'legal', name: 'Legal & Compliance', count: faqData.filter(item => item.category === 'legal').length },
    { id: 'account', name: 'Account & Privacy', count: faqData.filter(item => item.category === 'account').length }
  ];

  const filteredFAQs = activeCategory === 'all' 
    ? faqData 
    : faqData.filter(item => item.category === activeCategory);

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const isOpen = (id: string) => openItems.includes(id);

  // Generate structured data for FAQ
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": filteredFAQs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead
        title="Frequently Asked Questions - Cannabis Products Help"
        description="Find answers to common questions about our cannabis products, ordering, shipping, returns, and legal compliance. Get help with your hemp-derived product purchases."
        keywords={['cannabis FAQ', 'hemp questions', 'product help', 'ordering help', 'shipping info', 'cannabis legal', 'product support']}
        canonicalUrl="/faq"
        structuredData={faqStructuredData}
      />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about our premium cannabis products, 
            ordering process, shipping, and more. Can't find what you're looking for? 
            <a href="/contact" className="text-primary-600 hover:text-primary-700 ml-1">
              Contact our support team
            </a>.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Category Sidebar */}
            <div className="lg:w-1/4">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
                <nav className="space-y-2">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                        activeCategory === category.id
                          ? 'bg-primary-100 text-primary-700 font-medium'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{category.name}</span>
                        <span className="text-sm text-gray-500">({category.count})</span>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* FAQ Content */}
            <div className="lg:w-3/4">
              <div className="bg-white rounded-lg shadow-md">
                {filteredFAQs.length === 0 ? (
                  <div className="p-8 text-center">
                    <p className="text-gray-600">No questions found in this category.</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200">
                    {filteredFAQs.map((faq, index) => (
                      <div key={faq.id} className="p-6">
                        <button
                          onClick={() => toggleItem(faq.id)}
                          className="w-full text-left flex justify-between items-start group"
                        >
                          <h3 className="text-lg font-medium text-gray-900 group-hover:text-primary-600 transition-colors pr-4">
                            {faq.question}
                          </h3>
                          <div className="flex-shrink-0 ml-4">
                            {isOpen(faq.id) ? (
                              <ChevronUpIcon className="h-5 w-5 text-gray-500 group-hover:text-primary-600" />
                            ) : (
                              <ChevronDownIcon className="h-5 w-5 text-gray-500 group-hover:text-primary-600" />
                            )}
                          </div>
                        </button>
                        
                        {isOpen(faq.id) && (
                          <div className="mt-4 pr-8">
                            <p className="text-gray-700 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Contact CTA */}
              <div className="mt-8 bg-primary-50 rounded-lg p-6 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Still have questions?
                </h3>
                <p className="text-gray-600 mb-4">
                  Our customer support team is here to help you with any questions about our products or services.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/contact"
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
                  >
                    Contact Support
                  </a>
                  <a
                    href="tel:******-123-4567"
                    className="inline-flex items-center px-6 py-3 border border-primary-300 text-base font-medium rounded-md text-primary-700 bg-white hover:bg-primary-50 transition-colors"
                  >
                    Call Us: (*************
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
