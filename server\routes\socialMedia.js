const express = require('express');
const router = express.Router();
const { authenticate, requireAdmin } = require('../middleware/auth');
const { validatePagination } = require('../middleware/validation');
const socialMediaController = require('../controllers/socialMediaController');

// Public routes
// @route   GET /api/social-media/posts
// @desc    Get all social media posts
// @access  Public
router.get('/posts', validatePagination, socialMediaController.getPosts);

// @route   GET /api/social-media/stats
// @desc    Get social media platform statistics
// @access  Public
router.get('/stats', socialMediaController.getStats);

// @route   GET /api/social-media/platforms/:platform/posts
// @desc    Get posts from a specific platform
// @access  Public
router.get('/platforms/:platform/posts', validatePagination, socialMediaController.getPlatformPosts);

// @route   GET /api/social-media/platforms/:platform/stats
// @desc    Get platform-specific statistics
// @access  Public
router.get('/platforms/:platform/stats', socialMediaController.getPlatformStats);

// @route   GET /api/social-media/posts/:postId/engagement
// @desc    Get engagement metrics for a specific post
// @access  Public
router.get('/posts/:postId/engagement', socialMediaController.getPostEngagement);

// @route   GET /api/social-media/posts/search
// @desc    Search social media posts
// @access  Public
router.get('/posts/search', socialMediaController.searchPosts);

// @route   GET /api/social-media/trending
// @desc    Get trending hashtags and topics
// @access  Public
router.get('/trending', socialMediaController.getTrendingTopics);

module.exports = router;
