const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 1. Create roles table
      await queryInterface.createTable('roles', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true
        },
        display_name: {
          type: DataTypes.STRING(100),
          allowNull: false
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        permissions: {
          type: DataTypes.JSON,
          allowNull: false,
          defaultValue: '{}'
        },
        is_active: {
          type: DataTypes.BOOLEAN,
          defaultValue: true
        },
        is_system_role: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        priority: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, { transaction });

      // 2. Add indexes to roles table
      await queryInterface.addIndex('roles', ['name'], { 
        unique: true, 
        transaction 
      });
      await queryInterface.addIndex('roles', ['is_active'], { transaction });
      await queryInterface.addIndex('roles', ['priority'], { transaction });

      // 3. Insert default roles
      await queryInterface.bulkInsert('roles', [
        {
          name: 'admin',
          display_name: 'Administrator',
          description: 'Full system access with all permissions',
          permissions: JSON.stringify({
            products: { view: true, create: true, edit: true, delete: true, manageImages: true, manageCategories: true, manageInventory: true },
            users: { view: true, create: true, edit: true, delete: true, inviteManagers: true, viewCustomers: true },
            orders: { view: true, edit: true, delete: true, manage: true, refund: true, cancel: true },
            analytics: { view: true, export: true, viewReports: true },
            system: { viewLogs: true, viewAuditLogs: true, accessSettings: true, manageRoles: true, systemBackup: true },
            categories: { view: true, create: true, edit: true, delete: true },
            inventory: { view: true, edit: true, manage: true }
          }),
          is_system_role: true,
          priority: 100,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'manager',
          display_name: 'Manager',
          description: 'Limited access for order management and customer service',
          permissions: JSON.stringify({
            products: { view: true, create: false, edit: false, delete: false, manageImages: false, manageCategories: false, manageInventory: false },
            users: { view: true, create: false, edit: false, delete: false, inviteManagers: false, viewCustomers: true },
            orders: { view: true, edit: true, delete: false, manage: true, refund: true, cancel: true },
            analytics: { view: true, export: false, viewReports: true },
            system: { viewLogs: false, viewAuditLogs: false, accessSettings: false, manageRoles: false, systemBackup: false },
            categories: { view: true, create: false, edit: false, delete: false },
            inventory: { view: true, edit: false, manage: false }
          }),
          is_system_role: true,
          priority: 50,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'customer',
          display_name: 'Customer',
          description: 'Standard customer access for shopping and account management',
          permissions: JSON.stringify({
            products: { view: true, create: false, edit: false, delete: false, manageImages: false, manageCategories: false, manageInventory: false },
            users: { view: false, create: false, edit: false, delete: false, inviteManagers: false, viewCustomers: false },
            orders: { view: true, edit: false, delete: false, manage: false, refund: false, cancel: false },
            analytics: { view: false, export: false, viewReports: false },
            system: { viewLogs: false, viewAuditLogs: false, accessSettings: false, manageRoles: false, systemBackup: false },
            categories: { view: true, create: false, edit: false, delete: false },
            inventory: { view: false, edit: false, manage: false }
          }),
          is_system_role: true,
          priority: 10,
          created_at: new Date(),
          updated_at: new Date()
        }
      ], { transaction });

      // 4. Add legacy_role column to users table (keep existing role for migration)
      await queryInterface.addColumn('users', 'legacy_role', {
        type: DataTypes.ENUM('customer', 'admin', 'super_admin', 'manager'),
        allowNull: true
      }, { transaction });

      // 5. Copy existing role data to legacy_role
      await queryInterface.sequelize.query(
        'UPDATE users SET legacy_role = role',
        { transaction }
      );

      // 6. Add role_id column to users table
      await queryInterface.addColumn('users', 'role_id', {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'roles',
          key: 'id'
        }
      }, { transaction });

      // 7. Add foreign key constraint
      await queryInterface.addConstraint('users', {
        fields: ['role_id'],
        type: 'foreign key',
        name: 'fk_users_role_id',
        references: {
          table: 'roles',
          field: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
        transaction
      });

      // 8. Migrate existing role data to role_id
      const [adminRole] = await queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'admin'",
        { transaction }
      );
      const [managerRole] = await queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'manager'",
        { transaction }
      );
      const [customerRole] = await queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'customer'",
        { transaction }
      );

      if (adminRole.length > 0) {
        await queryInterface.sequelize.query(
          `UPDATE users SET role_id = ${adminRole[0].id} WHERE role IN ('admin', 'super_admin')`,
          { transaction }
        );
      }

      if (managerRole.length > 0) {
        await queryInterface.sequelize.query(
          `UPDATE users SET role_id = ${managerRole[0].id} WHERE role = 'manager'`,
          { transaction }
        );
      }

      if (customerRole.length > 0) {
        await queryInterface.sequelize.query(
          `UPDATE users SET role_id = ${customerRole[0].id} WHERE role = 'customer' OR role_id IS NULL`,
          { transaction }
        );
      }

      // 9. Add index for role_id
      await queryInterface.addIndex('users', ['role_id'], { transaction });

      await transaction.commit();
      console.log('✅ RBAC migration completed successfully');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ RBAC migration failed:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove foreign key constraint
      await queryInterface.removeConstraint('users', 'fk_users_role_id', { transaction });
      
      // Remove role_id column
      await queryInterface.removeColumn('users', 'role_id', { transaction });
      
      // Remove legacy_role column
      await queryInterface.removeColumn('users', 'legacy_role', { transaction });
      
      // Drop roles table
      await queryInterface.dropTable('roles', { transaction });
      
      await transaction.commit();
      console.log('✅ RBAC migration rollback completed');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ RBAC migration rollback failed:', error);
      throw error;
    }
  }
};
