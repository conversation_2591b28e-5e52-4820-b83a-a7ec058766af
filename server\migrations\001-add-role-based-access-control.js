const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Check if roles table exists
      const tableExists = await queryInterface.tableExists('roles');

      if (!tableExists) {
        // 1. Create roles table
        await queryInterface.createTable('roles', {
          id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
          },
          name: {
            type: DataTypes.STRING(50),
            allowNull: false,
            unique: true
          },
          display_name: {
            type: DataTypes.STRING(100),
            allowNull: false
          },
          description: {
            type: DataTypes.TEXT,
            allowNull: true
          },
          permissions: {
            type: DataTypes.JSON,
            allowNull: false,
            defaultValue: '{}'
          },
          is_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
          },
          is_system_role: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
          },
          priority: {
            type: DataTypes.INTEGER,
            defaultValue: 0
          },
          created_at: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
          },
          updated_at: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
          }
        }, { transaction });

        // 2. Add indexes to roles table (only if table was created)
        await queryInterface.addIndex('roles', ['name'], {
          unique: true,
          transaction
        });
        await queryInterface.addIndex('roles', ['is_active'], { transaction });
        await queryInterface.addIndex('roles', ['priority'], { transaction });
      } else {
        console.log('ℹ️ Roles table already exists, skipping creation');

        // Check if required columns exist and add them if missing
        const tableDescription = await queryInterface.describeTable('roles');

        if (!tableDescription.description) {
          await queryInterface.addColumn('roles', 'description', {
            type: DataTypes.TEXT,
            allowNull: true
          }, { transaction });
        }

        if (!tableDescription.permissions) {
          await queryInterface.addColumn('roles', 'permissions', {
            type: DataTypes.JSON,
            allowNull: false,
            defaultValue: '{}'
          }, { transaction });
        }

        if (!tableDescription.is_system_role) {
          await queryInterface.addColumn('roles', 'is_system_role', {
            type: DataTypes.BOOLEAN,
            defaultValue: false
          }, { transaction });
        }

        if (!tableDescription.priority) {
          await queryInterface.addColumn('roles', 'priority', {
            type: DataTypes.INTEGER,
            defaultValue: 0
          }, { transaction });
        }
      }

      // 3. Insert default roles (check if they exist first)
      const [existingRoles] = await queryInterface.sequelize.query(
        "SELECT name FROM roles WHERE name IN ('admin', 'manager', 'customer')",
        { transaction }
      );

      const existingRoleNames = existingRoles.map(role => role.name);
      const rolesToInsert = [];

      if (!existingRoleNames.includes('admin')) {
        rolesToInsert.push({
          name: 'admin',
          display_name: 'Administrator',
          description: 'Full system access with all permissions',
          permissions: JSON.stringify({
            products: { view: true, create: true, edit: true, delete: true, manageImages: true, manageCategories: true, manageInventory: true },
            users: { view: true, create: true, edit: true, delete: true, inviteManagers: true, viewCustomers: true },
            orders: { view: true, edit: true, delete: true, manage: true, refund: true, cancel: true },
            analytics: { view: true, export: true, viewReports: true },
            system: { viewLogs: true, viewAuditLogs: true, accessSettings: true, manageRoles: true, systemBackup: true },
            categories: { view: true, create: true, edit: true, delete: true },
            inventory: { view: true, edit: true, manage: true }
          }),
          is_system_role: true,
          priority: 100,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      if (!existingRoleNames.includes('manager')) {
        rolesToInsert.push({
          name: 'manager',
          display_name: 'Manager',
          description: 'Limited access for order management and customer service',
          permissions: JSON.stringify({
            products: { view: true, create: false, edit: false, delete: false, manageImages: false, manageCategories: false, manageInventory: false },
            users: { view: true, create: false, edit: false, delete: false, inviteManagers: false, viewCustomers: true },
            orders: { view: true, edit: true, delete: false, manage: true, refund: true, cancel: true },
            analytics: { view: true, export: false, viewReports: true },
            system: { viewLogs: false, viewAuditLogs: false, accessSettings: false, manageRoles: false, systemBackup: false },
            categories: { view: true, create: false, edit: false, delete: false },
            inventory: { view: true, edit: false, manage: false }
          }),
          is_system_role: true,
          priority: 50,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      if (!existingRoleNames.includes('customer')) {
        rolesToInsert.push({
          name: 'customer',
          display_name: 'Customer',
          description: 'Standard customer access for shopping and account management',
          permissions: JSON.stringify({
            products: { view: true, create: false, edit: false, delete: false, manageImages: false, manageCategories: false, manageInventory: false },
            users: { view: false, create: false, edit: false, delete: false, inviteManagers: false, viewCustomers: false },
            orders: { view: true, edit: false, delete: false, manage: false, refund: false, cancel: false },
            analytics: { view: false, export: false, viewReports: false },
            system: { viewLogs: false, viewAuditLogs: false, accessSettings: false, manageRoles: false, systemBackup: false },
            categories: { view: true, create: false, edit: false, delete: false },
            inventory: { view: false, edit: false, manage: false }
          }),
          is_system_role: true,
          priority: 10,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      // Insert only the roles that don't exist
      if (rolesToInsert.length > 0) {
        await queryInterface.bulkInsert('roles', rolesToInsert, { transaction });
        console.log(`✅ Inserted ${rolesToInsert.length} default roles`);
      } else {
        console.log('ℹ️ All default roles already exist, skipping insertion');
      }

      // 4. Check Users table structure and handle role migration
      const usersTableDescription = await queryInterface.describeTable('Users');

      // Check if we need to add legacy_role column
      if (!usersTableDescription.legacy_role) {
        await queryInterface.addColumn('Users', 'legacy_role', {
          type: DataTypes.ENUM('customer', 'admin', 'super_admin', 'manager'),
          allowNull: true
        }, { transaction });

        // Copy existing role data to legacy_role if role column exists
        if (usersTableDescription.role) {
          await queryInterface.sequelize.query(
            'UPDATE Users SET legacy_role = role',
            { transaction }
          );
        }
      }

      // Check if roleId column exists (our model uses camelCase)
      let roleColumnName = 'roleId';
      if (!usersTableDescription.roleId && !usersTableDescription.role_id) {
        // Neither roleId nor role_id exists, add roleId column
        await queryInterface.addColumn('Users', 'roleId', {
          type: DataTypes.INTEGER,
          allowNull: true,
          references: {
            model: 'roles',
            key: 'id'
          }
        }, { transaction });
      } else if (usersTableDescription.role_id && !usersTableDescription.roleId) {
        // Only role_id exists (snake_case), use that
        roleColumnName = 'role_id';
      }
      // If roleId exists, we don't need to add anything

      // 8. Migrate existing role data to roleId/role_id
      const [adminRole] = await queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'admin'",
        { transaction }
      );
      const [managerRole] = await queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'manager'",
        { transaction }
      );
      const [customerRole] = await queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'customer'",
        { transaction }
      );

      // Migrate role data based on legacy_role or role column
      if (adminRole.length > 0) {
        if (usersTableDescription.legacy_role) {
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${adminRole[0].id} WHERE legacy_role IN ('admin', 'super_admin')`,
            { transaction }
          );
        } else if (usersTableDescription.role) {
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${adminRole[0].id} WHERE role IN ('admin', 'super_admin')`,
            { transaction }
          );
        }
      }

      if (managerRole.length > 0) {
        if (usersTableDescription.legacy_role) {
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${managerRole[0].id} WHERE legacy_role = 'manager'`,
            { transaction }
          );
        } else if (usersTableDescription.role) {
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${managerRole[0].id} WHERE role = 'manager'`,
            { transaction }
          );
        }
      }

      if (customerRole.length > 0) {
        // Set customer role for users without a role or with customer role
        if (usersTableDescription.legacy_role) {
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${customerRole[0].id} WHERE legacy_role = 'customer' OR ${roleColumnName} IS NULL`,
            { transaction }
          );
        } else if (usersTableDescription.role) {
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${customerRole[0].id} WHERE role = 'customer' OR ${roleColumnName} IS NULL`,
            { transaction }
          );
        } else {
          // No legacy role data, set all users without roleId to customer
          await queryInterface.sequelize.query(
            `UPDATE Users SET ${roleColumnName} = ${customerRole[0].id} WHERE ${roleColumnName} IS NULL`,
            { transaction }
          );
        }
      }

      await transaction.commit();
      console.log('✅ RBAC migration completed successfully');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ RBAC migration failed:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove foreign key constraint
      await queryInterface.removeConstraint('users', 'fk_users_role_id', { transaction });
      
      // Remove role_id column
      await queryInterface.removeColumn('users', 'role_id', { transaction });
      
      // Remove legacy_role column
      await queryInterface.removeColumn('users', 'legacy_role', { transaction });
      
      // Drop roles table
      await queryInterface.dropTable('roles', { transaction });
      
      await transaction.commit();
      console.log('✅ RBAC migration rollback completed');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ RBAC migration rollback failed:', error);
      throw error;
    }
  }
};
