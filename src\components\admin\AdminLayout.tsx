import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { logout } from '../../store/slices/authSlice';
import {
  HomeIcon,
  UserGroupIcon,
  ShoppingBagIcon,
  CubeIcon,
  TagIcon,
  StarIcon,
  TicketIcon,
  FolderIcon,
  ChartBarIcon,
  CogIcon,
  ArrowLeftOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: number;
  children?: NavigationItem[];
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>(['dashboard']);
  const [notifications, setNotifications] = useState(3);

  // Navigation items
  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: HomeIcon
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: ChartBarIcon,
      children: [
        { name: 'Overview', href: '/admin/analytics/overview', icon: ChartBarIcon },
        { name: 'Sales', href: '/admin/analytics/sales', icon: ChartBarIcon },
        { name: 'Customers', href: '/admin/analytics/customers', icon: ChartBarIcon },
        { name: 'Products', href: '/admin/analytics/products', icon: ChartBarIcon }
      ]
    },
    {
      name: 'User Management',
      href: '/admin/users',
      icon: UserGroupIcon,
      children: [
        { name: 'All Users', href: '/admin/users', icon: UserGroupIcon },
        { name: 'Add User', href: '/admin/users/new', icon: UserGroupIcon },
        { name: 'Roles & Permissions', href: '/admin/users/roles', icon: UserGroupIcon }
      ]
    },
    {
      name: 'Orders',
      href: '/admin/orders',
      icon: ShoppingBagIcon,
      badge: 5,
      children: [
        { name: 'All Orders', href: '/admin/orders', icon: ShoppingBagIcon },
        { name: 'Pending Orders', href: '/admin/orders?status=pending', icon: ShoppingBagIcon },
        { name: 'Shipped Orders', href: '/admin/orders?status=shipped', icon: ShoppingBagIcon }
      ]
    },
    {
      name: 'Products',
      href: '/admin/products',
      icon: CubeIcon,
      children: [
        { name: 'All Products', href: '/admin/products', icon: CubeIcon },
        { name: 'Add Product', href: '/admin/products/new', icon: CubeIcon },
        { name: 'Inventory', href: '/admin/products/inventory', icon: CubeIcon },
        { name: 'Bulk Import', href: '/admin/products/import', icon: CubeIcon }
      ]
    },
    {
      name: 'Categories',
      href: '/admin/categories',
      icon: FolderIcon,
      children: [
        { name: 'All Categories', href: '/admin/categories', icon: FolderIcon },
        { name: 'Add Category', href: '/admin/categories/new', icon: FolderIcon },
        { name: 'Category Tree', href: '/admin/categories/tree', icon: FolderIcon }
      ]
    },
    {
      name: 'Reviews',
      href: '/admin/reviews',
      icon: StarIcon,
      badge: 12,
      children: [
        { name: 'All Reviews', href: '/admin/reviews', icon: StarIcon },
        { name: 'Pending Reviews', href: '/admin/reviews?status=pending', icon: StarIcon },
        { name: 'Approved Reviews', href: '/admin/reviews?status=approved', icon: StarIcon },
        { name: 'Rejected Reviews', href: '/admin/reviews?status=rejected', icon: StarIcon }
      ]
    },
    {
      name: 'Coupons',
      href: '/admin/coupons',
      icon: TicketIcon,
      children: [
        { name: 'All Coupons', href: '/admin/coupons', icon: TicketIcon },
        { name: 'Add Coupon', href: '/admin/coupons/new', icon: TicketIcon },
        { name: 'Usage Reports', href: '/admin/coupons/reports', icon: TicketIcon }
      ]
    },
    {
      name: 'Customers',
      href: '/admin/customers',
      icon: UserGroupIcon
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: CogIcon,
      children: [
        { name: 'General', href: '/admin/settings/general', icon: CogIcon },
        { name: 'Email', href: '/admin/settings/email', icon: CogIcon },
        { name: 'Payment', href: '/admin/settings/payment', icon: CogIcon },
        { name: 'Shipping', href: '/admin/settings/shipping', icon: CogIcon }
      ]
    }
  ];

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(item => item !== itemName)
        : [...prev, itemName]
    );
  };

  const isCurrentPath = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const isParentActive = (item: NavigationItem) => {
    if (isCurrentPath(item.href)) return true;
    if (item.children) {
      return item.children.some(child => isCurrentPath(child.href));
    }
    return false;
  };

  // Auto-expand active parent items
  useEffect(() => {
    navigation.forEach(item => {
      if (isParentActive(item) && item.children && !expandedItems.includes(item.name)) {
        setExpandedItems(prev => [...prev, item.name]);
      }
    });
  }, [location.pathname]);

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isActive = isCurrentPath(item.href);
    const isExpanded = expandedItems.includes(item.name);
    const hasChildren = item.children && item.children.length > 0;
    const isParentOfActive = isParentActive(item);

    return (
      <div key={item.name}>
        <div
          className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer ${
            level === 0 ? 'mx-3' : 'mx-6'
          } ${
            isActive
              ? 'bg-indigo-100 text-indigo-900'
              : isParentOfActive && level === 0
              ? 'bg-indigo-50 text-indigo-700'
              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
          }`}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.name);
            } else {
              navigate(item.href);
              setSidebarOpen(false);
            }
          }}
        >
          <item.icon
            className={`mr-3 flex-shrink-0 h-5 w-5 ${
              isActive
                ? 'text-indigo-500'
                : isParentOfActive && level === 0
                ? 'text-indigo-400'
                : 'text-gray-400 group-hover:text-gray-500'
            }`}
          />
          <span className="flex-1">{item.name}</span>
          
          {item.badge && (
            <span className="ml-3 inline-block py-0.5 px-2 text-xs font-medium rounded-full bg-red-100 text-red-800">
              {item.badge}
            </span>
          )}
          
          {hasChildren && (
            <div className="ml-2">
              {isExpanded ? (
                <ChevronDownIcon className="h-4 w-4" />
              ) : (
                <ChevronRightIcon className="h-4 w-4" />
              )}
            </div>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>
          <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
            <div className="flex-shrink-0 flex items-center px-4">
              <img className="h-8 w-auto" src="/Nirvana_logo.png" alt="Nirvana Organics" />
              <span className="ml-2 text-xl font-bold text-gray-900">Admin</span>
            </div>
            <nav className="mt-5 space-y-1">
              {navigation.map(item => renderNavigationItem(item))}
            </nav>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col h-0 flex-1 border-r border-gray-200 bg-white">
            <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
              <div className="flex items-center flex-shrink-0 px-4">
                <img className="h-8 w-auto" src="/Nirvana_logo.png" alt="Nirvana Organics" />
                <span className="ml-2 text-xl font-bold text-gray-900">Admin</span>
              </div>
              <nav className="mt-5 flex-1 space-y-1">
                {navigation.map(item => renderNavigationItem(item))}
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Top navigation */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          
          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex">
              {/* Breadcrumb could go here */}
            </div>
            
            <div className="ml-4 flex items-center md:ml-6">
              {/* Notifications */}
              <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <BellIcon className="h-6 w-6" />
                {notifications > 0 && (
                  <span className="absolute -mt-2 -mr-2 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {notifications}
                  </span>
                )}
              </button>

              {/* Profile dropdown */}
              <div className="ml-3 relative">
                <div className="flex items-center">
                  <UserCircleIcon className="h-8 w-8 text-gray-400" />
                  <div className="ml-2">
                    <div className="text-sm font-medium text-gray-700">
                      {user?.firstName} {user?.lastName}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">{user?.role}</div>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="ml-3 p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    title="Logout"
                  >
                    <ArrowLeftOnRectangleIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
