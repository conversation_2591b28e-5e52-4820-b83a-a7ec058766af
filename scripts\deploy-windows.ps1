# ============================================================================
# Nirvana Backend Deployment Script for Windows
# Deploys the backend to root@srv928821 from Windows
# ============================================================================

$REMOTE_SERVER = "root@srv928821"
$REMOTE_PATH = "/var/www/nirvana-backend"
$LOCAL_PATH = "."

Write-Host "🚀 Starting Nirvana Backend Deployment" -ForegroundColor Green
Write-Host "📡 Target Server: $REMOTE_SERVER" -ForegroundColor Cyan
Write-Host "📁 Remote Path: $REMOTE_PATH" -ForegroundColor Cyan
Write-Host ""

# Step 1: Prepare deployment package
Write-Host "1️⃣ Preparing deployment package..." -ForegroundColor Yellow
node scripts/deploy-to-remote-server.js

# Step 2: Create remote directory
Write-Host "2️⃣ Creating remote directory..." -ForegroundColor Yellow
ssh $REMOTE_SERVER "mkdir -p $REMOTE_PATH"

# Step 3: Copy files using SCP (since rsync might not be available on Windows)
Write-Host "3️⃣ Copying files to remote server..." -ForegroundColor Yellow

# Copy main files
scp -r server/ "${REMOTE_SERVER}:${REMOTE_PATH}/"
scp package.json "${REMOTE_SERVER}:${REMOTE_PATH}/"
scp package-lock.json "${REMOTE_SERVER}:${REMOTE_PATH}/"
scp .env.production "${REMOTE_SERVER}:${REMOTE_PATH}/"
scp -r scripts/ "${REMOTE_SERVER}:${REMOTE_PATH}/"
scp -r uploads/ "${REMOTE_SERVER}:${REMOTE_PATH}/" 2>$null
scp -r public/ "${REMOTE_SERVER}:${REMOTE_PATH}/" 2>$null

Write-Host "✅ Files copied successfully" -ForegroundColor Green

# Step 4: Setup on remote server
Write-Host "4️⃣ Setting up on remote server..." -ForegroundColor Yellow

$setupCommands = @"
cd $REMOTE_PATH

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

# Install dependencies
echo "Installing npm dependencies..."
npm install --production

# Copy production environment
if [ -f .env.production ]; then
    cp .env.production .env
    echo "✅ Production environment configured"
fi

# Create necessary directories
mkdir -p uploads logs public/uploads
chmod 755 uploads logs public/uploads

# Install PM2 globally
npm install -g pm2

# Stop existing process if running
pm2 stop nirvana-backend 2>/dev/null || true
pm2 delete nirvana-backend 2>/dev/null || true

# Start the application
pm2 start server/index.js --name nirvana-backend

# Setup PM2 to start on boot
pm2 startup
pm2 save

echo "✅ Backend server started with PM2"
"@

ssh $REMOTE_SERVER $setupCommands

Write-Host "5️⃣ Checking deployment status..." -ForegroundColor Yellow
ssh $REMOTE_SERVER "cd $REMOTE_PATH && pm2 status"

Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Update .env file on the server with your production settings"
Write-Host "2. Configure your domain settings"
Write-Host "3. Setup SSL certificate"
Write-Host "4. Test your API endpoints"
Write-Host ""
Write-Host "🔧 Useful Commands:" -ForegroundColor Cyan
Write-Host "ssh $REMOTE_SERVER 'pm2 status'"
Write-Host "ssh $REMOTE_SERVER 'pm2 logs nirvana-backend'"
Write-Host "ssh $REMOTE_SERVER 'pm2 restart nirvana-backend'"
