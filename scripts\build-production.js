#!/usr/bin/env node

/**
 * Production Build Script
 * Optimizes and builds the frontend for production deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏗️  Building Nirvana Organics for Production...\n');

// Configuration
const config = {
  buildDir: 'dist',
  adminBuildDir: 'dist-admin',
  assetsDir: 'public',
  optimizationLevel: 'aggressive'
};

// Utility functions
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

function execCommand(command, description) {
  log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed`, 'success');
    return true;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'error');
    return false;
  }
}

// Pre-build checks
function preBuildChecks() {
  log('🔍 Running pre-build checks...');
  
  // Check Node.js version
  const nodeVersion = process.version;
  log(`Node.js version: ${nodeVersion}`);
  
  // Check if package.json exists
  if (!fs.existsSync('package.json')) {
    log('❌ package.json not found', 'error');
    process.exit(1);
  }
  
  // Check if vite.config.ts exists
  if (!fs.existsSync('vite.config.ts')) {
    log('❌ vite.config.ts not found', 'error');
    process.exit(1);
  }
  
  log('✅ Pre-build checks passed', 'success');
}

// Clean previous builds
function cleanBuild() {
  log('🧹 Cleaning previous builds...');
  
  const dirsToClean = [config.buildDir, config.adminBuildDir];
  
  dirsToClean.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      log(`   Removed: ${dir}`);
    }
  });
  
  log('✅ Build directories cleaned', 'success');
}

// Install dependencies
function installDependencies() {
  return execCommand('npm ci --production', 'Installing production dependencies');
}

// Type checking
function typeCheck() {
  if (fs.existsSync('tsconfig.json')) {
    return execCommand('npm run type-check', 'Running TypeScript type checking');
  } else {
    log('⚠️  TypeScript config not found, skipping type check', 'warning');
    return true;
  }
}

// Linting
function runLinting() {
  return execCommand('npm run lint', 'Running ESLint');
}

// Build frontend
function buildFrontend() {
  log('🏗️  Building main frontend application...');
  
  // Set production environment variables
  process.env.NODE_ENV = 'production';
  process.env.VITE_APP_ENV = 'production';
  
  return execCommand('npm run build:prod', 'Building frontend for production');
}

// Build admin panel
function buildAdmin() {
  log('🏗️  Building admin panel...');
  
  if (fs.existsSync('vite.admin.config.ts')) {
    return execCommand('npm run build:admin', 'Building admin panel');
  } else {
    log('⚠️  Admin config not found, skipping admin build', 'warning');
    return true;
  }
}

// Optimize assets
function optimizeAssets() {
  log('🎨 Optimizing static assets...');
  
  // Check if build directory exists
  if (!fs.existsSync(config.buildDir)) {
    log('❌ Build directory not found', 'error');
    return false;
  }
  
  // Optimize images (if imagemin is available)
  try {
    execSync('npm list imagemin', { stdio: 'ignore' });
    log('   Optimizing images...');
    // Add image optimization logic here if needed
  } catch (error) {
    log('   ⚠️  imagemin not available, skipping image optimization', 'warning');
  }
  
  // Generate asset manifest
  generateAssetManifest();
  
  log('✅ Asset optimization completed', 'success');
  return true;
}

// Generate asset manifest
function generateAssetManifest() {
  log('📋 Generating asset manifest...');
  
  const manifest = {
    buildTime: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: 'production',
    assets: {}
  };
  
  // Scan build directory for assets
  function scanDirectory(dir, basePath = '') {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativePath = path.join(basePath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath, relativePath);
      } else {
        const ext = path.extname(item);
        const size = stat.size;
        
        if (!manifest.assets[ext]) {
          manifest.assets[ext] = [];
        }
        
        manifest.assets[ext].push({
          path: relativePath.replace(/\\/g, '/'),
          size: size,
          sizeFormatted: formatBytes(size)
        });
      }
    });
  }
  
  if (fs.existsSync(config.buildDir)) {
    scanDirectory(config.buildDir);
  }
  
  // Write manifest
  const manifestPath = path.join(config.buildDir, 'manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  
  log(`   Manifest written to: ${manifestPath}`);
}

// Format bytes utility
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Generate build report
function generateBuildReport() {
  log('📊 Generating build report...');
  
  const report = {
    buildTime: new Date().toISOString(),
    nodeVersion: process.version,
    environment: process.env.NODE_ENV,
    success: true,
    builds: {}
  };
  
  // Analyze main build
  if (fs.existsSync(config.buildDir)) {
    const buildStats = getBuildStats(config.buildDir);
    report.builds.main = buildStats;
  }
  
  // Analyze admin build
  if (fs.existsSync(config.adminBuildDir)) {
    const adminStats = getBuildStats(config.adminBuildDir);
    report.builds.admin = adminStats;
  }
  
  // Write report
  const reportPath = 'build-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`✅ Build report generated: ${reportPath}`, 'success');
  
  // Display summary
  displayBuildSummary(report);
}

// Get build statistics
function getBuildStats(buildDir) {
  const stats = {
    totalFiles: 0,
    totalSize: 0,
    fileTypes: {}
  };
  
  function scanDir(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDir(fullPath);
      } else {
        stats.totalFiles++;
        stats.totalSize += stat.size;
        
        const ext = path.extname(item) || 'no-extension';
        if (!stats.fileTypes[ext]) {
          stats.fileTypes[ext] = { count: 0, size: 0 };
        }
        stats.fileTypes[ext].count++;
        stats.fileTypes[ext].size += stat.size;
      }
    });
  }
  
  scanDir(buildDir);
  
  return stats;
}

// Display build summary
function displayBuildSummary(report) {
  log('\n🎉 Build Summary', 'success');
  log('================');
  
  Object.entries(report.builds).forEach(([buildName, stats]) => {
    log(`\n📦 ${buildName.toUpperCase()} Build:`);
    log(`   Files: ${stats.totalFiles}`);
    log(`   Total Size: ${formatBytes(stats.totalSize)}`);
    
    log('   File Types:');
    Object.entries(stats.fileTypes).forEach(([ext, data]) => {
      log(`     ${ext}: ${data.count} files (${formatBytes(data.size)})`);
    });
  });
  
  log('\n✅ Production build completed successfully!', 'success');
}

// Main build process
async function main() {
  try {
    preBuildChecks();
    cleanBuild();
    
    if (!installDependencies()) process.exit(1);
    if (!typeCheck()) process.exit(1);
    if (!runLinting()) process.exit(1);
    if (!buildFrontend()) process.exit(1);
    if (!buildAdmin()) process.exit(1);
    if (!optimizeAssets()) process.exit(1);
    
    generateBuildReport();
    
    log('\n🚀 Ready for deployment!', 'success');
    
  } catch (error) {
    log(`❌ Build failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run the build
main();
