import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  memoryUsage?: number;
  renderTime?: number;
  componentMounts?: number;
}

interface UsePerformanceMonitorOptions {
  enableMemoryMonitoring?: boolean;
  enableRenderTimeTracking?: boolean;
  logInterval?: number;
  onMemoryThreshold?: (usage: number) => void;
  memoryThreshold?: number; // MB
}

export const usePerformanceMonitor = (
  componentName: string,
  options: UsePerformanceMonitorOptions = {}
) => {
  const {
    enableMemoryMonitoring = false, // Disabled to prevent loading issues
    enableRenderTimeTracking = false, // Disabled to prevent loading issues
    logInterval = 30000, // 30 seconds
    onMemoryThreshold,
    memoryThreshold = 100 // 100MB
  } = options;

  const mountTimeRef = useRef<number>(Date.now());
  const renderCountRef = useRef<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const metricsRef = useRef<PerformanceMetrics>({});

  // Track render time
  const trackRenderStart = useCallback(() => {
    if (enableRenderTimeTracking) {
      return performance.now();
    }
    return 0;
  }, [enableRenderTimeTracking]);

  const trackRenderEnd = useCallback((startTime: number) => {
    if (enableRenderTimeTracking && startTime > 0) {
      const renderTime = performance.now() - startTime;
      metricsRef.current.renderTime = renderTime;
      
      // Log slow renders in development
      if (process.env.NODE_ENV === 'development' && renderTime > 16) {
        console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
      }
    }
  }, [componentName, enableRenderTimeTracking]);

  // Memory monitoring
  const checkMemoryUsage = useCallback(() => {
    if (enableMemoryMonitoring && 'memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      
      metricsRef.current.memoryUsage = usedMB;
      
      // Check threshold
      if (onMemoryThreshold && usedMB > memoryThreshold) {
        onMemoryThreshold(usedMB);
      }
      
      // Log memory usage in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`Memory usage in ${componentName}: ${usedMB.toFixed(2)}MB`);
      }
    }
  }, [componentName, enableMemoryMonitoring, onMemoryThreshold, memoryThreshold]);

  // Component lifecycle tracking
  useEffect(() => {
    renderCountRef.current += 1;
    metricsRef.current.componentMounts = renderCountRef.current;
    
    // Start performance monitoring
    if (enableMemoryMonitoring) {
      checkMemoryUsage();
      intervalRef.current = setInterval(checkMemoryUsage, logInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [checkMemoryUsage, enableMemoryMonitoring, logInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      // Log component lifecycle in development
      if (process.env.NODE_ENV === 'development') {
        const lifeTime = Date.now() - mountTimeRef.current;
        console.log(`${componentName} unmounted after ${lifeTime}ms, ${renderCountRef.current} renders`);
      }
    };
  }, [componentName]);

  // Force garbage collection (development only)
  const forceGarbageCollection = useCallback(() => {
    if (process.env.NODE_ENV === 'development' && 'gc' in window) {
      (window as any).gc();
      console.log('Forced garbage collection');
    }
  }, []);

  // Get current metrics
  const getMetrics = useCallback((): PerformanceMetrics => {
    return { ...metricsRef.current };
  }, []);

  // Performance optimization helpers
  const optimizeImages = useCallback(() => {
    // Remove unused images from DOM
    const images = document.querySelectorAll('img[data-loaded="false"]');
    images.forEach(img => {
      if (!img.getBoundingClientRect().top) {
        img.remove();
      }
    });
  }, []);

  const clearEventListeners = useCallback(() => {
    // Helper to clear event listeners that might cause memory leaks
    const events = ['scroll', 'resize', 'mousemove', 'touchmove'];
    events.forEach(event => {
      const listeners = (window as any)._eventListeners?.[event] || [];
      listeners.forEach((listener: EventListener) => {
        window.removeEventListener(event, listener);
      });
    });
  }, []);

  return {
    trackRenderStart,
    trackRenderEnd,
    checkMemoryUsage,
    forceGarbageCollection,
    getMetrics,
    optimizeImages,
    clearEventListeners,
    renderCount: renderCountRef.current
  };
};

// Performance monitoring wrapper component
export const withPerformanceMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) => {
  const WithPerformanceMonitoring = (props: P) => {
    const name = componentName || WrappedComponent.displayName || WrappedComponent.name;
    const { trackRenderStart, trackRenderEnd } = usePerformanceMonitor(name);
    
    useEffect(() => {
      const startTime = trackRenderStart();
      return () => trackRenderEnd(startTime);
    });

    return <WrappedComponent {...props} />;
  };

  WithPerformanceMonitoring.displayName = `withPerformanceMonitoring(${componentName || WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithPerformanceMonitoring;
};
