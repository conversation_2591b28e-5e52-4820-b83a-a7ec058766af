name: Deploy to <PERSON><PERSON>

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18.x'
  CACHE_KEY_PREFIX: 'nirvana-organics'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: nirvana_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.npm
          node_modules
          server/node_modules
        key: ${{ env.CACHE_KEY_PREFIX }}-${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ env.CACHE_KEY_PREFIX }}-${{ runner.os }}-node-

    - name: Install dependencies
      run: |
        npm ci
        cd server && npm ci

    - name: Run frontend tests
      run: npm run test:ci
      env:
        CI: true

    - name: Run backend tests
      run: |
        cd server
        npm run test
      env:
        CI: true
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 3306
        DB_NAME: nirvana_test
        DB_USER: root
        DB_PASSWORD: test_password
        JWT_SECRET: test-jwt-secret-for-ci
        JWT_REFRESH_SECRET: test-refresh-secret-for-ci

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          coverage/
          server/coverage/
          test-results/

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Restore dependencies cache
      uses: actions/cache@v3
      with:
        path: |
          ~/.npm
          node_modules
          server/node_modules
        key: ${{ env.CACHE_KEY_PREFIX }}-${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

    - name: Install dependencies
      run: |
        npm ci
        cd server && npm ci

    - name: Build application
      run: npm run build
      env:
        NODE_ENV: production
        CI: true

    - name: Create deployment package
      run: |
        chmod +x scripts/deploy.sh
        ./scripts/deploy.sh --skip-tests

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-${{ github.sha }}
        path: dist/
        retention-days: 30

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: staging

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-${{ github.sha }}
        path: dist/

    - name: Deploy to staging server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        port: ${{ secrets.STAGING_PORT || 22 }}
        script: |
          # Create backup
          if [ -d "/home/<USER>/public_html" ]; then
            cp -r /home/<USER>/public_html /home/<USER>/backup_$(date +%Y%m%d_%H%M%S)
          fi
          
          # Create directory if it doesn't exist
          mkdir -p /home/<USER>/public_html
          
          # Stop existing application
          pm2 stop nirvana-organics-staging || true

    - name: Upload files to staging
      uses: appleboy/scp-action@v0.1.4
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        port: ${{ secrets.STAGING_PORT || 22 }}
        source: "dist/*"
        target: "/home/<USER>/public_html/"
        strip_components: 1

    - name: Start staging application
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        port: ${{ secrets.STAGING_PORT || 22 }}
        script: |
          cd /home/<USER>/public_html
          
          # Set environment variables
          echo "NODE_ENV=staging" > .env
          echo "DB_HOST=${{ secrets.STAGING_DB_HOST }}" >> .env
          echo "DB_NAME=${{ secrets.STAGING_DB_NAME }}" >> .env
          echo "DB_USER=${{ secrets.STAGING_DB_USER }}" >> .env
          echo "DB_PASSWORD=${{ secrets.STAGING_DB_PASSWORD }}" >> .env
          echo "JWT_SECRET=${{ secrets.JWT_SECRET }}" >> .env
          echo "JWT_REFRESH_SECRET=${{ secrets.JWT_REFRESH_SECRET }}" >> .env
          echo "SQUARE_ACCESS_TOKEN=${{ secrets.STAGING_SQUARE_ACCESS_TOKEN }}" >> .env
          echo "SQUARE_ENVIRONMENT=sandbox" >> .env
          echo "SQUARE_LOCATION_ID=${{ secrets.SQUARE_LOCATION_ID }}" >> .env
          
          # Start application
          chmod +x start.sh
          pm2 start ecosystem.config.js --env staging || ./start.sh

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/production'
    environment: production

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-${{ github.sha }}
        path: dist/

    - name: Deploy to production server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USERNAME }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        port: ${{ secrets.PRODUCTION_PORT || 22 }}
        script: |
          # Create backup
          if [ -d "/home/<USER>/public_html" ]; then
            cp -r /home/<USER>/public_html /home/<USER>/backup_$(date +%Y%m%d_%H%M%S)
          fi
          
          # Create directory if it doesn't exist
          mkdir -p /home/<USER>/public_html
          
          # Stop existing application gracefully
          pm2 stop nirvana-organics || true

    - name: Upload files to production
      uses: appleboy/scp-action@v0.1.4
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USERNAME }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        port: ${{ secrets.PRODUCTION_PORT || 22 }}
        source: "dist/*"
        target: "/home/<USER>/public_html/"
        strip_components: 1

    - name: Start production application
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USERNAME }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        port: ${{ secrets.PRODUCTION_PORT || 22 }}
        script: |
          cd /home/<USER>/public_html
          
          # Set environment variables
          echo "NODE_ENV=production" > .env
          echo "DB_HOST=${{ secrets.PRODUCTION_DB_HOST }}" >> .env
          echo "DB_NAME=${{ secrets.PRODUCTION_DB_NAME }}" >> .env
          echo "DB_USER=${{ secrets.PRODUCTION_DB_USER }}" >> .env
          echo "DB_PASSWORD=${{ secrets.PRODUCTION_DB_PASSWORD }}" >> .env
          echo "JWT_SECRET=${{ secrets.JWT_SECRET }}" >> .env
          echo "JWT_REFRESH_SECRET=${{ secrets.JWT_REFRESH_SECRET }}" >> .env
          echo "SQUARE_ACCESS_TOKEN=${{ secrets.PRODUCTION_SQUARE_ACCESS_TOKEN }}" >> .env
          echo "SQUARE_ENVIRONMENT=production" >> .env
          echo "SQUARE_LOCATION_ID=${{ secrets.SQUARE_LOCATION_ID }}" >> .env
          echo "EMAIL_HOST=${{ secrets.EMAIL_HOST }}" >> .env
          echo "EMAIL_USER=${{ secrets.EMAIL_USER }}" >> .env
          echo "EMAIL_PASSWORD=${{ secrets.EMAIL_PASSWORD }}" >> .env
          
          # Start application
          chmod +x start.sh
          pm2 start ecosystem.config.js --env production || ./start.sh
          
          # Save PM2 configuration
          pm2 save

    - name: Health check
      run: |
        sleep 30
        curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1

    - name: Notify deployment success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "✅ Production deployment successful!"
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

    - name: Notify deployment failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ Production deployment failed!"
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  cleanup:
    name: Cleanup Old Artifacts
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
    - name: Delete old artifacts
      uses: actions/github-script@v6
      with:
        script: |
          const artifacts = await github.rest.actions.listArtifactsForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
          });
          
          const oldArtifacts = artifacts.data.artifacts
            .filter(artifact => artifact.name.startsWith('build-'))
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
            .slice(5); // Keep only the 5 most recent builds
          
          for (const artifact of oldArtifacts) {
            await github.rest.actions.deleteArtifact({
              owner: context.repo.owner,
              repo: context.repo.repo,
              artifact_id: artifact.id,
            });
            console.log(`Deleted artifact: ${artifact.name}`);
          }
