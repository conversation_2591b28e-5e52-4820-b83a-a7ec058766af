import React, { useState } from 'react';
import { ProductFilters as ProductFiltersType } from '../../types';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface ProductFiltersProps {
  filters: ProductFiltersType;
  onFilterChange: (filters: Partial<ProductFiltersType>) => void;
  onClearFilters: () => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters
}) => {
  const [expandedSections, setExpandedSections] = useState({
    cannabinoid: true,
    strain: true,
    price: true,
    availability: true,
    features: true
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const cannabinoids = [
    { value: 'THC-A', label: 'THC-A', count: 45 },
    { value: 'CBD', label: 'CBD', count: 32 },
    { value: 'Delta-8', label: 'Delta-8', count: 28 },
    { value: 'Delta-9', label: 'Delta-9', count: 21 },
    { value: 'THC-P', label: 'THC-P', count: 15 }
  ];

  const strains = [
    { value: 'Sativa', label: 'Sativa', count: 52 },
    { value: 'Indica', label: 'Indica', count: 48 },
    { value: 'Hybrid', label: 'Hybrid', count: 41 }
  ];

  const handlePriceChange = (field: 'minPrice' | 'maxPrice', value: string) => {
    const numValue = value === '' ? undefined : parseFloat(value);
    onFilterChange({ [field]: numValue });
  };

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === 'page' || key === 'limit' || key === 'sortBy' || key === 'sortOrder') {
      return false;
    }
    return value !== undefined && value !== null && value !== '';
  });

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
            Search Products
          </label>
          <input
            type="text"
            id="search"
            value={filters.search || ''}
            onChange={(e) => onFilterChange({ search: e.target.value || undefined })}
            placeholder="Search by name, description..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        {/* Cannabinoid Filter */}
        <div>
          <button
            onClick={() => toggleSection('cannabinoid')}
            className="flex items-center justify-between w-full text-left"
          >
            <span className="text-sm font-medium text-gray-900">Cannabinoid</span>
            {expandedSections.cannabinoid ? (
              <ChevronUpIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.cannabinoid && (
            <div className="mt-3 space-y-2">
              {cannabinoids.map((cannabinoid) => (
                <label key={cannabinoid.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.cannabinoid === cannabinoid.value}
                    onChange={(e) => {
                      onFilterChange({
                        cannabinoid: e.target.checked ? cannabinoid.value : undefined
                      });
                    }}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {cannabinoid.label}
                  </span>
                  <span className="ml-auto text-xs text-gray-500">
                    ({cannabinoid.count})
                  </span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Strain Filter */}
        <div>
          <button
            onClick={() => toggleSection('strain')}
            className="flex items-center justify-between w-full text-left"
          >
            <span className="text-sm font-medium text-gray-900">Strain Type</span>
            {expandedSections.strain ? (
              <ChevronUpIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.strain && (
            <div className="mt-3 space-y-2">
              {strains.map((strain) => (
                <label key={strain.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.strain === strain.value}
                    onChange={(e) => {
                      onFilterChange({
                        strain: e.target.checked ? strain.value : undefined
                      });
                    }}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {strain.label}
                  </span>
                  <span className="ml-auto text-xs text-gray-500">
                    ({strain.count})
                  </span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Price Range */}
        <div>
          <button
            onClick={() => toggleSection('price')}
            className="flex items-center justify-between w-full text-left"
          >
            <span className="text-sm font-medium text-gray-900">Price Range</span>
            {expandedSections.price ? (
              <ChevronUpIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.price && (
            <div className="mt-3 space-y-3">
              <div className="flex items-center space-x-2">
                <div className="flex-1">
                  <label htmlFor="minPrice" className="block text-xs text-gray-600 mb-1">
                    Min Price
                  </label>
                  <input
                    type="number"
                    id="minPrice"
                    value={filters.minPrice || ''}
                    onChange={(e) => handlePriceChange('minPrice', e.target.value)}
                    placeholder="$0"
                    min="0"
                    step="0.01"
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                </div>
                <span className="text-gray-500 mt-5">-</span>
                <div className="flex-1">
                  <label htmlFor="maxPrice" className="block text-xs text-gray-600 mb-1">
                    Max Price
                  </label>
                  <input
                    type="number"
                    id="maxPrice"
                    value={filters.maxPrice || ''}
                    onChange={(e) => handlePriceChange('maxPrice', e.target.value)}
                    placeholder="$999"
                    min="0"
                    step="0.01"
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                </div>
              </div>
              
              {/* Quick Price Filters */}
              <div className="flex flex-wrap gap-2">
                {[
                  { label: 'Under $25', min: undefined, max: 25 },
                  { label: '$25-$50', min: 25, max: 50 },
                  { label: '$50-$100', min: 50, max: 100 },
                  { label: 'Over $100', min: 100, max: undefined }
                ].map((range) => (
                  <button
                    key={range.label}
                    onClick={() => onFilterChange({ minPrice: range.min, maxPrice: range.max })}
                    className="text-xs px-2 py-1 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                  >
                    {range.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Availability */}
        <div>
          <button
            onClick={() => toggleSection('availability')}
            className="flex items-center justify-between w-full text-left"
          >
            <span className="text-sm font-medium text-gray-900">Availability</span>
            {expandedSections.availability ? (
              <ChevronUpIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.availability && (
            <div className="mt-3 space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.inStock || false}
                  onChange={(e) => onFilterChange({ inStock: e.target.checked || undefined })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">In Stock Only</span>
              </label>
            </div>
          )}
        </div>

        {/* Features */}
        <div>
          <button
            onClick={() => toggleSection('features')}
            className="flex items-center justify-between w-full text-left"
          >
            <span className="text-sm font-medium text-gray-900">Features</span>
            {expandedSections.features ? (
              <ChevronUpIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.features && (
            <div className="mt-3 space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.featured || false}
                  onChange={(e) => onFilterChange({ featured: e.target.checked || undefined })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Featured Products</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.bestSeller || false}
                  onChange={(e) => onFilterChange({ bestSeller: e.target.checked || undefined })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Best Sellers</span>
              </label>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductFilters;
