# Square Payment Production Setup Guide

## 🚨 CRITICAL: Production vs Sandbox

**IMPORTANT**: Your current configuration is using SANDBOX credentials. These MUST be changed to production credentials before going live.

### Current Configuration (SANDBOX - DO NOT USE IN PRODUCTION)
```
SQUARE_APPLICATION_ID=sandbox-sq0idb-iKZkLt1rHUkKu9X4L7LEtA
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_LOCATION_ID=LWBM41BAWMRQS
SQUARE_ENVIRONMENT=sandbox
```

## 📋 Steps to Configure Production Square API

### 1. Access Square Developer Dashboard
1. Go to [Square Developer Dashboard](https://developer.squareup.com/apps)
2. Log in with your Square business account
3. Select your application or create a new one for production

### 2. Get Production Credentials

#### Application ID
1. In your app dashboard, go to the **Credentials** tab
2. Under **Production**, copy the **Application ID**
3. Update `SQUARE_APPLICATION_ID` in your `.env.production` file

#### Access Token
1. In the same **Credentials** tab under **Production**
2. Copy the **Access Token**
3. Update `SQUARE_ACCESS_TOKEN` in your `.env.production` file

#### Location ID
1. Go to the **Locations** tab in your Square dashboard
2. Find your business location
3. Copy the **Location ID**
4. Update `SQUARE_LOCATION_ID` in your `.env.production` file

### 3. Update Environment Configuration

Replace the sandbox values in `.env.production`:

```bash
# Square Payment Service - PRODUCTION CREDENTIALS
SQUARE_APPLICATION_ID=sq0idp-YOUR_PRODUCTION_APPLICATION_ID
SQUARE_ACCESS_TOKEN=YOUR_PRODUCTION_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_PRODUCTION_LOCATION_ID
SQUARE_ENVIRONMENT=production
SQUARE_WEBHOOK_SIGNATURE_KEY=YOUR_PRODUCTION_WEBHOOK_SIGNATURE_KEY
```

### 4. Configure Webhooks (Optional but Recommended)

#### Create Webhook Endpoint
1. In Square Developer Dashboard, go to **Webhooks**
2. Click **Create Webhook**
3. Set the endpoint URL: `https://shopnirvanaorganics.com/api/webhooks/square`
4. Select events you want to listen to:
   - `payment.created`
   - `payment.updated`
   - `order.created`
   - `order.updated`
   - `order.fulfilled`

#### Get Webhook Signature Key
1. After creating the webhook, copy the **Signature Key**
2. Update `SQUARE_WEBHOOK_SIGNATURE_KEY` in your environment file

### 5. Test Production Configuration

Run the validation script to test your production Square configuration:

```bash
npm run setup:services
```

This will:
- Verify your credentials are valid
- Check that you're using production environment
- Test API connectivity
- Validate location access

### 6. Important Security Notes

#### ✅ DO's
- Use production credentials only in production environment
- Keep access tokens secure and never commit to version control
- Regularly rotate access tokens
- Monitor transaction logs
- Set up webhook endpoints for real-time updates

#### ❌ DON'Ts
- Never use sandbox credentials in production
- Don't hardcode credentials in source code
- Don't share access tokens
- Don't ignore webhook signature validation

### 7. Testing Checklist

Before going live, test the following:

- [ ] **Payment Processing**: Test credit card payments
- [ ] **Refunds**: Test refund functionality
- [ ] **Webhooks**: Verify webhook events are received
- [ ] **Error Handling**: Test declined cards and errors
- [ ] **Location Verification**: Ensure correct business location
- [ ] **Currency**: Verify correct currency settings
- [ ] **Tax Calculation**: Test tax calculations if applicable

### 8. Monitoring and Maintenance

#### Transaction Monitoring
- Monitor transactions in Square Dashboard
- Set up alerts for failed payments
- Review transaction reports regularly

#### Error Monitoring
- Monitor application logs for Square API errors
- Set up alerts for API failures
- Track payment success rates

### 9. Common Issues and Solutions

#### Issue: "Unauthorized" Error
**Solution**: Check that your access token is valid and for the correct environment

#### Issue: "Location not found" Error
**Solution**: Verify the location ID matches your business location in Square

#### Issue: "Invalid application" Error
**Solution**: Ensure application ID is correct and app is approved for production

#### Issue: Webhook not receiving events
**Solution**: 
- Check webhook URL is accessible
- Verify SSL certificate is valid
- Check webhook signature validation

### 10. Support and Resources

- [Square Developer Documentation](https://developer.squareup.com/docs)
- [Square API Reference](https://developer.squareup.com/reference/square)
- [Square Developer Community](https://developer.squareup.com/forums)
- [Square Support](https://squareup.com/help)

## 🔧 Quick Setup Commands

```bash
# 1. Generate production secrets
npm run generate:secrets

# 2. Update .env.production with Square credentials
# (Manual step - update the file with your production credentials)

# 3. Validate configuration
npm run setup:services

# 4. Test payment processing
npm run test:payments  # If you have this script
```

## ⚠️ Final Reminders

1. **NEVER use sandbox credentials in production**
2. **Test thoroughly before going live**
3. **Monitor transactions closely after deployment**
4. **Keep credentials secure and rotate regularly**
5. **Set up proper error handling and logging**
