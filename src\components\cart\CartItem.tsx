import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch } from '../../hooks/redux';
import { updateCartItem, removeFromCart } from '../../store/slices/cartSlice';
import { addToast } from '../../store/slices/uiSlice';
import { CartItem as CartItemType } from '../../types';
import { TrashIcon, MinusIcon, PlusIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../common/LoadingSpinner';

interface CartItemProps {
  item: CartItemType;
  itemIndex: number;
  loading?: boolean;
}

const CartItem: React.FC<CartItemProps> = ({ item, itemIndex, loading = false }) => {
  const dispatch = useAppDispatch();
  const [isUpdating, setIsUpdating] = useState(false);

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setIsUpdating(true);
    try {
      await dispatch(updateCartItem({ 
        itemId: itemIndex.toString(), 
        quantity: newQuantity 
      })).unwrap();
      
      dispatch(addToast({
        type: 'success',
        title: 'Cart Updated',
        message: 'Item quantity updated successfully'
      }));
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Update Failed',
        message: error as string || 'Failed to update item quantity'
      }));
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveItem = async () => {
    if (window.confirm('Are you sure you want to remove this item from your cart?')) {
      setIsUpdating(true);
      try {
        await dispatch(removeFromCart(itemIndex.toString())).unwrap();
        
        dispatch(addToast({
          type: 'success',
          title: 'Item Removed',
          message: 'Item removed from cart successfully'
        }));
      } catch (error) {
        dispatch(addToast({
          type: 'error',
          title: 'Remove Failed',
          message: error as string || 'Failed to remove item from cart'
        }));
      } finally {
        setIsUpdating(false);
      }
    }
  };

  const itemTotal = item.price * item.quantity;

  return (
    <div className={`flex items-center space-x-4 p-4 border-b border-gray-200 ${loading || isUpdating ? 'opacity-50' : ''}`}>
      {/* Product Image */}
      <div className="flex-shrink-0 w-20 h-20">
        <img
          src={typeof item.product?.images?.[0] === 'string' ? item.product.images[0] : item.product?.images?.[0]?.url || '/images/placeholder-product.jpg'}
          alt={item.product?.name || 'Product'}
          className="w-full h-full object-cover rounded-lg"
        />
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              <Link 
                to={`/products/${item.product?.slug}`}
                className="hover:text-primary-600 transition-colors"
              >
                {item.product?.name || 'Product'}
              </Link>
            </h3>
            
            {item.variant && (
              <p className="text-sm text-gray-600 mt-1">
                {item.variant.name}: {item.variant.value}
              </p>
            )}
            
            <p className="text-lg font-semibold text-primary-600 mt-2">
              ${item.price.toFixed(2)}
            </p>
          </div>

          {/* Remove Button */}
          <button
            onClick={handleRemoveItem}
            disabled={loading || isUpdating}
            className="text-red-500 hover:text-red-700 transition-colors p-1"
            title="Remove item"
          >
            {isUpdating ? (
              <LoadingSpinner size="small" />
            ) : (
              <TrashIcon className="h-5 w-5" />
            )}
          </button>
        </div>

        {/* Quantity Controls and Total */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Quantity:</span>
            <div className="flex items-center border border-gray-300 rounded-lg">
              <button
                onClick={() => handleQuantityChange(item.quantity - 1)}
                disabled={item.quantity <= 1 || loading || isUpdating}
                className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <MinusIcon className="h-4 w-4" />
              </button>
              
              <span className="px-4 py-2 text-center min-w-[3rem] border-x border-gray-300">
                {item.quantity}
              </span>
              
              <button
                onClick={() => handleQuantityChange(item.quantity + 1)}
                disabled={loading || isUpdating}
                className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <PlusIcon className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="text-right">
            <p className="text-lg font-semibold text-gray-900">
              ${itemTotal.toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartItem;
