import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminPageHeader from '../../components/admin/AdminPageHeader';
import AdminDataTable, { TableColumn, TableAction } from '../../components/admin/AdminDataTable';
import {
  ShoppingBagIcon,
  EyeIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';

interface Order {
  id: string;
  orderNumber: string;
  customer: {
    name: string;
    email: string;
    phone?: string;
    membershipType?: 'first-time' | 'regular' | 'premium';
    gender?: 'he' | 'she' | 'they';
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  // Enhanced order data
  shippingMethod: 'regular' | 'regular-free' | 'express';
  shippingCost: number;
  trafficSource?: 'organic' | 'direct' | 'social-media' | 'referral' | 'paid';
  referralSource?: string;
  isFirstOrder: boolean;
  customerLifetimeValue: number;
  orderSource: 'web' | 'mobile' | 'phone' | 'in-store';
  createdAt: string;
  updatedAt: string;
  trackingNumber?: string;
  trackingUrl?: string;
  estimatedDeliveryDate?: string;
}

interface OrderFilters {
  search: string;
  status: string;
  paymentStatus: string;
  membershipType: string;
  trafficSource: string;
  shippingMethod: string;
  isFirstOrder: string;
  dateFrom: string;
  dateTo: string;
  amountRange: string;
}

const OrderManagement: React.FC = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<OrderFilters>({
    search: '',
    status: 'all',
    paymentStatus: 'all',
    membershipType: 'all',
    trafficSource: 'all',
    shippingMethod: 'all',
    isFirstOrder: 'all',
    dateFrom: '',
    dateTo: '',
    amountRange: 'all'
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // Sample data
  useEffect(() => {
    setOrders([
      {
        id: '1',
        orderNumber: 'ORD-001',
        customer: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+****************',
          membershipType: 'regular',
          gender: 'he'
        },
        items: [
          { name: 'Organic Turmeric Powder', quantity: 2, price: 19.99 },
          { name: 'Raw Honey', quantity: 1, price: 29.99 }
        ],
        total: 69.97,
        status: 'pending',
        paymentStatus: 'paid',
        shippingAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        // Enhanced order data
        shippingMethod: 'regular',
        shippingCost: 9.99,
        trafficSource: 'organic',
        referralSource: 'google.com',
        isFirstOrder: false,
        customerLifetimeValue: 189.95,
        orderSource: 'web',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        trackingNumber: 'TRK123456789',
        trackingUrl: 'https://usps.com/track/TRK123456789',
        estimatedDeliveryDate: '2024-01-20T00:00:00Z'
      },
      {
        id: '2',
        orderNumber: 'ORD-002',
        customer: {
          name: 'Jane Smith',
          email: '<EMAIL>'
        },
        items: [
          { name: 'Coconut Oil', quantity: 1, price: 24.99 }
        ],
        total: 24.99,
        status: 'processing',
        paymentStatus: 'paid',
        shippingAddress: {
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'USA'
        },
        createdAt: '2024-01-14T09:15:00Z',
        updatedAt: '2024-01-14T11:20:00Z'
      },
      {
        id: '3',
        orderNumber: 'ORD-003',
        customer: {
          name: 'Mike Johnson',
          email: '<EMAIL>'
        },
        items: [
          { name: 'Quinoa Seeds', quantity: 3, price: 15.99 }
        ],
        total: 47.97,
        status: 'shipped',
        paymentStatus: 'paid',
        shippingAddress: {
          street: '789 Pine St',
          city: 'Chicago',
          state: 'IL',
          zipCode: '60601',
          country: 'USA'
        },
        createdAt: '2024-01-13T16:45:00Z',
        updatedAt: '2024-01-14T08:30:00Z',
        trackingNumber: 'TRK123456789'
      },
      {
        id: '4',
        orderNumber: 'ORD-004',
        customer: {
          name: 'Sarah Wilson',
          email: '<EMAIL>'
        },
        items: [
          { name: 'Himalayan Pink Salt', quantity: 2, price: 12.99 }
        ],
        total: 25.98,
        status: 'delivered',
        paymentStatus: 'paid',
        shippingAddress: {
          street: '321 Elm St',
          city: 'Miami',
          state: 'FL',
          zipCode: '33101',
          country: 'USA'
        },
        createdAt: '2024-01-12T14:20:00Z',
        updatedAt: '2024-01-13T16:00:00Z',
        trackingNumber: 'TRK987654321'
      }
    ]);
    setPagination(prev => ({ ...prev, total: 4 }));
  }, []);

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    // Implement search logic here
  };

  const handleFilterChange = (key: keyof OrderFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    // Implement filter logic here
  };

  const handleBulkAction = (action: string, orderIds: string[]) => {
    console.log(`Bulk ${action} for orders:`, orderIds);
    // Implement bulk actions here
    setSelectedOrders([]);
  };

  const handleOrderAction = (action: string, order: Order) => {
    switch (action) {
      case 'view':
        navigate(`/admin/orders/${order.id}`);
        break;
      case 'process':
        console.log('Process order:', order.id);
        // Implement process logic here
        break;
      case 'ship':
        console.log('Ship order:', order.id);
        // Implement ship logic here
        break;
      case 'cancel':
        if (window.confirm('Are you sure you want to cancel this order?')) {
          console.log('Cancel order:', order.id);
          // Implement cancel logic here
        }
        break;
      case 'refund':
        if (window.confirm('Are you sure you want to refund this order?')) {
          console.log('Refund order:', order.id);
          // Implement refund logic here
        }
        break;
      case 'print':
        console.log('Print order:', order.id);
        // Implement print logic here
        break;
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return ClockIcon;
      case 'processing':
        return ExclamationTriangleIcon;
      case 'shipped':
        return TruckIcon;
      case 'delivered':
        return CheckCircleIcon;
      case 'cancelled':
      case 'refunded':
        return XCircleIcon;
      default:
        return ClockIcon;
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: Order['paymentStatus']) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns: TableColumn<Order>[] = [
    {
      key: 'orderNumber',
      title: 'Order',
      render: (value, record) => (
        <div>
          <Link
            to={`/admin/orders/${record.id}`}
            className="text-indigo-600 hover:text-indigo-900 font-medium"
          >
            {value}
          </Link>
          <div className="text-xs text-gray-500">
            {new Date(record.createdAt).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'Customer',
      render: (value, record) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{value.name}</div>
          <div className="text-sm text-gray-500">{value.email}</div>
          <div className="flex items-center space-x-2 mt-1">
            {value.membershipType && (
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                value.membershipType === 'premium'
                  ? 'bg-purple-100 text-purple-800'
                  : value.membershipType === 'regular'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {value.membershipType}
              </span>
            )}
            {record.isFirstOrder && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                First Order
              </span>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'items',
      title: 'Items',
      render: (value) => (
        <div>
          <div className="text-sm text-gray-900">
            {value.length} item{value.length !== 1 ? 's' : ''}
          </div>
          <div className="text-xs text-gray-500">
            {value.slice(0, 2).map(item => item.name).join(', ')}
            {value.length > 2 && ` +${value.length - 2} more`}
          </div>
        </div>
      )
    },
    {
      key: 'total',
      title: 'Total',
      align: 'right',
      render: (value) => (
        <span className="text-sm font-medium text-gray-900">
          ${value.toFixed(2)}
        </span>
      )
    },
    {
      key: 'status',
      title: 'Order Status',
      render: (value) => {
        const StatusIcon = getStatusIcon(value);
        return (
          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(value)}`}>
            <StatusIcon className="h-3 w-3 mr-1" />
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </span>
        );
      }
    },
    {
      key: 'paymentStatus',
      title: 'Payment',
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(value)}`}>
          {value.charAt(0).toUpperCase() + value.slice(1)}
        </span>
      )
    },
    {
      key: 'shippingMethod',
      title: 'Shipping & Source',
      render: (value, record) => (
        <div>
          <div className="text-xs text-gray-900">
            {value === 'regular-free' ? 'Free Shipping' :
             value === 'express' ? 'Express' : 'Standard'}
            {record.shippingCost > 0 && ` ($${record.shippingCost.toFixed(2)})`}
          </div>
          {record.trafficSource && (
            <div className="text-xs text-gray-500 mt-1">
              <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                record.trafficSource === 'organic' ? 'bg-green-100 text-green-700' :
                record.trafficSource === 'paid' ? 'bg-red-100 text-red-700' :
                record.trafficSource === 'social-media' ? 'bg-blue-100 text-blue-700' :
                record.trafficSource === 'referral' ? 'bg-yellow-100 text-yellow-700' :
                'bg-gray-100 text-gray-700'
              }`}>
                {record.trafficSource}
              </span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'trackingNumber',
      title: 'Tracking',
      render: (value, record) => (
        value ? (
          record.trackingUrl ? (
            <a
              href={record.trackingUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs font-mono text-indigo-600 hover:text-indigo-900"
            >
              {value}
            </a>
          ) : (
            <span className="text-xs font-mono text-gray-600">{value}</span>
          )
        ) : (
          <span className="text-xs text-gray-400">-</span>
        )
      )
    }
  ];

  const actions: TableAction<Order>[] = [
    {
      label: 'View Details',
      onClick: (order) => handleOrderAction('view', order),
      icon: EyeIcon
    },
    {
      label: 'Process Order',
      onClick: (order) => handleOrderAction('process', order),
      disabled: (order) => order.status !== 'pending'
    },
    {
      label: 'Mark as Shipped',
      onClick: (order) => handleOrderAction('ship', order),
      icon: TruckIcon,
      disabled: (order) => !['pending', 'processing'].includes(order.status)
    },
    {
      label: 'Print Order',
      onClick: (order) => handleOrderAction('print', order),
      icon: PrinterIcon
    },
    {
      label: 'Cancel Order',
      onClick: (order) => handleOrderAction('cancel', order),
      variant: 'danger',
      disabled: (order) => ['delivered', 'cancelled', 'refunded'].includes(order.status)
    },
    {
      label: 'Refund Order',
      onClick: (order) => handleOrderAction('refund', order),
      variant: 'danger',
      disabled: (order) => order.paymentStatus !== 'paid' || ['cancelled', 'refunded'].includes(order.status)
    }
  ];

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Order Management"
        subtitle="Manage customer orders, fulfillment, and shipping"
        stats={[
          { label: 'Total Orders', value: '2,456' },
          { label: 'Pending Orders', value: '23', change: { value: 5, type: 'increase' } },
          { label: 'Processing', value: '18', change: { value: 2, type: 'increase' } },
          { label: 'Shipped Today', value: '12' }
        ]}
        actions={[
          {
            label: 'Export Orders',
            onClick: () => console.log('Export orders'),
            variant: 'secondary',
            icon: ArrowDownTrayIcon
          }
        ]}
      />

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-indigo-600 hover:text-indigo-900 flex items-center"
            >
              <FunnelIcon className="h-4 w-4 mr-1" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </button>
          </div>
        </div>

        {showFilters && (
          <div className="px-6 py-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search orders..."
                    value={filters.search}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Order Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Status
                </label>
                <select
                  value={filters.paymentStatus}
                  onChange={(e) => handleFilterChange('paymentStatus', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Payments</option>
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Membership Type
                </label>
                <select
                  value={filters.membershipType}
                  onChange={(e) => handleFilterChange('membershipType', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Members</option>
                  <option value="first-time">First-time</option>
                  <option value="regular">Regular</option>
                  <option value="premium">Premium</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Traffic Source
                </label>
                <select
                  value={filters.trafficSource}
                  onChange={(e) => handleFilterChange('trafficSource', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Sources</option>
                  <option value="organic">Organic Search</option>
                  <option value="direct">Direct</option>
                  <option value="social-media">Social Media</option>
                  <option value="referral">Referral</option>
                  <option value="paid">Paid Ads</option>
                </select>
              </div>
            </div>

            {/* Second row of filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Shipping Method
                </label>
                <select
                  value={filters.shippingMethod}
                  onChange={(e) => handleFilterChange('shippingMethod', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Methods</option>
                  <option value="regular">Standard</option>
                  <option value="regular-free">Free Shipping</option>
                  <option value="express">Express</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Type
                </label>
                <select
                  value={filters.isFirstOrder}
                  onChange={(e) => handleFilterChange('isFirstOrder', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Customers</option>
                  <option value="true">First Order</option>
                  <option value="false">Returning</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount Range
                </label>
                <select
                  value={filters.amountRange}
                  onChange={(e) => handleFilterChange('amountRange', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Amounts</option>
                  <option value="0-50">$0 - $50</option>
                  <option value="50-100">$50 - $100</option>
                  <option value="100-200">$100 - $200</option>
                  <option value="200+">$200+</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date Range
                </label>
                <div className="flex space-x-2">
                  <input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Orders Table */}
      <AdminDataTable
        columns={columns}
        data={orders}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(page, pageSize) => setPagination(prev => ({ ...prev, current: page, pageSize }))}
        actions={actions}
        bulkActions={{
          actions: [
            {
              label: 'Mark as Processing',
              onClick: (ids) => handleBulkAction('process', ids),
              variant: 'primary'
            },
            {
              label: 'Mark as Shipped',
              onClick: (ids) => handleBulkAction('ship', ids),
              variant: 'secondary'
            },
            {
              label: 'Export Selected',
              onClick: (ids) => handleBulkAction('export', ids)
            }
          ],
          getRowId: (order) => order.id
        }}
        emptyText="No orders found"
      />
    </AdminLayout>
  );
};

export default OrderManagement;
